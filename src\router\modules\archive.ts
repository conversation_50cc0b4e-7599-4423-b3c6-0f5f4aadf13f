import type { RouteRecordRaw } from 'vue-router';

// 档案模块路由
export const archiveRoutes: RouteRecordRaw[] = [
  {
    path: '/archive',
    name: 'Archive',
    component: () => import('@/views/archive/Archive.vue'),
    meta: {
      title: '档案 - 家庭健康管理系统',
      icon: 'User',
      order: 2
    }
  },
  {
    path: '/archive/personal-info',
    name: 'PersonalInfo',
    component: () => import('@/views/archive/PersonalInfo.vue'),
    meta: {
      title: '个人信息 - 家庭健康管理系统',
      parent: 'Archive',
      breadcrumb: [
        { title: '档案', path: '/archive' },
        { title: '个人信息' }
      ]
    }
  },
  {
    path: '/archive/health-center',
    name: 'HealthCenter',
    component: () => import('@/views/archive/HealthCenter.vue'),
    meta: {
      title: '健康档案中心 - 家庭健康管理系统',
      parent: 'Archive',
      breadcrumb: [
        { title: '档案', path: '/archive' },
        { title: '健康档案中心' }
      ]
    }
  },
  {
    path: '/archive/medicine-box',
    name: 'MedicineBox',
    component: () => import('@/views/archive/MedicineBox.vue'),
    meta: {
      title: '个人药箱 - 家庭健康管理系统',
      parent: 'Archive',
      breadcrumb: [
        { title: '档案', path: '/archive' },
        { title: '个人药箱' }
      ]
    }
  },
  {
    path: '/archive/health-overview',
    name: 'HealthOverview',
    component: () => import('@/views/archive/HealthOverview.vue'),
    meta: {
      title: '综合健康概要 - 家庭健康管理系统',
      parent: 'Archive',
      breadcrumb: [
        { title: '档案', path: '/archive' },
        { title: '综合健康概要' }
      ]
    }
  },
  {
    path: '/archive/emergency-card',
    name: 'EmergencyCard',
    component: () => import('@/views/archive/EmergencyCard.vue'),
    meta: {
      title: '紧急资料卡 - 家庭健康管理系统',
      parent: 'Archive',
      breadcrumb: [
        { title: '档案', path: '/archive' },
        { title: '紧急资料卡' }
      ]
    }
  }
];
