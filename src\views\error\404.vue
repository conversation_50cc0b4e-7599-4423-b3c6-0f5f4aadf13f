<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
          </svg>
        </div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">页面不存在</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
          请检查网址是否正确，或返回首页继续浏览。
        </p>
        
        <div class="error-actions">
          <button class="btn btn-primary" @click="goHome">
            返回首页
          </button>
          <button class="btn btn-outline" @click="goBack">
            返回上页
          </button>
        </div>
        
        <div class="error-suggestions">
          <h3 class="suggestions-title">您可以尝试：</h3>
          <ul class="suggestions-list">
            <li>检查网址拼写是否正确</li>
            <li>使用导航菜单浏览其他页面</li>
            <li>联系我们获取帮助</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/dashboard');
};

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-4);
}

.error-container {
  max-width: 600px;
  text-align: center;
}

.error-illustration {
  margin-bottom: var(--spacing-8);
}

.error-code {
  font-size: 120px;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--spacing-4);
}

.error-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  color: var(--color-gray-400);
}

.error-content {
  background-color: var(--theme-bg-primary);
  padding: var(--spacing-8);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.error-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.error-description {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-6) 0;
}

.error-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  margin-bottom: var(--spacing-6);
}

.error-suggestions {
  text-align: left;
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
}

.suggestions-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-2);
  position: relative;
  padding-left: var(--spacing-4);
}

.suggestions-list li::before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }
  
  .error-icon {
    width: 60px;
    height: 60px;
  }
  
  .error-content {
    padding: var(--spacing-6);
  }
  
  .error-actions {
    flex-direction: column;
  }
}
</style>
