<template>
  <div class="onboarding-page">
    <div class="onboarding-container">
      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${((currentSlide + 1) / slides.length) * 100}%` }"
          ></div>
        </div>
        <span class="progress-text">{{ currentSlide + 1 }} / {{ slides.length }}</span>
      </div>

      <!-- 幻灯片内容 -->
      <div class="slides-container">
        <transition name="slide" mode="out-in">
          <div :key="currentSlide" class="slide">
            <div class="slide-illustration">
              <div class="illustration-icon" :style="{ backgroundColor: slides[currentSlide].color }">
                <span class="icon">{{ slides[currentSlide].icon }}</span>
              </div>
            </div>
            
            <div class="slide-content">
              <h1 class="slide-title">{{ slides[currentSlide].title }}</h1>
              <p class="slide-description">{{ slides[currentSlide].description }}</p>
              
              <div class="slide-features">
                <div 
                  v-for="(feature, index) in slides[currentSlide].features" 
                  :key="index"
                  class="feature-item"
                >
                  <div class="feature-icon">{{ feature.icon }}</div>
                  <span class="feature-text">{{ feature.text }}</span>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- 导航控制 -->
      <div class="navigation-controls">
        <!-- 幻灯片指示器 -->
        <div class="slide-indicators">
          <button
            v-for="(slide, index) in slides"
            :key="index"
            class="indicator"
            :class="{ active: index === currentSlide }"
            @click="goToSlide(index)"
          ></button>
        </div>

        <!-- 导航按钮 -->
        <div class="navigation-buttons">
          <button
            v-if="currentSlide > 0"
            class="nav-btn prev-btn"
            @click="previousSlide"
          >
            上一步
          </button>
          
          <button
            v-if="currentSlide < slides.length - 1"
            class="nav-btn next-btn"
            @click="nextSlide"
          >
            下一步
          </button>
          
          <button
            v-if="currentSlide === slides.length - 1"
            class="nav-btn start-btn"
            @click="startJourney"
          >
            开始体验
          </button>
        </div>
      </div>

      <!-- 跳过按钮 -->
      <button class="skip-btn" @click="skipOnboarding">
        跳过引导
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 当前幻灯片索引
const currentSlide = ref(0);

// 幻灯片数据
const slides = ref([
  {
    title: '家人健康，一手掌握',
    description: '统一管理家庭成员的健康信息，让关爱更贴心，让健康更透明。',
    icon: '🏠',
    color: '#667eea',
    features: [
      { icon: '👨‍👩‍👧‍👦', text: '家庭成员统一管理' },
      { icon: '📊', text: '健康数据可视化' },
      { icon: '🔔', text: '智能健康提醒' }
    ]
  },
  {
    title: 'AI智能分析，个性化建议',
    description: '基于大数据和AI算法，为每位家庭成员提供个性化的健康分析和建议。',
    icon: '🤖',
    color: '#764ba2',
    features: [
      { icon: '🧠', text: 'AI健康分析' },
      { icon: '📈', text: '趋势预测' },
      { icon: '💡', text: '个性化建议' }
    ]
  },
  {
    title: '紧急情况，一键通知',
    description: '智能监测异常情况，紧急时刻自动通知家人和医生，守护生命安全。',
    icon: '🚨',
    color: '#f093fb',
    features: [
      { icon: '⚡', text: '实时监测' },
      { icon: '📞', text: '一键求助' },
      { icon: '🏥', text: '医疗联动' }
    ]
  },
  {
    title: '智能设备，无感同步',
    description: '连接各种智能健康设备，自动同步数据，让健康管理更轻松便捷。',
    icon: '📱',
    color: '#f093fb',
    features: [
      { icon: '⌚', text: '智能穿戴设备' },
      { icon: '🩺', text: '医疗检测设备' },
      { icon: '🔄', text: '自动数据同步' }
    ]
  }
]);

// 切换到指定幻灯片
const goToSlide = (index: number) => {
  currentSlide.value = index;
};

// 下一张幻灯片
const nextSlide = () => {
  if (currentSlide.value < slides.value.length - 1) {
    currentSlide.value++;
  }
};

// 上一张幻灯片
const previousSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  }
};

// 开始体验
const startJourney = () => {
  router.push('/family-setup');
};

// 跳过引导
const skipOnboarding = () => {
  router.push('/family-setup');
};
</script>

<style scoped>
.onboarding-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-4);
}

.onboarding-container {
  width: 100%;
  max-width: 800px;
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-12);
  position: relative;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  font-weight: var(--font-weight-medium);
  min-width: 60px;
  text-align: right;
}

.slides-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-8);
}

.slide {
  text-align: center;
  width: 100%;
}

.slide-illustration {
  margin-bottom: var(--spacing-8);
}

.illustration-icon {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: var(--shadow-lg);
}

.icon {
  font-size: 60px;
}

.slide-content {
  max-width: 600px;
  margin: 0 auto;
}

.slide-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-tight);
}

.slide-description {
  font-size: var(--font-size-lg);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-8) 0;
  line-height: var(--line-height-relaxed);
}

.slide-features {
  display: flex;
  justify-content: center;
  gap: var(--spacing-8);
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  min-width: 120px;
}

.feature-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-1);
}

.feature-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  text-align: center;
  line-height: var(--line-height-normal);
}

.navigation-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-6);
}

.slide-indicators {
  display: flex;
  gap: var(--spacing-2);
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-gray-300);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.indicator.active {
  background-color: var(--color-primary);
}

.navigation-buttons {
  display: flex;
  gap: var(--spacing-4);
}

.nav-btn {
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.prev-btn {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border);
}

.prev-btn:hover {
  background-color: var(--color-gray-100);
}

.next-btn,
.start-btn {
  background-color: var(--color-primary);
  color: white;
}

.next-btn:hover,
.start-btn:hover {
  background-color: var(--color-primary-dark);
}

.start-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

.skip-btn {
  position: absolute;
  top: var(--spacing-6);
  right: var(--spacing-6);
  background: none;
  border: none;
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.skip-btn:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

/* 幻灯片切换动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .onboarding-container {
    padding: var(--spacing-8);
  }
  
  .slide-title {
    font-size: var(--font-size-2xl);
  }
  
  .slide-description {
    font-size: var(--font-size-base);
  }
  
  .illustration-icon {
    width: 100px;
    height: 100px;
  }
  
  .icon {
    font-size: 50px;
  }
  
  .slide-features {
    gap: var(--spacing-4);
  }
  
  .feature-item {
    min-width: 100px;
  }
  
  .navigation-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .nav-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .onboarding-container {
    padding: var(--spacing-6);
  }
  
  .slide-features {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .feature-item {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    min-width: auto;
  }
  
  .feature-icon {
    margin-bottom: 0;
  }
}
</style>
