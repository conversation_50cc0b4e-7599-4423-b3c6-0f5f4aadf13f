<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div v-if="showHeader" class="page-header">
      <div class="page-header-content">
        <!-- 面包屑导航 -->
        <div v-if="breadcrumb && breadcrumb.length > 0" class="breadcrumb">
          <span
            v-for="(item, index) in breadcrumb"
            :key="index"
            class="breadcrumb-item"
          >
            <router-link
              v-if="item.path && index < breadcrumb.length - 1"
              :to="item.path"
              class="breadcrumb-link"
            >
              {{ item.title }}
            </router-link>
            <span v-else class="breadcrumb-current">{{ item.title }}</span>
            <span
              v-if="index < breadcrumb.length - 1"
              class="breadcrumb-separator"
              >/</span
            >
          </span>
        </div>

        <!-- 页面标题和描述 -->
        <div class="page-title-section">
          <div class="page-title-wrapper">
            <h1 v-if="title" class="page-title">{{ title }}</h1>
            <p v-if="description" class="page-description">{{ description }}</p>
          </div>

          <!-- 页面操作按钮 -->
          <div v-if="$slots.actions" class="page-actions">
            <slot name="actions" />
          </div>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content" :class="{ 'no-header': !showHeader }">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

interface BreadcrumbItem {
  title: string;
  path?: string;
}

interface Props {
  title?: string;
  description?: string;
  showHeader?: boolean;
  breadcrumb?: BreadcrumbItem[];
  autoBreadcrumb?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  autoBreadcrumb: true,
});

const route = useRoute();

// 自动生成面包屑
const computedBreadcrumb = computed(() => {
  if (props.breadcrumb) {
    return props.breadcrumb;
  }

  if (props.autoBreadcrumb && route.meta?.breadcrumb) {
    return route.meta.breadcrumb as BreadcrumbItem[];
  }

  return [];
});

const breadcrumb = computed(() => computedBreadcrumb.value);
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  display: flex;
  flex-direction: column;
  background-color: var(--theme-bg-secondary);
}

.page-header {
  background-color: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border);
  padding: var(--spacing-6) var(--spacing-8);
}

.page-header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.breadcrumb-link {
  color: var(--theme-text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.breadcrumb-link:hover {
  color: var(--color-primary);
}

.breadcrumb-current {
  color: var(--theme-text-primary);
  font-weight: var(--font-weight-medium);
}

.breadcrumb-separator {
  color: var(--color-gray-400);
  margin: 0 var(--spacing-1);
}

.page-title-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-6);
}

.page-title-wrapper {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-tight);
}

.page-description {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  padding: var(--spacing-6) 0;
  overflow-y: auto;
  width: 100%;
}

.page-content.no-header {
  padding-top: var(--spacing-6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-4) var(--spacing-4);
  }

  .page-title-section {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-4);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .page-content {
    padding: var(--spacing-4);
  }

  .breadcrumb {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-3) var(--spacing-3);
  }

  .page-title {
    font-size: var(--font-size-lg);
  }

  .page-content {
    padding: var(--spacing-3);
  }
}
</style>
