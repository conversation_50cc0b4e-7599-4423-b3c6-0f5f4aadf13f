<template>
  <el-dialog
    v-model="visible"
    title="健康报告详情"
    width="1200px"
    :before-close="handleClose"
  >
    <div v-if="report" class="report-detail-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="basic-info">
            <div class="report-header">
              <div class="report-meta">
                <h3 class="report-title">{{ report.name }}</h3>
                <div class="report-tags">
                  <el-tag :type="getTypeColor(report.type)" size="small">
                    {{ getTypeName(report.type) }}
                  </el-tag>
                  <el-tag :type="getStatusColor(report.status)" size="small">
                    {{ getStatusName(report.status) }}
                  </el-tag>
                  <el-tag v-if="report.needsReview" type="warning" size="small">
                    需要审核
                  </el-tag>
                </div>
              </div>
              <div class="report-actions">
                <el-button @click="downloadReport">
                  <el-icon><Download /></el-icon>
                  下载原文件
                </el-button>
                <el-button type="primary" @click="editMode = !editMode">
                  <el-icon><Edit /></el-icon>
                  {{ editMode ? '取消编辑' : '编辑数据' }}
                </el-button>
              </div>
            </div>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="成员姓名">
                {{ report.memberName }}
              </el-descriptions-item>
              <el-descriptions-item label="报告日期">
                {{ formatDate(report.reportDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="上传时间">
                {{ formatDateTime(report.uploadDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="文件大小">
                {{ report.fileSize }}
              </el-descriptions-item>
              <el-descriptions-item label="页数">
                {{ report.pageCount }} 页
              </el-descriptions-item>
              <el-descriptions-item label="识别准确度">
                <div class="confidence-info">
                  <span>{{ report.confidence }}%</span>
                  <el-progress
                    :percentage="report.confidence"
                    :stroke-width="6"
                    :show-text="false"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 提取数据 -->
        <el-tab-pane label="提取数据" name="extracted">
          <div v-if="report.extractedData" class="extracted-data">
            <!-- 患者基本信息 -->
            <div class="data-section">
              <h4 class="section-title">患者基本信息</h4>
              <el-form
                :model="editableData.basicInfo"
                label-width="100px"
                :disabled="!editMode"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="姓名">
                      <el-input v-model="editableData.basicInfo.patientName" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="患者ID">
                      <el-input v-model="editableData.basicInfo.patientId" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="年龄">
                      <el-input-number
                        v-model="editableData.basicInfo.age"
                        :min="0"
                        :max="150"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="性别">
                      <el-select v-model="editableData.basicInfo.gender" style="width: 100%">
                        <el-option label="男" value="男" />
                        <el-option label="女" value="女" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="医院名称">
                      <el-input v-model="editableData.basicInfo.hospitalName" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="医生姓名">
                      <el-input v-model="editableData.basicInfo.doctorName" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 生命体征 -->
            <div v-if="editableData.vitalSigns" class="data-section">
              <h4 class="section-title">生命体征</h4>
              <el-form
                :model="editableData.vitalSigns"
                label-width="100px"
                :disabled="!editMode"
              >
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="身高(cm)">
                      <el-input-number
                        v-model="editableData.vitalSigns.height"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="体重(kg)">
                      <el-input-number
                        v-model="editableData.vitalSigns.weight"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="BMI">
                      <el-input-number
                        v-model="editableData.vitalSigns.bmi"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="血压">
                      <el-input v-model="editableData.vitalSigns.bloodPressure" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="心率(bpm)">
                      <el-input-number
                        v-model="editableData.vitalSigns.heartRate"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="体温(°C)">
                      <el-input-number
                        v-model="editableData.vitalSigns.temperature"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 检验结果 -->
            <div v-if="editableData.labResults" class="data-section">
              <h4 class="section-title">
                检验结果
                <el-button
                  v-if="editMode"
                  type="primary"
                  size="small"
                  @click="addLabResult"
                >
                  添加项目
                </el-button>
              </h4>
              <el-table :data="editableData.labResults" style="width: 100%">
                <el-table-column label="检验项目" width="150">
                  <template #default="{ row, $index }">
                    <el-input
                      v-if="editMode"
                      v-model="row.itemName"
                      size="small"
                    />
                    <span v-else>{{ row.itemName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="结果" width="100">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.value"
                      size="small"
                    />
                    <span v-else>{{ row.value }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="单位" width="80">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.unit"
                      size="small"
                    />
                    <span v-else>{{ row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="参考范围" width="120">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.referenceRange"
                      size="small"
                    />
                    <span v-else>{{ row.referenceRange }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-select
                      v-if="editMode"
                      v-model="row.status"
                      size="small"
                      style="width: 100%"
                    >
                      <el-option label="正常" value="normal" />
                      <el-option label="偏高" value="high" />
                      <el-option label="偏低" value="low" />
                      <el-option label="异常" value="abnormal" />
                    </el-select>
                    <el-tag
                      v-else
                      :type="getLabStatusColor(row.status)"
                      size="small"
                    >
                      {{ getLabStatusName(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column v-if="editMode" label="操作" width="80">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeLabResult($index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 诊断和建议 -->
            <div class="data-section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4 class="section-title">诊断结果</h4>
                  <el-input
                    v-if="editMode"
                    v-model="diagnosisText"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入诊断结果，每行一个"
                  />
                  <div v-else class="text-content">
                    <div
                      v-for="(diagnosis, index) in editableData.diagnosis"
                      :key="index"
                      class="text-item"
                    >
                      {{ diagnosis }}
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <h4 class="section-title">医生建议</h4>
                  <el-input
                    v-if="editMode"
                    v-model="recommendationsText"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入医生建议，每行一个"
                  />
                  <div v-else class="text-content">
                    <div
                      v-for="(recommendation, index) in editableData.recommendations"
                      :key="index"
                      class="text-item"
                    >
                      {{ recommendation }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 用药信息 -->
            <div v-if="editableData.medications" class="data-section">
              <h4 class="section-title">
                用药信息
                <el-button
                  v-if="editMode"
                  type="primary"
                  size="small"
                  @click="addMedication"
                >
                  添加药物
                </el-button>
              </h4>
              <el-table :data="editableData.medications" style="width: 100%">
                <el-table-column label="药物名称" width="150">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.name"
                      size="small"
                    />
                    <span v-else>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="剂量" width="100">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.dosage"
                      size="small"
                    />
                    <span v-else>{{ row.dosage }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="频次" width="100">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.frequency"
                      size="small"
                    />
                    <span v-else>{{ row.frequency }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="疗程" width="100">
                  <template #default="{ row }">
                    <el-input
                      v-if="editMode"
                      v-model="row.duration"
                      size="small"
                    />
                    <span v-else>{{ row.duration }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-if="editMode" label="操作" width="80">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeMedication($index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 其他信息 -->
            <div class="data-section">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="下次复诊">
                    <el-date-picker
                      v-if="editMode"
                      v-model="editableData.nextAppointment"
                      type="date"
                      placeholder="选择复诊日期"
                      style="width: 100%"
                    />
                    <span v-else>
                      {{ editableData.nextAppointment ? formatDate(editableData.nextAppointment) : '无' }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注">
                    <el-input
                      v-if="editMode"
                      v-model="editableData.notes"
                      type="textarea"
                      :rows="2"
                    />
                    <span v-else>{{ editableData.notes || '无' }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无提取数据" />
          </div>
        </el-tab-pane>

        <!-- 原文件预览 -->
        <el-tab-pane label="原文件预览" name="preview">
          <div class="file-preview">
            <div class="preview-toolbar">
              <el-button-group>
                <el-button @click="zoomOut">
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button @click="zoomIn">
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
                <el-button @click="resetZoom">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-button-group>
              <span class="zoom-info">{{ Math.round(zoomLevel * 100) }}%</span>
            </div>
            <div class="preview-content">
              <div
                class="preview-image"
                :style="{ transform: `scale(${zoomLevel})` }"
              >
                <img
                  v-if="report.thumbnailUrl"
                  :src="report.thumbnailUrl"
                  :alt="report.name"
                  class="preview-img"
                />
                <div v-else class="no-preview">
                  <el-icon><Document /></el-icon>
                  <span>无法预览此文件</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-else class="loading-content">
      <el-skeleton :rows="10" animated />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="editMode"
          type="primary"
          @click="saveChanges"
          :loading="saving"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Edit, ZoomOut, ZoomIn, Refresh, Document } from '@element-plus/icons-vue'
import { healthArchiveService, type HealthReport, type HealthReportData } from '@/services/healthArchiveService'

interface Props {
  modelValue: boolean
  reportId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const saving = ref(false)
const editMode = ref(false)
const activeTab = ref('basic')
const zoomLevel = ref(1)

const report = ref<HealthReport | null>(null)
const editableData = reactive<HealthReportData>({
  basicInfo: {},
  vitalSigns: {},
  labResults: [],
  diagnosis: [],
  recommendations: [],
  medications: [],
  nextAppointment: undefined,
  notes: ''
})

// 用于编辑的文本字段
const diagnosisText = ref('')
const recommendationsText = ref('')

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.reportId) {
    loadReportDetail()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetState()
  }
})

// 监听诊断和建议文本变化
watch(diagnosisText, (val) => {
  editableData.diagnosis = val.split('\n').filter(line => line.trim())
})

watch(recommendationsText, (val) => {
  editableData.recommendations = val.split('\n').filter(line => line.trim())
})

const resetState = () => {
  report.value = null
  editMode.value = false
  activeTab.value = 'basic'
  zoomLevel.value = 1
}

const loadReportDetail = async () => {
  loading.value = true
  try {
    const reportData = await healthArchiveService.getHealthReportDetail(props.reportId)
    report.value = reportData
    
    if (reportData.extractedData) {
      Object.assign(editableData, reportData.extractedData)
      diagnosisText.value = reportData.extractedData.diagnosis?.join('\n') || ''
      recommendationsText.value = reportData.extractedData.recommendations?.join('\n') || ''
    }
  } catch (error) {
    console.error('加载报告详情失败:', error)
    ElMessage.error('加载报告详情失败')
  } finally {
    loading.value = false
  }
}

const saveChanges = async () => {
  saving.value = true
  try {
    await healthArchiveService.updateHealthReportData(props.reportId, editableData)
    ElMessage.success('报告数据保存成功')
    editMode.value = false
    emit('updated')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const addLabResult = () => {
  editableData.labResults?.push({
    itemName: '',
    value: '',
    unit: '',
    referenceRange: '',
    status: 'normal'
  })
}

const removeLabResult = (index: number) => {
  editableData.labResults?.splice(index, 1)
}

const addMedication = () => {
  editableData.medications?.push({
    name: '',
    dosage: '',
    frequency: '',
    duration: ''
  })
}

const removeMedication = (index: number) => {
  editableData.medications?.splice(index, 1)
}

const downloadReport = () => {
  if (report.value) {
    ElMessage.success(`正在下载 ${report.value.name}`)
  }
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.5)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 辅助函数
const getTypeName = (type: string) => {
  const names: Record<string, string> = {
    checkup: '体检',
    lab: '化验',
    imaging: '影像',
    prescription: '处方',
    other: '其他'
  }
  return names[type] || type
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    checkup: 'primary',
    lab: 'success',
    imaging: 'warning',
    prescription: 'info',
    other: ''
  }
  return colors[type] || ''
}

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    manual_review: '需审核'
  }
  return names[status] || status
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    manual_review: 'info'
  }
  return colors[status] || ''
}

const getLabStatusName = (status: string) => {
  const names: Record<string, string> = {
    normal: '正常',
    high: '偏高',
    low: '偏低',
    abnormal: '异常'
  }
  return names[status] || status
}

const getLabStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    normal: 'success',
    high: 'warning',
    low: 'info',
    abnormal: 'danger'
  }
  return colors[status] || ''
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN').format(date)
}

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.report-detail-content {
  max-height: 700px;
  overflow-y: auto;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.report-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.report-tags {
  display: flex;
  gap: 8px;
}

.report-actions {
  display: flex;
  gap: 12px;
}

.confidence-info {
  display: flex;
  align-items: center;
}

.data-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light);
}

.text-content {
  min-height: 100px;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.text-item {
  margin-bottom: 8px;
  padding: 4px 0;
}

.text-item:last-child {
  margin-bottom: 0;
}

.file-preview {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  margin-bottom: 16px;
}

.zoom-info {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.preview-content {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  transition: transform 0.3s;
}

.preview-img {
  max-width: 100%;
  height: auto;
  display: block;
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--theme-text-secondary);
}

.no-preview .el-icon {
  font-size: 48px;
}

.no-data {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
