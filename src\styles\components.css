/* 组件样式 */

/* ===== 按钮组件 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--component-padding-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}

/* 按钮类型 */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
  border-color: var(--color-gray-200);
}

.btn-secondary:hover {
  background-color: var(--color-gray-300);
  border-color: var(--color-gray-300);
}

.btn-success {
  background-color: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.btn-success:hover {
  background-color: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.btn-warning:hover {
  background-color: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.btn-danger:hover {
  background-color: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
}

/* ===== 卡片组件 ===== */
.card {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--theme-border);
  background-color: var(--theme-bg-secondary);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--theme-border);
  background-color: var(--theme-bg-secondary);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-4);
}

/* ===== 表单组件 ===== */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
}

.form-input {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: border-color var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input:disabled {
  background-color: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

.form-error {
  color: var(--color-danger);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.form-help {
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

/* ===== 徽章组件 ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.badge-success {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.badge-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.badge-danger {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.badge-info {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

/* ===== 分割线组件 ===== */
.divider {
  border: none;
  height: 1px;
  background-color: var(--theme-border);
  margin: var(--spacing-4) 0;
}

.divider-vertical {
  width: 1px;
  height: auto;
  background-color: var(--theme-border);
  margin: 0 var(--spacing-4);
}

/* ===== 加载组件 ===== */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: var(--spacing-1);
}

.loading-dot {
  width: 6px;
  height: 6px;
  background-color: var(--color-primary);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

/* ===== 提示组件 ===== */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-gray-900);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal);
  z-index: 1000;
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

/* ===== 面包屑组件 ===== */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--color-gray-400);
}

.breadcrumb-link {
  color: var(--theme-text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.breadcrumb-link:hover {
  color: var(--color-primary);
}

.breadcrumb-current {
  color: var(--theme-text-primary);
  font-weight: var(--font-weight-medium);
}
