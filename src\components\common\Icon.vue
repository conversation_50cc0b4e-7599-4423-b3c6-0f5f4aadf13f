<template>
  <component
    :is="iconComponent"
    :class="iconClass"
    :style="iconStyle"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import * as ElementPlusIcons from '@element-plus/icons-vue';

interface Props {
  name: string;
  size?: string | number;
  color?: string;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: '16px',
  color: 'currentColor',
  class: ''
});

// 图标组件映射
const iconComponent = computed(() => {
  const iconName = props.name;
  return ElementPlusIcons[iconName as keyof typeof ElementPlusIcons] || null;
});

// 图标样式类
const iconClass = computed(() => {
  return ['icon', props.class].filter(Boolean).join(' ');
});

// 图标样式
const iconStyle = computed(() => {
  const size = typeof props.size === 'number' ? `${props.size}px` : props.size;
  return {
    width: size,
    height: size,
    color: props.color,
    display: 'inline-block',
    verticalAlign: 'middle'
  };
});
</script>

<style scoped>
.icon {
  flex-shrink: 0;
}
</style>
