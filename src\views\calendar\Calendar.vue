<template>
  <div class="page-container">
    <!-- 日历功能 -->
    <div class="calendar-content">
      <div class="calendar-main">
        <ContentCard title="健康日历" subtitle="管理健康任务和提醒">
          <div class="calendar-container">
            <!-- 日历头部 -->
            <div class="calendar-header">
              <div class="calendar-nav">
                <button class="nav-btn" @click="previousMonth">
                  <ArrowLeft />
                </button>
                <h3 class="current-month">{{ currentMonthYear }}</h3>
                <button class="nav-btn" @click="nextMonth">
                  <ArrowRight />
                </button>
              </div>

              <div class="calendar-filters">
                <select v-model="selectedEventType" class="filter-select">
                  <option value="">全部类型</option>
                  <option value="medication">用药</option>
                  <option value="exercise">运动</option>
                  <option value="checkup">检查</option>
                  <option value="appointment">预约</option>
                </select>
                <select v-model="selectedMember" class="filter-select">
                  <option value="">全部成员</option>
                  <option value="张先生">张先生</option>
                  <option value="李女士">李女士</option>
                  <option value="张小明">张小明</option>
                </select>
              </div>

              <div class="calendar-actions">
                <button class="btn btn-primary" @click="showAddEventModal">
                  <Plus /> 添加事件
                </button>
              </div>
            </div>

            <!-- 日历网格 -->
            <div class="calendar-grid">
              <!-- 星期标题 -->
              <div class="weekdays">
                <div v-for="day in weekdays" :key="day" class="weekday">
                  {{ day }}
                </div>
              </div>

              <!-- 日期网格 -->
              <div class="days-grid">
                <div
                  v-for="day in calendarDays"
                  :key="day.date"
                  class="day-cell"
                  :class="{
                    'other-month': !day.isCurrentMonth,
                    today: day.isToday,
                    selected: day.isSelected,
                    'has-events': day.events.length > 0,
                  }"
                  @click="selectDay(day)"
                >
                  <span class="day-number">{{ day.day }}</span>
                  <div v-if="day.events.length > 0" class="day-events">
                    <div
                      v-for="event in day.events.slice(0, 3)"
                      :key="event.id"
                      class="event-dot"
                      :class="`event-${event.type}`"
                      :title="event.title"
                    ></div>
                    <span v-if="day.events.length > 3" class="more-events">
                      +{{ day.events.length - 3 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 今日事件侧边栏 -->
      <div class="calendar-sidebar">
        <ContentCard title="今日事件" subtitle="今天的健康任务和提醒">
          <div v-if="todayEvents.length === 0" class="empty-state">
            <p>今天没有安排的健康事件</p>
          </div>
          <div v-else class="events-list">
            <div
              v-for="event in todayEvents"
              :key="event.id"
              class="event-item"
              :class="`event-${event.type}`"
            >
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <h4 class="event-title">{{ event.title }}</h4>
                <p class="event-description">{{ event.description }}</p>
              </div>
              <div class="event-actions">
                <button
                  class="btn btn-sm btn-primary"
                  @click="completeEvent(event)"
                >
                  完成
                </button>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>

    <!-- 添加事件模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="cancelAddEvent">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>添加健康事件</h3>
          <button class="close-btn" @click="cancelAddEvent">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>事件标题</label>
            <input
              v-model="newEvent.title"
              type="text"
              placeholder="请输入事件标题"
            />
          </div>
          <div class="form-group">
            <label>事件描述</label>
            <textarea
              v-model="newEvent.description"
              placeholder="请输入事件描述"
            ></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>事件类型</label>
              <select v-model="newEvent.type">
                <option value="medication">用药</option>
                <option value="exercise">运动</option>
                <option value="checkup">检查</option>
                <option value="appointment">预约</option>
              </select>
            </div>
            <div class="form-group">
              <label>家庭成员</label>
              <select v-model="newEvent.member">
                <option value="张先生">张先生</option>
                <option value="李女士">李女士</option>
                <option value="张小明">张小明</option>
              </select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>日期</label>
              <input v-model="newEvent.date" type="date" />
            </div>
            <div class="form-group">
              <label>时间</label>
              <input v-model="newEvent.time" type="time" />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" @click="cancelAddEvent">取消</button>
          <button class="btn btn-primary" @click="saveNewEvent">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入
import { ArrowLeft, ArrowRight, Plus } from "@element-plus/icons-vue";

// 模拟数据
const calendarEvents = [
  {
    id: 1,
    title: "服用降压药",
    description: "早晨服用降压药",
    type: "medication",
    member: "张先生",
    date: new Date(),
    time: "08:00",
  },
  {
    id: 2,
    title: "血压测量",
    description: "定期血压监测",
    type: "checkup",
    member: "张先生",
    date: new Date(),
    time: "18:00",
  },
];

const getActivityTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    medication: "用药",
    checkup: "检查",
    exercise: "运动",
    diet: "饮食",
  };
  return labels[type] || type;
};

// 当前日期
const currentDate = ref(new Date());
const selectedDate = ref(new Date());

// 筛选条件
const selectedEventType = ref("");
const selectedMember = ref("");

// 添加事件模态框
const showAddModal = ref(false);
const newEvent = ref({
  title: "",
  description: "",
  type: "medication",
  member: "张先生",
  date: "",
  time: "08:00",
});

// 星期标题
const weekdays = ["日", "一", "二", "三", "四", "五", "六"];

// 当前月年显示
const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
  });
});

// 事件数据
const events = ref(calendarEvents);

// 过滤后的事件
const filteredEvents = computed(() => {
  return events.value.filter((event) => {
    const typeMatch =
      !selectedEventType.value || event.type === selectedEventType.value;
    const memberMatch =
      !selectedMember.value || event.member === selectedMember.value;
    return typeMatch && memberMatch;
  });
});

// 生成日历天数
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();

  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - firstDay.getDay());

  const days = [];
  const today = new Date();

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    const dayEvents = filteredEvents.value.filter(
      (event) => event.date.toDateString() === date.toDateString()
    );

    days.push({
      date: date.toISOString(),
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.toDateString() === today.toDateString(),
      isSelected: date.toDateString() === selectedDate.value.toDateString(),
      events: dayEvents,
    });
  }

  return days;
});

// 今日事件
const todayEvents = computed(() => {
  const today = new Date();
  return filteredEvents.value.filter(
    (event) => event.date.toDateString() === today.toDateString()
  );
});

// 导航方法
const previousMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  );
};

const nextMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  );
};

// 选择日期
const selectDay = (day: any) => {
  selectedDate.value = new Date(day.date);
};

// 添加事件
const showAddEventModal = () => {
  showAddModal.value = true;
  newEvent.value.date = selectedDate.value.toISOString().split("T")[0];
};

// 保存新事件
const saveNewEvent = () => {
  if (newEvent.value.title && newEvent.value.date) {
    const event = {
      id: Date.now(),
      title: newEvent.value.title,
      description: newEvent.value.description,
      type: newEvent.value.type,
      member: newEvent.value.member,
      date: new Date(newEvent.value.date),
      time: newEvent.value.time,
      status: "pending",
    };
    events.value.push(event);
    showAddModal.value = false;
    resetNewEvent();
  }
};

// 重置新事件表单
const resetNewEvent = () => {
  newEvent.value = {
    title: "",
    description: "",
    type: "medication",
    member: "张先生",
    date: "",
    time: "08:00",
  };
};

// 取消添加事件
const cancelAddEvent = () => {
  showAddModal.value = false;
  resetNewEvent();
};

// 完成事件
const completeEvent = (event: any) => {
  event.status = "completed";
  console.log("完成事件:", event.title);
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.calendar-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

.calendar-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-6);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.nav-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.current-month {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
}

.calendar-grid {
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: var(--theme-bg-secondary);
}

.weekday {
  padding: var(--spacing-3);
  text-align: center;
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
  border-right: 1px solid var(--theme-border);
}

.weekday:last-child {
  border-right: none;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.day-cell {
  min-height: 80px;
  padding: var(--spacing-2);
  border-right: 1px solid var(--theme-border);
  border-bottom: 1px solid var(--theme-border);
  cursor: pointer;
  transition: background-color var(--transition-normal);
  position: relative;
}

.day-cell:hover {
  background: var(--theme-bg-secondary);
}

.day-cell.other-month {
  color: var(--theme-text-tertiary);
  background: var(--theme-bg-tertiary);
}

.day-cell.today {
  background: var(--color-primary-light);
}

.day-cell.selected {
  background: var(--color-primary);
  color: white;
}

.day-number {
  font-weight: var(--font-weight-medium);
}

.day-events {
  margin-top: var(--spacing-1);
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--color-primary);
}

.event-dot.event-medication {
  background: var(--color-info);
}

.event-dot.event-checkup {
  background: var(--color-warning);
}

.event-dot.event-exercise {
  background: var(--color-success);
}

.more-events {
  font-size: 10px;
  color: var(--theme-text-secondary);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.event-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--color-primary);
  background: var(--theme-bg-secondary);
}

.event-item.event-medication {
  border-left-color: var(--color-info);
}

.event-item.event-checkup {
  border-left-color: var(--color-warning);
}

.event-time {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
  min-width: 60px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.event-description {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  margin: 0;
}

.btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

/* 筛选器样式 */
.calendar-filters {
  display: flex;
  gap: var(--spacing-3);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--theme-border);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--theme-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.close-btn:hover {
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.modal-body {
  padding: var(--spacing-6);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-6);
  border-top: 1px solid var(--theme-border);
}

.btn-outline {
  background: transparent;
  color: var(--theme-text-secondary);
  border-color: var(--theme-border);
}

.btn-outline:hover {
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

@media (max-width: 768px) {
  .calendar-content {
    grid-template-columns: 1fr;
  }

  .calendar-header {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .day-cell {
    min-height: 60px;
  }
}
</style>
