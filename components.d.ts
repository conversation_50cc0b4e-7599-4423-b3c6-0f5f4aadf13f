/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActionCard: typeof import('./src/components/family/ActionCard.vue')['default']
    ActivityLogDialog: typeof import('./src/components/family/ActivityLogDialog.vue')['default']
    AddArchiveDialog: typeof import('./src/components/health-archive/AddArchiveDialog.vue')['default']
    AddDeviceDialog: typeof import('./src/components/health-archive/AddDeviceDialog.vue')['default']
    AddMedicineDialog: typeof import('./src/components/medicine/AddMedicineDialog.vue')['default']
    BarChart: typeof import('./src/components/charts/BarChart.vue')['default']
    BaseChart: typeof import('./src/components/charts/BaseChart.vue')['default']
    BatchMemberOperationsDialog: typeof import('./src/components/family/BatchMemberOperationsDialog.vue')['default']
    Breadcrumb: typeof import('./src/components/common/Breadcrumb.vue')['default']
    ContentCard: typeof import('./src/components/common/ContentCard.vue')['default']
    CreateFamilyDialog: typeof import('./src/components/family/CreateFamilyDialog.vue')['default']
    DeviceConfigDialog: typeof import('./src/components/health-archive/DeviceConfigDialog.vue')['default']
    EditArchiveDialog: typeof import('./src/components/health-archive/EditArchiveDialog.vue')['default']
    EditMedicineDialog: typeof import('./src/components/medicine/EditMedicineDialog.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EmergencyContactsDialog: typeof import('./src/components/family/EmergencyContactsDialog.vue')['default']
    FamilyActivityLogDialog: typeof import('./src/components/family/FamilyActivityLogDialog.vue')['default']
    FamilyEditDialog: typeof import('./src/components/family/FamilyEditDialog.vue')['default']
    FamilyInfoDialog: typeof import('./src/components/family/FamilyInfoDialog.vue')['default']
    FamilyReportsDialog: typeof import('./src/components/family/FamilyReportsDialog.vue')['default']
    FamilySettingsDialog: typeof import('./src/components/family/FamilySettingsDialog.vue')['default']
    HealthArchiveSettingsDialog: typeof import('./src/components/health-archive/HealthArchiveSettingsDialog.vue')['default']
    HealthDataFilterDialog: typeof import('./src/components/health-archive/HealthDataFilterDialog.vue')['default']
    HealthDataSyncDialog: typeof import('./src/components/health-archive/HealthDataSyncDialog.vue')['default']
    HealthReminderDialog: typeof import('./src/components/health-archive/HealthReminderDialog.vue')['default']
    HealthReportDetailDialog: typeof import('./src/components/health-archive/HealthReportDetailDialog.vue')['default']
    HealthReportListDialog: typeof import('./src/components/health-archive/HealthReportListDialog.vue')['default']
    HealthReportUploadDialog: typeof import('./src/components/health-archive/HealthReportUploadDialog.vue')['default']
    HealthTrendAnalysisDialog: typeof import('./src/components/health-archive/HealthTrendAnalysisDialog.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    InviteHistoryDialog: typeof import('./src/components/family/InviteHistoryDialog.vue')['default']
    InviteMemberDialog: typeof import('./src/components/family/InviteMemberDialog.vue')['default']
    LineChart: typeof import('./src/components/charts/LineChart.vue')['default']
    MainLayout: typeof import('./src/components/layout/MainLayout.vue')['default']
    MedicineBoxManager: typeof import('./src/components/medicine/MedicineBoxManager.vue')['default']
    MedicineDetailsDialog: typeof import('./src/components/medicine/MedicineDetailsDialog.vue')['default']
    MemberRoleDialog: typeof import('./src/components/family/MemberRoleDialog.vue')['default']
    PageContainer: typeof import('./src/components/common/PageContainer.vue')['default']
    PermissionManagementDialog: typeof import('./src/components/family/PermissionManagementDialog.vue')['default']
    PieChart: typeof import('./src/components/charts/PieChart.vue')['default']
    RemoveMemberDialog: typeof import('./src/components/family/RemoveMemberDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatCard: typeof import('./src/components/common/StatCard.vue')['default']
    StockAdjustmentDialog: typeof import('./src/components/medicine/StockAdjustmentDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
