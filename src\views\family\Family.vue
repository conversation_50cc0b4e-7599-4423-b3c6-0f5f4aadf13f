<template>
  <div class="page-container">
    <!-- 家庭统计概览 -->
    <div class="family-stats-section">
      <div class="stats-grid">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.totalMembers }}</div>
            <div class="stat-label">家庭成员</div>
            <div class="stat-trend">
              <span class="trend-text">较上月 +0</span>
            </div>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.healthyMembers }}</div>
            <div class="stat-label">健康状态良好</div>
            <div class="stat-trend">
              <span class="trend-text">健康率 100%</span>
            </div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.needAttention }}</div>
            <div class="stat-label">需要关注</div>
            <div class="stat-trend">
              <span class="trend-text">较上月 -1</span>
            </div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.checkupsThisMonth }}</div>
            <div class="stat-label">本月体检</div>
            <div class="stat-trend">
              <span class="trend-text">下次：3天后</span>
            </div>
          </div>
        </div>

        <div class="stat-card medicine">
          <div class="stat-icon">
            <el-icon><FirstAidKit /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.totalMedicines }}</div>
            <div class="stat-label">家庭药品</div>
            <div class="stat-trend">
              <span class="trend-text"
                >{{ familyStats.expiringSoon }}个即将过期</span
              >
            </div>
          </div>
        </div>

        <div class="stat-card activity">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ familyStats.weeklyActivities }}</div>
            <div class="stat-label">本周活动</div>
            <div class="stat-trend">
              <span class="trend-text"
                >较上周 +{{ familyStats.activityIncrease }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 家庭健康概览 -->
    <div class="family-overview-section">
      <div class="section-header">
        <h2 class="section-title">家庭健康概览</h2>
        <div class="header-actions">
          <el-button-group>
            <el-button
              :type="overviewPeriod === 'week' ? 'primary' : ''"
              @click="overviewPeriod = 'week'"
              >本周</el-button
            >
            <el-button
              :type="overviewPeriod === 'month' ? 'primary' : ''"
              @click="overviewPeriod = 'month'"
              >本月</el-button
            >
            <el-button
              :type="overviewPeriod === 'year' ? 'primary' : ''"
              @click="overviewPeriod = 'year'"
              >本年</el-button
            >
          </el-button-group>
        </div>
      </div>

      <div class="overview-grid">
        <div class="overview-card health-trend">
          <h3 class="card-title">健康趋势</h3>
          <div class="trend-chart">
            <div class="chart-placeholder">
              <el-icon><TrendCharts /></el-icon>
              <span>健康指数趋势图</span>
            </div>
            <div class="trend-summary">
              <div class="trend-item">
                <span class="trend-label">平均健康指数</span>
                <span class="trend-value good">85分</span>
              </div>
              <div class="trend-item">
                <span class="trend-label">本月变化</span>
                <span class="trend-value positive">+3分</span>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card recent-activities">
          <h3 class="card-title">最近活动</h3>
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.type">
                <el-icon v-if="activity.type === 'checkup'"
                  ><Document
                /></el-icon>
                <el-icon v-else-if="activity.type === 'medicine'"
                  ><FirstAidKit
                /></el-icon>
                <el-icon v-else><Clock /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">
                  {{ formatActivityTime(activity.time) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card health-alerts">
          <h3 class="card-title">健康提醒</h3>
          <div class="alert-list">
            <div
              v-for="alert in healthAlerts"
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <div class="alert-icon">
                <el-icon v-if="alert.level === 'warning'"><Warning /></el-icon>
                <el-icon v-else><CircleCheck /></el-icon>
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-desc">{{ alert.description }}</div>
                <div class="alert-actions">
                  <el-button size="small" type="primary" link>{{
                    alert.action
                  }}</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 家庭成员列表 -->
    <div class="family-members-section">
      <div class="section-header">
        <h2 class="section-title">家庭成员</h2>
        <div class="header-actions">
          <button class="btn btn-primary" @click="showInviteDialog = true">
            <el-icon><User /></el-icon>
            <span>邀请成员</span>
          </button>
          <button
            class="btn btn-outline"
            @click="showFamilySettingsDialog = true"
          >
            <el-icon><Setting /></el-icon>
            <span>家庭设置</span>
          </button>
        </div>
      </div>

      <div class="members-grid">
        <ContentCard
          v-for="member in familyMembers"
          :key="member.id"
          :title="member.name"
          :subtitle="member.relationship"
          hoverable
          size="md"
          class="member-card"
        >
          <template #actions>
            <div class="member-actions">
              <button
                class="btn btn-sm btn-outline"
                @click="viewMemberProfile(member)"
              >
                查看档案
              </button>
              <el-dropdown
                @command="(command) => handleMemberAction(command, member)"
              >
                <button class="btn btn-sm btn-outline">
                  <span>更多</span>
                  <el-icon><ArrowDown /></el-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                    <el-dropdown-item command="role">角色管理</el-dropdown-item>
                    <el-dropdown-item
                      command="remove"
                      divided
                      :disabled="member.id === 1"
                    >
                      移除成员
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <div class="member-info">
            <div class="member-avatar">
              <img :src="member.avatar" :alt="member.name" />
              <div class="status-indicator" :class="member.healthStatus"></div>
            </div>
            <div class="member-details">
              <div class="detail-item">
                <span class="detail-label">年龄</span>
                <span class="detail-value">{{ member.age }}岁</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">性别</span>
                <span class="detail-value">{{ member.gender }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">健康状态</span>
                <span
                  class="detail-value"
                  :class="`status-${member.healthStatus}`"
                >
                  {{ getHealthStatusText(member.healthStatus) }}
                </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">最近体检</span>
                <span class="detail-value">{{
                  formatDate(member.lastCheckup)
                }}</span>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <h2 class="section-title">快速操作</h2>
      </div>
      <div class="quick-actions-grid">
        <div class="action-card" @click="handleFamilyInfo">
          <div class="action-icon family-info">
            <el-icon><House /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">家庭信息</h3>
            <p class="action-description">管理家庭基本信息</p>
          </div>
        </div>

        <div class="action-card" @click="handlePermissionManagement">
          <div class="action-icon permission">
            <el-icon><Key /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">权限管理</h3>
            <p class="action-description">设置成员访问权限</p>
          </div>
        </div>

        <div class="action-card" @click="handleEmergencyContacts">
          <div class="action-icon emergency">
            <el-icon><Phone /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">紧急联系人</h3>
            <p class="action-description">设置紧急联系信息</p>
          </div>
        </div>

        <div class="action-card" @click="handleActivityLog">
          <div class="action-icon activity">
            <el-icon><Document /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">活动日志</h3>
            <p class="action-description">查看操作记录</p>
          </div>
        </div>

        <div class="action-card" @click="handleInviteHistory">
          <div class="action-icon invite-history">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">邀请历史</h3>
            <p class="action-description">管理邀请记录</p>
          </div>
        </div>

        <div class="action-card" @click="handleFamilyMedicineBox">
          <div class="action-icon medicine">
            <el-icon><FirstAidKit /></el-icon>
          </div>
          <div class="action-content">
            <h3 class="action-title">家庭药箱</h3>
            <p class="action-description">管理共享药品</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <FamilyInfoDialog
      v-model="showFamilyInfoDialog"
      @success="handleDialogSuccess"
    />

    <PermissionManagementDialog
      v-model="showPermissionDialog"
      @success="handleDialogSuccess"
    />

    <EmergencyContactsDialog
      v-model="showEmergencyDialog"
      @success="handleDialogSuccess"
    />

    <FamilySettingsDialog
      v-model="showSettingsDialog"
      @success="handleDialogSuccess"
    />

    <ActivityLogDialog
      v-model="showActivityDialog"
      @success="handleDialogSuccess"
    />

    <InviteHistoryDialog
      v-model="showInviteHistoryDialog"
      @success="handleDialogSuccess"
    />

    <InviteMemberDialog
      v-model="showInviteDialog"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
// 暂时注释掉组件导入，先让页面正常运行
// import ActionCard from "../../components/family/ActionCard.vue";
// import ContentCard from "../../components/common/ContentCard.vue";
import {
  House,
  Key,
  Phone,
  Setting,
  Document,
  Clock,
  Share,
  FirstAidKit,
  User,
  ArrowDown,
  ArrowRight,
  CircleCheck,
  Warning,
  TrendCharts,
} from "@element-plus/icons-vue";

const router = useRouter();

// 对话框状态
const showFamilyInfoDialog = ref(false);
const showPermissionDialog = ref(false);
const showEmergencyDialog = ref(false);
const showSettingsDialog = ref(false);
const showActivityDialog = ref(false);
const showInviteHistoryDialog = ref(false);
const showInviteDialog = ref(false);
const showFamilySettingsDialog = ref(false);
const overviewPeriod = ref("month");

// 家庭统计数据
const familyStats = ref({
  totalMembers: 3,
  healthyMembers: 3,
  needAttention: 0,
  checkupsThisMonth: 2,
  totalMedicines: 15,
  expiringSoon: 2,
  weeklyActivities: 8,
  activityIncrease: 3,
});

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    type: "checkup",
    title: "张先生完成了年度体检",
    time: new Date("2024-01-20T10:30:00"),
    details: "血压、血糖、心电图等指标正常",
  },
  {
    id: 2,
    type: "medicine",
    title: "李女士添加了新药品",
    time: new Date("2024-01-19T15:20:00"),
    details: "维生素D3，每日一粒",
  },
  {
    id: 3,
    type: "activity",
    title: "张小明记录了运动数据",
    time: new Date("2024-01-18T18:45:00"),
    details: "跑步30分钟，消耗300卡路里",
  },
  {
    id: 4,
    type: "checkup",
    title: "李女士预约了妇科检查",
    time: new Date("2024-01-17T14:00:00"),
    details: "预约时间：2024年1月25日上午9:00",
  },
  {
    id: 5,
    type: "medicine",
    title: "张小明服用感冒药",
    time: new Date("2024-01-16T08:30:00"),
    details: "连花清瘟胶囊，一日三次",
  },
]);

// 健康提醒数据
const healthAlerts = ref([
  {
    id: 1,
    level: "warning",
    title: "药品即将过期",
    description: "感冒药将在3天后过期，请及时处理",
    action: "查看药箱",
    priority: "high",
  },
  {
    id: 2,
    level: "info",
    title: "体检提醒",
    description: "张小明的年度体检预约在下周三",
    action: "查看预约",
    priority: "medium",
  },
  {
    id: 3,
    level: "warning",
    title: "血压监测提醒",
    description: "张先生已连续5天未测量血压",
    action: "立即测量",
    priority: "high",
  },
  {
    id: 4,
    level: "info",
    title: "运动目标达成",
    description: "张小明本周运动目标已完成80%",
    action: "查看详情",
    priority: "low",
  },
  {
    id: 5,
    level: "warning",
    title: "疫苗接种提醒",
    description: "李女士的流感疫苗需要在本月内接种",
    action: "预约接种",
    priority: "medium",
  },
]);

// 快速操作数据
const quickActions = ref([
  {
    id: 1,
    title: "家庭信息",
    description: "查看和编辑家庭基本信息",
    icon: "House",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
    handler: () => (showFamilyInfoDialog.value = true),
  },
  {
    id: 2,
    title: "权限管理",
    description: "管理家庭成员权限设置",
    icon: "Key",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
    handler: () => (showPermissionDialog.value = true),
  },
  {
    id: 3,
    title: "紧急联系人",
    description: "设置紧急情况联系人",
    icon: "Phone",
    iconColor: "#EF4444",
    iconBgColor: "#FEE2E2",
    handler: () => (showEmergencyDialog.value = true),
  },
  {
    id: 4,
    title: "活动日志",
    description: "查看家庭健康活动记录",
    icon: "Document",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
    handler: () => (showActivityDialog.value = true),
  },
  {
    id: 5,
    title: "邀请历史",
    description: "查看家庭成员邀请记录",
    icon: "Clock",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
    handler: () => (showInviteHistoryDialog.value = true),
  },
  {
    id: 6,
    title: "家庭药箱",
    description: "管理家庭常用药品",
    icon: "FirstAidKit",
    iconColor: "#06B6D4",
    iconBgColor: "#CFFAFE",
    handler: () => router.push("/activities/medicine"),
  },
]);

// 家庭成员数据
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    relationship: "本人",
    age: 45,
    gender: "男",
    healthStatus: "normal",
    lastCheckup: new Date("2024-01-15"),
    avatar: "https://via.placeholder.com/60x60?text=张",
  },
  {
    id: 2,
    name: "李女士",
    relationship: "配偶",
    age: 42,
    gender: "女",
    healthStatus: "normal",
    lastCheckup: new Date("2024-02-10"),
    avatar: "https://via.placeholder.com/60x60?text=李",
  },
  {
    id: 3,
    name: "张小明",
    relationship: "子女",
    age: 18,
    gender: "男",
    healthStatus: "normal",
    lastCheckup: new Date("2024-03-05"),
    avatar: "https://via.placeholder.com/60x60?text=明",
  },
]);

// 工具函数
const getHealthStatusText = (status: string) => {
  switch (status) {
    case "normal":
      return "健康";
    case "warning":
      return "需关注";
    case "danger":
      return "异常";
    default:
      return "未知";
  }
};

const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

const formatActivityTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor(diff / (1000 * 60));

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return "刚刚";
  }
};

// 图标映射函数
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    House,
    Key,
    Phone,
    Document,
    Clock,
    FirstAidKit,
    Setting,
    Share,
    User,
    ArrowDown,
    ArrowRight,
    CircleCheck,
    Warning,
    TrendCharts,
  };
  return iconMap[iconName] || House;
};

// 成员操作函数
const viewMemberProfile = (member: any) => {
  router.push(`/archive/personal-info?member=${member.id}`);
};

const handleMemberAction = (command: string, member: any) => {
  switch (command) {
    case "edit":
      ElMessage.info(`编辑 ${member.name} 的信息`);
      break;
    case "role":
      ElMessage.info(`管理 ${member.name} 的角色`);
      break;
    case "remove":
      ElMessage.warning(`移除 ${member.name}`);
      break;
  }
};

// 功能处理函数
const handleFamilyInfo = () => {
  showFamilyInfoDialog.value = true;
};

const handlePermissionManagement = () => {
  showPermissionDialog.value = true;
};

const handleEmergencyContacts = () => {
  showEmergencyDialog.value = true;
};

const handleFamilySettings = () => {
  showSettingsDialog.value = true;
};

const handleActivityLog = () => {
  showActivityDialog.value = true;
};

const handleInviteHistory = () => {
  showInviteHistoryDialog.value = true;
};

const handleInviteFamily = () => {
  showInviteDialog.value = true;
};

const handleFamilyMedicineBox = () => {
  router.push("/family/medicine-box");
};

const handleDialogSuccess = () => {
  ElMessage.success("操作成功");
};

const handleInviteSuccess = () => {
  ElMessage.success("邀请发送成功");
  // 重新加载家庭成员数据
  // 这里可以添加刷新成员列表的逻辑
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}

.family-stats-section {
  margin-bottom: var(--spacing-8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-4);
}

.stat-card {
  background: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  border: 1px solid var(--theme-border);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--card-accent-color);
}

.stat-card.primary {
  --card-accent-color: #3b82f6;
}

.stat-card.primary .stat-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.stat-card.success {
  --card-accent-color: #10b981;
}

.stat-card.success .stat-icon {
  background: #d1fae5;
  color: #10b981;
}

.stat-card.warning {
  --card-accent-color: #f59e0b;
}

.stat-card.warning .stat-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.stat-card.info {
  --card-accent-color: #8b5cf6;
}

.stat-card.info .stat-icon {
  background: #ede9fe;
  color: #8b5cf6;
}

.stat-card.medicine {
  --card-accent-color: #ef4444;
}

.stat-card.medicine .stat-icon {
  background: #fee2e2;
  color: #ef4444;
}

.stat-card.activity {
  --card-accent-color: #06b6d4;
}

.stat-card.activity .stat-icon {
  background: #cffafe;
  color: #06b6d4;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--theme-text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-trend {
  font-size: 12px;
}

.trend-text {
  color: var(--theme-text-tertiary);
}

.quick-actions-section {
  margin-bottom: var(--spacing-8);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.family-overview-section {
  margin-bottom: var(--spacing-8);
}

.overview-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--spacing-6);
}

.overview-card {
  background: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  border: 1px solid var(--theme-border);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme-text-secondary);
  border: 2px dashed var(--theme-border);
  border-radius: var(--border-radius-md);
  gap: var(--spacing-2);
}

.chart-placeholder .el-icon {
  font-size: 48px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.activity-item:hover {
  background: var(--theme-bg-secondary);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.activity-icon.checkup {
  background: #ede9fe;
  color: #8b5cf6;
}

.activity-icon.medicine {
  background: #fee2e2;
  color: #ef4444;
}

.activity-icon.activity {
  background: #cffafe;
  color: #06b6d4;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: 2px;
}

.activity-time {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--alert-color);
}

.alert-item.warning {
  --alert-color: #f59e0b;
  background: #fffbeb;
}

.alert-item.info {
  --alert-color: #3b82f6;
  background: #eff6ff;
}

.alert-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--alert-color);
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: 2px;
}

.alert-desc {
  font-size: 12px;
  color: var(--theme-text-secondary);
  line-height: 1.4;
}

.family-members-section {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.member-card {
  transition: transform var(--transition-normal);
}

.member-card:hover {
  transform: translateY(-2px);
}

.member-actions {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.member-info {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.member-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.normal {
  background-color: var(--color-success);
}

.status-indicator.warning {
  background-color: var(--color-warning);
}

.status-indicator.danger {
  background-color: var(--color-danger);
}

.member-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.detail-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.status-normal {
  color: var(--color-success);
}

.status-warning {
  color: var(--color-warning);
}

.status-danger {
  color: var(--color-danger);
}

.action-card {
  background: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  border: 1px solid var(--theme-border);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.action-card:hover {
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.action-icon.family-info {
  background: #dbeafe;
  color: #3b82f6;
}

.action-icon.permission {
  background: #d1fae5;
  color: #10b981;
}

.action-icon.emergency {
  background: #fee2e2;
  color: #ef4444;
}

.action-icon.activity {
  background: #fef3c7;
  color: #f59e0b;
}

.action-icon.invite-history {
  background: #cffafe;
  color: #06b6d4;
}

.action-icon.medicine {
  background: #fee2e2;
  color: #ef4444;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .overview-grid {
    grid-template-columns: 1fr 1fr;
  }

  .overview-card.health-trend {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-4);
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-3);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .member-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .detail-item {
    justify-content: center;
    gap: var(--spacing-2);
  }

  .stat-card {
    padding: var(--spacing-4);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .chart-placeholder {
    height: 150px;
  }
}
</style>
