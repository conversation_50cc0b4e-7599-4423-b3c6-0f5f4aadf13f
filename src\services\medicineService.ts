import type {
  Medicine,
  MedicineStats,
  MedicineFilter,
  MedicineFormData,
  StockAdjustment,
} from "@/types/medicine";

// 模拟数据
const mockMedicines: Medicine[] = [
  {
    id: "1",
    name: "阿莫西林胶囊",
    category: "prescription",
    dosage: "250mg",
    unit: "粒",
    quantity: 24,
    minQuantity: 10,
    expiryDate: "2024-12-31",
    manufacturer: "华北制药",
    batchNumber: "HB20240301",
    instructions: "每次2粒，每日3次，饭后服用",
    sideEffects: "可能出现恶心、腹泻等胃肠道反应",
    contraindications: "对青霉素过敏者禁用",
    storage: "密封，在干燥处保存",
    price: 15.8,
    purchaseDate: "2024-03-01",
    location: "卧室药箱",
    notes: "感冒时备用",
    userId: "1",
    createdAt: "2024-03-01T10:00:00Z",
    updatedAt: "2024-03-01T10:00:00Z",
  },
  {
    id: "2",
    name: "维生素C片",
    category: "supplement",
    dosage: "100mg",
    unit: "片",
    quantity: 5,
    minQuantity: 20,
    expiryDate: "2024-08-15",
    manufacturer: "汤臣倍健",
    instructions: "每日1片，饭后服用",
    storage: "避光，干燥处保存",
    price: 68.0,
    purchaseDate: "2024-02-15",
    location: "客厅药箱",
    notes: "增强免疫力",
    userId: "1",
    createdAt: "2024-02-15T10:00:00Z",
    updatedAt: "2024-02-15T10:00:00Z",
  },
  {
    id: "3",
    name: "布洛芬缓释胶囊",
    category: "otc",
    dosage: "300mg",
    unit: "粒",
    quantity: 18,
    minQuantity: 12,
    expiryDate: "2025-06-30",
    manufacturer: "中美史克",
    batchNumber: "SK20240415",
    instructions: "口服，一次1粒，一日2次",
    sideEffects: "可能出现胃肠道不适、头痛、眩晕",
    contraindications: "消化性溃疡患者禁用",
    storage: "密封保存",
    price: 22.0,
    purchaseDate: "2024-04-20",
    location: "卧室药箱",
    notes: "退热止痛",
    userId: "1",
    createdAt: "2024-04-20T09:15:00Z",
    updatedAt: "2024-04-20T09:15:00Z",
  },
  {
    id: "4",
    name: "复方甘草片",
    category: "otc",
    dosage: "0.3g",
    unit: "片",
    quantity: 30,
    minQuantity: 15,
    expiryDate: "2024-11-20",
    manufacturer: "太极集团",
    batchNumber: "TJ20240310",
    instructions: "口服，一次3-4片，一日3次",
    sideEffects: "长期大量服用可能引起水钠潴留",
    contraindications: "高血压、心脏病患者慎用",
    storage: "密封，在干燥处保存",
    price: 8.5,
    purchaseDate: "2024-03-25",
    location: "客厅药箱",
    notes: "止咳化痰",
    userId: "1",
    createdAt: "2024-03-25T16:45:00Z",
    updatedAt: "2024-03-25T16:45:00Z",
  },
  {
    id: "5",
    name: "钙尔奇D片",
    category: "supplement",
    dosage: "600mg",
    unit: "片",
    quantity: 45,
    minQuantity: 30,
    expiryDate: "2025-03-10",
    manufacturer: "辉瑞制药",
    batchNumber: "PF20240505",
    instructions: "口服，一次1片，一日1-2次",
    sideEffects: "偶见便秘、腹胀",
    contraindications: "高钙血症患者禁用",
    storage: "密封，在干燥处保存",
    price: 45.0,
    purchaseDate: "2024-05-10",
    location: "卧室药箱",
    notes: "补钙",
    userId: "1",
    createdAt: "2024-05-10T11:20:00Z",
    updatedAt: "2024-05-10T11:20:00Z",
  },
  {
    id: "6",
    name: "蒙脱石散",
    category: "otc",
    dosage: "3g",
    unit: "袋",
    quantity: 8,
    minQuantity: 6,
    expiryDate: "2024-09-15",
    manufacturer: "博福-益普生",
    batchNumber: "BF20240220",
    instructions: "口服，成人一次1袋，一日3次",
    sideEffects: "偶见便秘",
    contraindications: "肠梗阻患者禁用",
    storage: "密封保存",
    price: 18.8,
    purchaseDate: "2024-02-28",
    location: "客厅药箱",
    notes: "止泻",
    userId: "1",
    createdAt: "2024-02-28T13:10:00Z",
    updatedAt: "2024-02-28T13:10:00Z",
  },
  {
    id: "7",
    name: "感康复方氨酚烷胺片",
    category: "otc",
    dosage: "12片",
    unit: "盒",
    quantity: 2,
    minQuantity: 3,
    expiryDate: "2024-10-30",
    manufacturer: "吴太感康",
    batchNumber: "WT20240401",
    instructions: "口服，一次1片，一日2次",
    sideEffects: "可能出现困倦、口干",
    contraindications: "严重肝肾功能不全者禁用",
    storage: "密封，在干燥处保存",
    price: 12.5,
    purchaseDate: "2024-04-05",
    location: "客厅药箱",
    notes: "感冒药",
    userId: "1",
    createdAt: "2024-04-05T14:30:00Z",
    updatedAt: "2024-04-05T14:30:00Z",
  },
  {
    id: "8",
    name: "开塞露",
    category: "otc",
    dosage: "20ml",
    unit: "支",
    quantity: 6,
    minQuantity: 4,
    expiryDate: "2025-12-31",
    manufacturer: "山东齐都",
    batchNumber: "QD20240601",
    instructions: "直肠给药，成人一次1支",
    sideEffects: "偶见局部刺激",
    contraindications: "肠道出血患者禁用",
    storage: "密封保存",
    price: 3.2,
    purchaseDate: "2024-06-10",
    location: "卫生间药箱",
    notes: "通便",
    userId: "1",
    createdAt: "2024-06-10T08:45:00Z",
    updatedAt: "2024-06-10T08:45:00Z",
  },
  {
    id: "5",
    name: "钙尔奇D片",
    category: "supplement",
    dosage: "600mg",
    unit: "片",
    quantity: 45,
    minQuantity: 30,
    expiryDate: "2025-01-10",
    manufacturer: "惠氏制药",
    batchNumber: "WY20240201",
    instructions: "每日1片，餐后服用",
    sideEffects: "偶见便秘、腹胀",
    contraindications: "高钙血症患者禁用",
    storage: "密封，置阴凉干燥处",
    price: 89.0,
    purchaseDate: "2024-02-01",
    location: "卧室药箱",
    notes: "补钙保健",
    userId: "1",
    createdAt: "2024-02-01T14:30:00Z",
    updatedAt: "2024-02-01T14:30:00Z",
  },
  {
    id: "6",
    name: "蒙脱石散",
    category: "otc",
    dosage: "3g",
    unit: "袋",
    quantity: 8,
    minQuantity: 6,
    expiryDate: "2024-09-15",
    manufacturer: "博福-益普生",
    batchNumber: "BP20240118",
    instructions: "口服，成人一次1袋，一日3次",
    sideEffects: "偶见便秘",
    contraindications: "肠梗阻患者禁用",
    storage: "密封保存",
    price: 18.6,
    purchaseDate: "2024-01-20",
    location: "客厅药箱",
    notes: "止泻药",
    userId: "1",
    createdAt: "2024-01-20T11:20:00Z",
    updatedAt: "2024-01-20T11:20:00Z",
  },
  {
    id: "7",
    name: "开塞露",
    category: "otc",
    dosage: "20ml",
    unit: "支",
    quantity: 6,
    minQuantity: 3,
    expiryDate: "2025-12-31",
    manufacturer: "山东齐都药业",
    batchNumber: "QD20240505",
    instructions: "直肠给药，成人一次1支",
    sideEffects: "偶见局部刺激",
    contraindications: "直肠出血患者禁用",
    storage: "密封保存",
    price: 12.0,
    purchaseDate: "2024-05-10",
    location: "卫生间药箱",
    notes: "通便润肠",
    userId: "1",
    createdAt: "2024-05-10T08:45:00Z",
    updatedAt: "2024-05-10T08:45:00Z",
  },
  {
    id: "8",
    name: "999感冒灵颗粒",
    category: "otc",
    dosage: "10g",
    unit: "袋",
    quantity: 12,
    minQuantity: 8,
    expiryDate: "2024-10-30",
    manufacturer: "华润三九",
    batchNumber: "HR20240215",
    instructions: "开水冲服，一次1袋，一日3次",
    sideEffects: "偶见恶心、头晕",
    contraindications: "严重肝肾功能不全者禁用",
    storage: "密封，置阴凉干燥处",
    price: 16.8,
    purchaseDate: "2024-02-20",
    location: "客厅药箱",
    notes: "感冒常备药",
    userId: "1",
    createdAt: "2024-02-20T13:15:00Z",
    updatedAt: "2024-02-20T13:15:00Z",
  },
  {
    id: "9",
    name: "云南白药胶囊",
    category: "otc",
    dosage: "0.25g",
    unit: "粒",
    quantity: 16,
    minQuantity: 12,
    expiryDate: "2025-03-20",
    manufacturer: "云南白药集团",
    batchNumber: "YN20240308",
    instructions: "口服，一次4粒，一日3次",
    sideEffects: "偶见胃肠道反应",
    contraindications: "孕妇禁用",
    storage: "密封，置阴凉干燥处",
    price: 35.0,
    purchaseDate: "2024-03-15",
    location: "卧室药箱",
    notes: "跌打损伤",
    userId: "1",
    createdAt: "2024-03-15T10:30:00Z",
    updatedAt: "2024-03-15T10:30:00Z",
  },
  {
    id: "10",
    name: "丹参滴丸",
    category: "prescription",
    dosage: "27mg",
    unit: "丸",
    quantity: 180,
    minQuantity: 90,
    expiryDate: "2024-12-15",
    manufacturer: "天士力制药",
    batchNumber: "TSL20240120",
    instructions: "舌下含服，一次10丸，一日3次",
    sideEffects: "偶见口干、头晕",
    contraindications: "低血压患者慎用",
    storage: "密封，置阴凉干燥处",
    price: 45.6,
    purchaseDate: "2024-01-25",
    location: "卧室药箱",
    notes: "心血管保健",
    userId: "1",
    createdAt: "2024-01-25T09:00:00Z",
    updatedAt: "2024-01-25T09:00:00Z",
  },
];

class MedicineService {
  // 获取药品列表
  async getMedicines(
    userId: string,
    filter?: MedicineFilter
  ): Promise<Medicine[]> {
    let medicines = mockMedicines.filter((m) => m.userId === userId);

    if (filter?.category && filter.category !== "all") {
      medicines = medicines.filter((m) => m.category === filter.category);
    }

    if (filter?.search) {
      const search = filter.search.toLowerCase();
      medicines = medicines.filter(
        (m) =>
          m.name.toLowerCase().includes(search) ||
          m.manufacturer.toLowerCase().includes(search)
      );
    }

    if (filter?.status && filter.status !== "all") {
      const now = new Date();
      const thirtyDaysLater = new Date(
        now.getTime() + 30 * 24 * 60 * 60 * 1000
      );

      medicines = medicines.filter((m) => {
        const expiryDate = new Date(m.expiryDate);

        switch (filter.status) {
          case "expiring":
            return expiryDate <= thirtyDaysLater && expiryDate > now;
          case "expired":
            return expiryDate <= now;
          case "lowStock":
            return m.quantity <= m.minQuantity;
          default:
            return true;
        }
      });
    }

    return medicines;
  }

  // 获取药品统计
  async getMedicineStats(userId: string): Promise<MedicineStats> {
    const medicines = await this.getMedicines(userId);
    const now = new Date();
    const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const stats: MedicineStats = {
      total: medicines.length,
      expiringSoon: 0,
      lowStock: 0,
      expired: 0,
      byCategory: {
        prescription: 0,
        otc: 0,
        supplement: 0,
        other: 0,
      },
    };

    medicines.forEach((medicine) => {
      const expiryDate = new Date(medicine.expiryDate);

      if (expiryDate <= now) {
        stats.expired++;
      } else if (expiryDate <= thirtyDaysLater) {
        stats.expiringSoon++;
      }

      if (medicine.quantity <= medicine.minQuantity) {
        stats.lowStock++;
      }

      stats.byCategory[medicine.category]++;
    });

    return stats;
  }

  // 添加药品
  async addMedicine(userId: string, data: MedicineFormData): Promise<Medicine> {
    const newMedicine: Medicine = {
      id: Date.now().toString(),
      ...data,
      userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockMedicines.push(newMedicine);
    return newMedicine;
  }

  // 更新药品
  async updateMedicine(
    id: string,
    data: Partial<MedicineFormData>
  ): Promise<Medicine> {
    const index = mockMedicines.findIndex((m) => m.id === id);
    if (index === -1) {
      throw new Error("药品不存在");
    }

    mockMedicines[index] = {
      ...mockMedicines[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    return mockMedicines[index];
  }

  // 删除药品
  async deleteMedicine(id: string): Promise<void> {
    const index = mockMedicines.findIndex((m) => m.id === id);
    if (index === -1) {
      throw new Error("药品不存在");
    }

    mockMedicines.splice(index, 1);
  }

  // 删除药品（别名方法）
  async removeMedicine(id: string): Promise<void> {
    return this.deleteMedicine(id);
  }

  // 调整库存
  async adjustStock(adjustment: StockAdjustment): Promise<Medicine> {
    const medicine = mockMedicines.find((m) => m.id === adjustment.medicineId);
    if (!medicine) {
      throw new Error("药品不存在");
    }

    switch (adjustment.type) {
      case "add":
        medicine.quantity += adjustment.quantity;
        break;
      case "subtract":
        medicine.quantity = Math.max(
          0,
          medicine.quantity - adjustment.quantity
        );
        break;
      case "set":
        medicine.quantity = adjustment.quantity;
        break;
    }

    medicine.updatedAt = new Date().toISOString();
    return medicine;
  }

  // 导出药品清单
  async exportMedicineList(userId: string): Promise<string> {
    const medicines = await this.getMedicines(userId);
    const csvContent = [
      "药品名称,类别,规格,数量,单位,最小库存,有效期,生产厂家,批号,用法用量,储存条件,备注",
      ...medicines.map((m) =>
        [
          m.name,
          m.category,
          m.dosage,
          m.quantity,
          m.unit,
          m.minQuantity,
          m.expiryDate,
          m.manufacturer,
          m.batchNumber || "",
          m.instructions,
          m.storage,
          m.notes || "",
        ].join(",")
      ),
    ].join("\n");

    return csvContent;
  }
}

export const medicineService = new MedicineService();
export type { Medicine, MedicineStats, MedicineFilter, MedicineFormData };
