<template>
  <div class="page-container">
    <div class="page-content">
      <div class="smart-chat-container">
        <!-- 聊天界面 -->
        <div class="chat-main">
          <ContentCard class="chat-card">
            <!-- 聊天消息区域 -->
            <div class="chat-messages" ref="messagesContainer">
              <div
                v-for="message in messages"
                :key="message.id"
                class="message"
                :class="{
                  'user-message': message.sender === 'user',
                  'ai-message': message.sender === 'ai',
                }"
              >
                <div class="message-avatar">
                  <span v-if="message.sender === 'user'">👤</span>
                  <span v-else>🤖</span>
                </div>
                <div class="message-content">
                  <div class="message-text">{{ message.text }}</div>
                  <div class="message-time">{{ formatTime(message.time) }}</div>
                </div>
              </div>

              <!-- AI正在输入指示器 -->
              <div
                v-if="isAiTyping"
                class="message ai-message typing-indicator"
              >
                <div class="message-avatar">
                  <span>🤖</span>
                </div>
                <div class="message-content">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-area">
              <div class="input-wrapper">
                <textarea
                  v-model="currentMessage"
                  class="chat-input"
                  placeholder="请输入您的健康问题..."
                  rows="1"
                  @keydown.enter.prevent="sendMessage"
                  @input="adjustTextareaHeight"
                  ref="messageInput"
                ></textarea>
                <button
                  class="send-button"
                  @click="sendMessage"
                  :disabled="!currentMessage.trim() || isAiTyping"
                >
                  <span>发送</span>
                </button>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 侧边栏 -->
        <div class="chat-sidebar">
          <!-- 快速咨询 -->
          <ContentCard title="快速咨询" size="sm" class="quick-questions-card">
            <div class="quick-questions">
              <button
                v-for="question in quickQuestions"
                :key="question.id"
                class="quick-question-btn"
                @click="selectQuickQuestion(question.text)"
              >
                <span class="question-icon">{{ question.icon }}</span>
                <span class="question-text">{{ question.text }}</span>
              </button>
            </div>
          </ContentCard>

          <!-- 历史对话 -->
          <ContentCard title="历史对话" size="sm" class="history-card">
            <template #actions>
              <button
                class="btn btn-xs btn-outline"
                @click="showHistoryDialog = true"
              >
                查看全部
              </button>
            </template>

            <div class="chat-history">
              <div
                v-for="chat in recentChats"
                :key="chat.id"
                class="history-item"
                @click="loadHistoryChat(chat)"
              >
                <div class="history-title">{{ chat.title }}</div>
                <div class="history-time">{{ formatDate(chat.time) }}</div>
              </div>

              <div v-if="recentChats.length === 0" class="empty-history">
                暂无历史对话
              </div>
            </div>
          </ContentCard>

          <!-- 健康档案快捷访问 -->
          <ContentCard title="健康档案" size="sm" class="profile-card">
            <div class="profile-quick-access">
              <button class="profile-btn" @click="goToProfile">
                <span class="profile-icon">📋</span>
                <span class="profile-text">查看健康档案</span>
              </button>
              <button class="profile-btn" @click="goToReports">
                <span class="profile-icon">📊</span>
                <span class="profile-text">健康报告</span>
              </button>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

const router = useRouter();

// 聊天消息
const messages = ref([
  {
    id: 1,
    sender: "ai",
    text: "您好！我是您的AI健康助手，很高兴为您服务。请问有什么健康问题需要咨询吗？",
    time: new Date(Date.now() - 5 * 60 * 1000),
  },
]);

// 当前输入的消息
const currentMessage = ref("");

// AI是否正在输入
const isAiTyping = ref(false);

// 消息容器引用
const messagesContainer = ref(null);
const messageInput = ref(null);

// 历史对话显示对话框
const showHistoryDialog = ref(false);

// 快速咨询问题
const quickQuestions = ref([
  { id: 1, icon: "🩺", text: "血压偏高怎么办？" },
  { id: 2, icon: "💊", text: "忘记吃药了怎么办？" },
  { id: 3, icon: "🏃", text: "适合我的运动方案" },
  { id: 4, icon: "🥗", text: "健康饮食建议" },
  { id: 5, icon: "😴", text: "改善睡眠质量" },
  { id: 6, icon: "🧘", text: "压力管理方法" },
]);

// 最近聊天记录
const recentChats = ref([
  {
    id: 1,
    title: "血压管理咨询",
    time: new Date(Date.now() - 24 * 60 * 60 * 1000),
  },
  {
    id: 2,
    title: "运动计划制定",
    time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
  },
  {
    id: 3,
    title: "饮食营养建议",
    time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
  },
]);

// 发送消息
const sendMessage = async () => {
  if (!currentMessage.value.trim() || isAiTyping.value) return;

  const userMessage = {
    id: Date.now(),
    sender: "user",
    text: currentMessage.value.trim(),
    time: new Date(),
  };

  messages.value.push(userMessage);
  const messageText = currentMessage.value.trim();
  currentMessage.value = "";

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  // 模拟AI回复
  isAiTyping.value = true;

  setTimeout(async () => {
    const aiResponse = generateAIResponse(messageText);
    const aiMessage = {
      id: Date.now() + 1,
      sender: "ai",
      text: aiResponse,
      time: new Date(),
    };

    messages.value.push(aiMessage);
    isAiTyping.value = false;

    await nextTick();
    scrollToBottom();
  }, 1500);
};

// 生成AI回复（模拟）
const generateAIResponse = (userMessage: string) => {
  const responses = {
    血压: "根据您的情况，建议您：1. 定期监测血压；2. 减少钠盐摄入；3. 适量运动；4. 保持良好作息。如果血压持续偏高，建议及时就医。",
    运动: "为您推荐适合的运动方案：1. 每周3-4次有氧运动，如快走、游泳；2. 每次30-45分钟；3. 运动强度以微微出汗为宜；4. 运动前后要做好热身和拉伸。",
    饮食: "健康饮食建议：1. 多吃新鲜蔬菜水果；2. 选择优质蛋白质；3. 控制油盐糖摄入；4. 少食多餐，规律进食；5. 多喝水，少喝含糖饮料。",
    睡眠: "改善睡眠质量的方法：1. 保持规律作息时间；2. 睡前1小时避免使用电子设备；3. 创造舒适的睡眠环境；4. 避免睡前大量进食；5. 适当进行放松活动。",
  };

  for (const [keyword, response] of Object.entries(responses)) {
    if (userMessage.includes(keyword)) {
      return response;
    }
  }

  return "感谢您的咨询。基于您的问题，我建议您：1. 保持健康的生活方式；2. 定期进行健康检查；3. 如有不适及时就医。如需更详细的建议，请提供更多具体信息。";
};

// 选择快速问题
const selectQuickQuestion = (questionText: string) => {
  currentMessage.value = questionText;
  sendMessage();
};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 调整文本框高度
const adjustTextareaHeight = () => {
  if (messageInput.value) {
    messageInput.value.style.height = "auto";
    messageInput.value.style.height = messageInput.value.scrollHeight + "px";
  }
};

// 加载历史对话
const loadHistoryChat = (chat: any) => {
  console.log("加载历史对话:", chat.title);
  // 这里应该加载历史对话内容
};

// 跳转到健康档案
const goToProfile = () => {
  router.push("/archive/personal-info");
};

// 跳转到健康报告
const goToReports = () => {
  router.push("/assistant/health-report");
};

// 格式化时间
const formatTime = (time: Date) => {
  return time.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 格式化日期
const formatDate = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) {
    return "今天";
  } else if (days === 1) {
    return "昨天";
  } else {
    return `${days}天前`;
  }
};

// 组件挂载时滚动到底部
onMounted(() => {
  nextTick(() => {
    scrollToBottom();
  });
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.smart-chat-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--spacing-6);
  height: calc(100vh - 200px);
}

.chat-main {
  display: flex;
  flex-direction: column;
}

.chat-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  max-height: calc(100vh - 350px);
}

.message {
  display: flex;
  gap: var(--spacing-3);
  align-items: flex-start;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: var(--color-secondary);
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.user-message .message-content {
  align-items: flex-end;
}

.message-text {
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  line-height: var(--line-height-relaxed);
  word-wrap: break-word;
}

.user-message .message-text {
  background-color: var(--color-primary);
  color: white;
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  padding: 0 var(--spacing-2);
}

.typing-indicator .message-text {
  padding: var(--spacing-2) var(--spacing-4);
}

.typing-dots {
  display: flex;
  gap: var(--spacing-1);
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--theme-text-secondary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-area {
  border-top: 1px solid var(--theme-border);
  padding: var(--spacing-4);
}

.input-wrapper {
  display: flex;
  gap: var(--spacing-3);
  align-items: flex-end;
}

.chat-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-family: inherit;
  resize: none;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.chat-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.send-button {
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-normal);
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.chat-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.quick-questions-card,
.history-card,
.profile-card {
  flex-shrink: 0;
}

.quick-questions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.quick-question-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
}

.quick-question-btn:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.question-icon {
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.question-text {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.chat-history {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.history-item:hover {
  background-color: var(--theme-bg-secondary);
}

.history-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-1);
}

.history-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.empty-history {
  text-align: center;
  padding: var(--spacing-4);
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
}

.profile-quick-access {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
}

.profile-btn:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.profile-icon {
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.profile-text {
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-chat-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
    height: calc(100vh - 150px);
  }

  .chat-sidebar {
    order: -1;
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: var(--spacing-2);
  }

  .quick-questions-card,
  .history-card,
  .profile-card {
    min-width: 250px;
    flex-shrink: 0;
  }

  .chat-messages {
    max-height: calc(100vh - 400px);
  }

  .message-content {
    max-width: 85%;
  }
}
</style>
