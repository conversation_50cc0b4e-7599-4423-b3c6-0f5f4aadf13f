<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加药品"
    width="600px"
    :before-close="handleClose"
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="药品名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入药品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="药品类别" prop="category">
            <el-select
              v-model="form.category"
              placeholder="请选择药品类别"
              style="width: 100%"
            >
              <el-option label="处方药" value="prescription" />
              <el-option label="非处方药" value="otc" />
              <el-option label="保健品" value="supplement" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规格" prop="dosage">
            <el-input v-model="form.dosage" placeholder="如：250mg" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-select
              v-model="form.unit"
              placeholder="请选择单位"
              style="width: 100%"
            >
              <el-option label="片" value="片" />
              <el-option label="粒" value="粒" />
              <el-option label="袋" value="袋" />
              <el-option label="瓶" value="瓶" />
              <el-option label="盒" value="盒" />
              <el-option label="支" value="支" />
              <el-option label="ml" value="ml" />
              <el-option label="g" value="g" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number
              v-model="form.quantity"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最小库存" prop="minQuantity">
            <el-input-number
              v-model="form.minQuantity"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效期" prop="expiryDate">
            <el-date-picker
              v-model="form.expiryDate"
              type="date"
              placeholder="选择有效期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产厂家" prop="manufacturer">
            <el-input
              v-model="form.manufacturer"
              placeholder="请输入生产厂家"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="批号">
            <el-input v-model="form.batchNumber" placeholder="请输入批号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="价格">
            <el-input-number
              v-model="form.price"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="购买日期">
            <el-date-picker
              v-model="form.purchaseDate"
              type="date"
              placeholder="选择购买日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存放位置">
            <el-input v-model="form.location" placeholder="如：卧室药箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="用法用量" prop="instructions">
        <el-input
          v-model="form.instructions"
          type="textarea"
          :rows="2"
          placeholder="请输入用法用量"
        />
      </el-form-item>

      <el-form-item label="副作用">
        <el-input
          v-model="form.sideEffects"
          type="textarea"
          :rows="2"
          placeholder="请输入副作用信息"
        />
      </el-form-item>

      <el-form-item label="禁忌症">
        <el-input
          v-model="form.contraindications"
          type="textarea"
          :rows="2"
          placeholder="请输入禁忌症信息"
        />
      </el-form-item>

      <el-form-item label="储存条件" prop="storage">
        <el-input v-model="form.storage" placeholder="如：密封，在干燥处保存" />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";

import type { MedicineFormData } from "@/types/medicine";

interface Props {
  modelValue: boolean;
  type?: "personal" | "family";
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success", data: MedicineFormData): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: "personal",
});

const emit = defineEmits<Emits>();

const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

const form = reactive<MedicineFormData>({
  name: "",
  category: "otc",
  dosage: "",
  unit: "片",
  quantity: 0,
  minQuantity: 0,
  expiryDate: "",
  manufacturer: "",
  batchNumber: "",
  instructions: "",
  sideEffects: "",
  contraindications: "",
  storage: "",
  price: 0,
  purchaseDate: "",
  location: "",
  notes: "",
});

const rules: FormRules = {
  name: [{ required: true, message: "请输入药品名称", trigger: "blur" }],
  category: [{ required: true, message: "请选择药品类别", trigger: "change" }],
  dosage: [{ required: true, message: "请输入规格", trigger: "blur" }],
  unit: [{ required: true, message: "请选择单位", trigger: "change" }],
  quantity: [
    { required: true, message: "请输入数量", trigger: "blur" },
    { type: "number", min: 0, message: "数量不能小于0", trigger: "blur" },
  ],
  minQuantity: [
    { required: true, message: "请输入最小库存", trigger: "blur" },
    { type: "number", min: 0, message: "最小库存不能小于0", trigger: "blur" },
  ],
  expiryDate: [{ required: true, message: "请选择有效期", trigger: "change" }],
  manufacturer: [
    { required: true, message: "请输入生产厂家", trigger: "blur" },
  ],
  instructions: [
    { required: true, message: "请输入用法用量", trigger: "blur" },
  ],
  storage: [{ required: true, message: "请输入储存条件", trigger: "blur" }],
};

const resetForm = () => {
  Object.assign(form, {
    name: "",
    category: "otc",
    dosage: "",
    unit: "片",
    quantity: 0,
    minQuantity: 0,
    expiryDate: "",
    manufacturer: "",
    batchNumber: "",
    instructions: "",
    sideEffects: "",
    contraindications: "",
    storage: "",
    price: 0,
    purchaseDate: "",
    location: "",
    notes: "",
  });
  formRef.value?.clearValidate();
};

const handleClose = () => {
  emit("update:modelValue", false);
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    emit("success", { ...form });
    ElMessage.success("添加药品成功");
    handleClose();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, (val) => {
  emit("update:modelValue", val);
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
