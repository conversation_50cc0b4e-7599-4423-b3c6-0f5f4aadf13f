/* 通用样式 */

/* ===== 布局工具类 ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-4);
}

/* 页面容器 - 统一所有页面的左右间隔 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

/* Flexbox 工具类 */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}
.flex-auto {
  flex: auto;
}
.flex-none {
  flex: none;
}

/* Grid 工具类 */
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}
.grid-cols-6 {
  grid-template-columns: repeat(6, 1fr);
}
.grid-cols-12 {
  grid-template-columns: repeat(12, 1fr);
}

.gap-1 {
  gap: var(--spacing-1);
}
.gap-2 {
  gap: var(--spacing-2);
}
.gap-3 {
  gap: var(--spacing-3);
}
.gap-4 {
  gap: var(--spacing-4);
}
.gap-6 {
  gap: var(--spacing-6);
}
.gap-8 {
  gap: var(--spacing-8);
}

/* ===== 间距工具类 ===== */
.m-0 {
  margin: 0;
}
.m-1 {
  margin: var(--spacing-1);
}
.m-2 {
  margin: var(--spacing-2);
}
.m-3 {
  margin: var(--spacing-3);
}
.m-4 {
  margin: var(--spacing-4);
}
.m-6 {
  margin: var(--spacing-6);
}
.m-8 {
  margin: var(--spacing-8);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-1);
}
.mt-2 {
  margin-top: var(--spacing-2);
}
.mt-3 {
  margin-top: var(--spacing-3);
}
.mt-4 {
  margin-top: var(--spacing-4);
}
.mt-6 {
  margin-top: var(--spacing-6);
}
.mt-8 {
  margin-top: var(--spacing-8);
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-1);
}
.mb-2 {
  margin-bottom: var(--spacing-2);
}
.mb-3 {
  margin-bottom: var(--spacing-3);
}
.mb-4 {
  margin-bottom: var(--spacing-4);
}
.mb-6 {
  margin-bottom: var(--spacing-6);
}
.mb-8 {
  margin-bottom: var(--spacing-8);
}

.ml-0 {
  margin-left: 0;
}
.ml-1 {
  margin-left: var(--spacing-1);
}
.ml-2 {
  margin-left: var(--spacing-2);
}
.ml-3 {
  margin-left: var(--spacing-3);
}
.ml-4 {
  margin-left: var(--spacing-4);
}

.mr-0 {
  margin-right: 0;
}
.mr-1 {
  margin-right: var(--spacing-1);
}
.mr-2 {
  margin-right: var(--spacing-2);
}
.mr-3 {
  margin-right: var(--spacing-3);
}
.mr-4 {
  margin-right: var(--spacing-4);
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: var(--spacing-1);
}
.p-2 {
  padding: var(--spacing-2);
}
.p-3 {
  padding: var(--spacing-3);
}
.p-4 {
  padding: var(--spacing-4);
}
.p-6 {
  padding: var(--spacing-6);
}
.p-8 {
  padding: var(--spacing-8);
}

.pt-0 {
  padding-top: 0;
}
.pt-1 {
  padding-top: var(--spacing-1);
}
.pt-2 {
  padding-top: var(--spacing-2);
}
.pt-3 {
  padding-top: var(--spacing-3);
}
.pt-4 {
  padding-top: var(--spacing-4);
}
.pt-6 {
  padding-top: var(--spacing-6);
}

.pb-0 {
  padding-bottom: 0;
}
.pb-1 {
  padding-bottom: var(--spacing-1);
}
.pb-2 {
  padding-bottom: var(--spacing-2);
}
.pb-3 {
  padding-bottom: var(--spacing-3);
}
.pb-4 {
  padding-bottom: var(--spacing-4);
}
.pb-6 {
  padding-bottom: var(--spacing-6);
}

.pl-0 {
  padding-left: 0;
}
.pl-1 {
  padding-left: var(--spacing-1);
}
.pl-2 {
  padding-left: var(--spacing-2);
}
.pl-3 {
  padding-left: var(--spacing-3);
}
.pl-4 {
  padding-left: var(--spacing-4);
}

.pr-0 {
  padding-right: 0;
}
.pr-1 {
  padding-right: var(--spacing-1);
}
.pr-2 {
  padding-right: var(--spacing-2);
}
.pr-3 {
  padding-right: var(--spacing-3);
}
.pr-4 {
  padding-right: var(--spacing-4);
}

/* ===== 文字工具类 ===== */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}

.font-light {
  font-weight: var(--font-weight-light);
}
.font-normal {
  font-weight: var(--font-weight-normal);
}
.font-medium {
  font-weight: var(--font-weight-medium);
}
.font-semibold {
  font-weight: var(--font-weight-semibold);
}
.font-bold {
  font-weight: var(--font-weight-bold);
}

.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.text-primary {
  color: var(--theme-text-primary);
}
.text-secondary {
  color: var(--theme-text-secondary);
}
.text-success {
  color: var(--color-success);
}
.text-warning {
  color: var(--color-warning);
}
.text-danger {
  color: var(--color-danger);
}
.text-info {
  color: var(--color-info);
}

/* ===== 背景工具类 ===== */
.bg-white {
  background-color: var(--color-white);
}
.bg-gray-50 {
  background-color: var(--color-gray-50);
}
.bg-gray-100 {
  background-color: var(--color-gray-100);
}
.bg-primary {
  background-color: var(--color-primary);
}
.bg-success {
  background-color: var(--color-success);
}
.bg-warning {
  background-color: var(--color-warning);
}
.bg-danger {
  background-color: var(--color-danger);
}
.bg-info {
  background-color: var(--color-info);
}

/* ===== 边框工具类 ===== */
.border {
  border: 1px solid var(--theme-border);
}
.border-t {
  border-top: 1px solid var(--theme-border);
}
.border-b {
  border-bottom: 1px solid var(--theme-border);
}
.border-l {
  border-left: 1px solid var(--theme-border);
}
.border-r {
  border-right: 1px solid var(--theme-border);
}

.rounded-none {
  border-radius: var(--border-radius-none);
}
.rounded-sm {
  border-radius: var(--border-radius-sm);
}
.rounded-md {
  border-radius: var(--border-radius-md);
}
.rounded-lg {
  border-radius: var(--border-radius-lg);
}
.rounded-xl {
  border-radius: var(--border-radius-xl);
}
.rounded-full {
  border-radius: var(--border-radius-full);
}

/* ===== 阴影工具类 ===== */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}
.shadow-md {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}
.shadow-xl {
  box-shadow: var(--shadow-xl);
}
.shadow-none {
  box-shadow: none;
}

/* ===== 显示工具类 ===== */
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.hidden {
  display: none;
}

/* ===== 位置工具类 ===== */
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.sticky {
  position: sticky;
}

/* ===== 响应式工具类 ===== */
@media (min-width: 640px) {
  .sm\:block {
    display: block;
  }
  .sm\:hidden {
    display: none;
  }
  .sm\:flex {
    display: flex;
  }
  .sm\:grid {
    display: grid;
  }
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:hidden {
    display: none;
  }
  .md\:flex {
    display: flex;
  }
  .md\:grid {
    display: grid;
  }
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:grid {
    display: grid;
  }
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}
