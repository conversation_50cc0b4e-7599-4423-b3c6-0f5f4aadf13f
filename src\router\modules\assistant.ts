import type { RouteRecordRaw } from 'vue-router';

// 智能助手模块路由
export const assistantRoutes: RouteRecordRaw[] = [
  {
    path: '/assistant',
    name: 'Assistant',
    component: () => import('@/views/assistant/Assistant.vue'),
    meta: {
      title: '助手 - 家庭健康管理系统',
      icon: 'ChatDotRound',
      order: 4
    }
  },
  {
    path: '/assistant/smart-chat',
    name: 'SmartChat',
    component: () => import('@/views/assistant/SmartChat.vue'),
    meta: {
      title: '智能对话 - 家庭健康管理系统',
      parent: 'Assistant',
      breadcrumb: [
        { title: '助手', path: '/assistant' },
        { title: '智能对话' }
      ]
    }
  },
  {
    path: '/assistant/health-analysis',
    name: 'HealthAnalysis',
    component: () => import('@/views/assistant/HealthAnalysis.vue'),
    meta: {
      title: '健康分析 - 家庭健康管理系统',
      parent: 'Assistant',
      breadcrumb: [
        { title: '助手', path: '/assistant' },
        { title: '健康分析' }
      ]
    }
  },
  {
    path: '/assistant/health-report',
    name: 'HealthReport',
    component: () => import('@/views/assistant/HealthReport.vue'),
    meta: {
      title: '健康报告 - 家庭健康管理系统',
      parent: 'Assistant',
      breadcrumb: [
        { title: '助手', path: '/assistant' },
        { title: '健康报告' }
      ]
    }
  },
  {
    path: '/assistant/health-suggestions',
    name: 'HealthSuggestions',
    component: () => import('@/views/assistant/HealthSuggestions.vue'),
    meta: {
      title: '健康建议 - 家庭健康管理系统',
      parent: 'Assistant',
      breadcrumb: [
        { title: '助手', path: '/assistant' },
        { title: '健康建议' }
      ]
    }
  },
  {
    path: '/assistant/health-prediction',
    name: 'HealthPrediction',
    component: () => import('@/views/assistant/HealthPrediction.vue'),
    meta: {
      title: '健康预测 - 家庭健康管理系统',
      parent: 'Assistant',
      breadcrumb: [
        { title: '助手', path: '/assistant' },
        { title: '健康预测' }
      ]
    }
  }
];
