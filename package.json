{"name": "family-health-management", "private": true, "version": "1.0.0", "description": "家庭健康管理系统 - 基于Vue 3 + TypeScript + Vite的现代化健康管理平台", "type": "module", "keywords": ["vue3", "typescript", "vite", "health-management", "family-health", "echarts", "element-plus"], "author": "Family Health Management Team", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.6.0", "element-plus": "^2.4.0", "lodash": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.2.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.15.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^8.56.0", "typescript": "~5.8.3", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}