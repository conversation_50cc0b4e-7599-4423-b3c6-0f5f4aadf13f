<template>
  <div class="page-container">
    <!-- 语言和显示设置 -->
    <div class="settings-section">
      <h2 class="section-title">语言和显示</h2>
      <ContentCard>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">界面语言</h4>
              <p class="setting-description">选择您偏好的界面语言</p>
            </div>
            <select v-model="settings.language" class="form-select">
              <option value="zh-CN">简体中文</option>
              <option value="zh-TW">繁体中文</option>
              <option value="en-US">English</option>
              <option value="ja-JP">日本語</option>
            </select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">字体大小</h4>
              <p class="setting-description">调整应用内字体大小</p>
            </div>
            <select v-model="settings.fontSize" class="form-select">
              <option value="small">小</option>
              <option value="medium">中</option>
              <option value="large">大</option>
              <option value="extra-large">特大</option>
            </select>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">深色模式</h4>
              <p class="setting-description">切换深色/浅色主题</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.darkMode" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">高对比度</h4>
              <p class="setting-description">提高文字和背景对比度</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.highContrast" />
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 无障碍功能 -->
    <div class="settings-section">
      <h2 class="section-title">无障碍功能</h2>
      <ContentCard>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">屏幕阅读器支持</h4>
              <p class="setting-description">启用语音朗读功能</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.screenReader" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">语音助手</h4>
              <p class="setting-description">启用语音控制和反馈</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.voiceAssistant" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">声音提示</h4>
              <p class="setting-description">操作时播放提示音</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.soundEffects" />
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 通知设置 -->
    <div class="settings-section">
      <h2 class="section-title">通知设置</h2>
      <ContentCard>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">推送通知</h4>
              <p class="setting-description">接收健康提醒和系统通知</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.pushNotifications" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">用药提醒</h4>
              <p class="setting-description">接收用药时间提醒</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.medicationReminders" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">健康报告</h4>
              <p class="setting-description">接收AI生成的健康报告通知</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.healthReports" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">紧急警报</h4>
              <p class="setting-description">接收紧急健康警报</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.emergencyAlerts" />
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 隐私和安全 -->
    <div class="settings-section">
      <h2 class="section-title">隐私和安全</h2>
      <ContentCard>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">生物识别认证</h4>
              <p class="setting-description">使用指纹或面部识别登录</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.biometricAuth" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">数据同步</h4>
              <p class="setting-description">在不同设备间同步健康数据</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.dataSync" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">自动备份</h4>
              <p class="setting-description">定期将数据备份到云端</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="settings.autoBackup" />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">隐私政策</h4>
              <p class="setting-description">查看数据使用协议</p>
            </div>
            <button class="btn btn-outline" @click="viewPrivacyPolicy">
              查看详情
            </button>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 存储使用 -->
    <div class="settings-section">
      <h2 class="section-title">存储使用</h2>
      <ContentCard>
        <div class="storage-overview">
          <div class="storage-info">
            <h4 class="storage-title">存储概览</h4>
            <p class="storage-usage">
              已使用 {{ storageInfo.used }}GB / 总共 {{ storageInfo.total }}GB
            </p>
          </div>
          <div class="storage-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${storageInfo.percentage}%` }"
              ></div>
            </div>
            <span class="progress-text">{{ storageInfo.percentage }}%</span>
          </div>
        </div>

        <div class="storage-breakdown">
          <div
            class="breakdown-item"
            v-for="item in storageBreakdown"
            :key="item.type"
          >
            <div class="breakdown-info">
              <span class="breakdown-label">{{ item.label }}</span>
              <span class="breakdown-size">{{ item.size }}GB</span>
            </div>
            <div class="breakdown-bar">
              <div
                class="breakdown-fill"
                :style="{
                  width: `${item.percentage}%`,
                  backgroundColor: item.color,
                }"
              ></div>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 数据管理 -->
    <div class="settings-section">
      <h2 class="section-title">数据管理</h2>
      <ContentCard>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">导出数据</h4>
              <p class="setting-description">导出个人健康数据和设置信息</p>
            </div>
            <button class="btn btn-primary" @click="exportData">
              导出数据
            </button>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">清除缓存</h4>
              <p class="setting-description">清理应用缓存，优化性能</p>
            </div>
            <button class="btn btn-secondary" @click="clearCache">
              清除缓存
            </button>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">重置设置</h4>
              <p class="setting-description">将所有设置恢复到默认状态</p>
            </div>
            <button class="btn btn-danger" @click="resetSettings">
              重置设置
            </button>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 应用信息 -->
    <div class="settings-section">
      <h2 class="section-title">应用信息</h2>
      <ContentCard>
        <div class="app-info">
          <div class="info-item">
            <span class="info-label">版本号</span>
            <span class="info-value">v1.0.0</span>
          </div>
          <div class="info-item">
            <span class="info-label">构建号</span>
            <span class="info-value">2024.01.15</span>
          </div>
          <div class="info-item">
            <span class="info-label">最后更新</span>
            <span class="info-value">2024年1月15日</span>
          </div>
        </div>

        <div class="app-actions">
          <button class="btn btn-outline" @click="checkUpdate">检查更新</button>
          <button class="btn btn-outline" @click="showFeedback">
            用户反馈
          </button>
          <button class="btn btn-outline" @click="showAbout">关于我们</button>
        </div>
      </ContentCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 设置数据
const settings = ref({
  language: "zh-CN",
  fontSize: "medium",
  darkMode: false,
  highContrast: false,
  screenReader: false,
  voiceAssistant: false,
  soundEffects: true,
  pushNotifications: true,
  medicationReminders: true,
  healthReports: true,
  emergencyAlerts: true,
  biometricAuth: false,
  dataSync: true,
  autoBackup: true,
});

// 存储信息
const storageInfo = ref({
  used: 2.3,
  total: 10,
  percentage: 23,
});

// 存储分类
const storageBreakdown = ref([
  {
    type: "health-data",
    label: "健康数据",
    size: 1.2,
    percentage: 52,
    color: "#3B82F6",
  },
  {
    type: "images",
    label: "图片文件",
    size: 0.8,
    percentage: 35,
    color: "#10B981",
  },
  {
    type: "reports",
    label: "报告文档",
    size: 0.3,
    percentage: 13,
    color: "#F59E0B",
  },
]);

// 查看隐私政策
const viewPrivacyPolicy = () => {
  console.log("查看隐私政策");
};

// 导出数据
const exportData = () => {
  console.log("导出数据");
};

// 清除缓存
const clearCache = () => {
  console.log("清除缓存");
};

// 重置设置
const resetSettings = () => {
  console.log("重置设置");
};

// 检查更新
const checkUpdate = () => {
  console.log("检查更新");
};

// 用户反馈
const showFeedback = () => {
  console.log("用户反馈");
};

// 关于我们
const showAbout = () => {
  console.log("关于我们");
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.settings-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

.settings-section {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.settings-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.setting-item:hover {
  background-color: var(--theme-bg-secondary);
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.setting-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.form-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-300);
  transition: var(--transition-normal);
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-normal);
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--color-primary);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 存储使用样式 */
.storage-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.storage-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.storage-usage {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.storage-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-bar {
  width: 200px;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  min-width: 40px;
}

.storage-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.breakdown-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 150px;
}

.breakdown-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
}

.breakdown-size {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.breakdown-bar {
  flex: 1;
  height: 6px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.breakdown-fill {
  height: 100%;
  transition: width var(--transition-normal);
}

/* 应用信息样式 */
.app-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--theme-border);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.info-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.app-actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-3);
  }

  .storage-overview {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-4);
  }

  .storage-progress {
    justify-content: space-between;
  }

  .progress-bar {
    flex: 1;
    max-width: none;
  }

  .breakdown-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .breakdown-info {
    min-width: auto;
  }

  .app-actions {
    flex-direction: column;
  }
}
</style>
