<template>
  <div class="page-container">
    <!-- 成员选择器 -->
    <div class="member-selector">
      <ContentCard title="选择家庭成员" size="sm">
        <div class="member-tabs">
          <button
            v-for="member in familyMembers"
            :key="member.id"
            class="member-tab"
            :class="{ active: selectedMember?.id === member.id }"
            @click="selectMember(member)"
          >
            <div class="member-avatar">
              <span>{{ member.name.charAt(0) }}</span>
            </div>
            <span class="member-name">{{ member.name }}</span>
          </button>
        </div>
      </ContentCard>
    </div>

    <!-- 个人信息内容 -->
    <div v-if="selectedMember" class="personal-info-content">
      <!-- 标签页导航 -->
      <div class="tabs-navigation">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-button"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-text">{{ tab.title }}</span>
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 基本信息 -->
        <div v-if="activeTab === 'basic'" class="tab-panel">
          <ContentCard title="基本信息" subtitle="个人基本资料">
            <template #actions>
              <button class="btn btn-primary" @click="editBasicInfo">
                编辑信息
              </button>
            </template>

            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">姓名</span>
                <span class="info-value">{{ selectedMember.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">性别</span>
                <span class="info-value">{{ selectedMember.gender }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">出生日期</span>
                <span class="info-value">{{
                  formatDate(selectedMember.birthday)
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">年龄</span>
                <span class="info-value">{{ selectedMember.age }}岁</span>
              </div>
              <div class="info-item">
                <span class="info-label">血型</span>
                <span class="info-value">{{
                  selectedMember.bloodType || "未知"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">联系电话</span>
                <span class="info-value">{{
                  selectedMember.phone || "未填写"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">身份证号</span>
                <span class="info-value">{{
                  selectedMember.idCard || "未填写"
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">家庭关系</span>
                <span class="info-value">{{
                  selectedMember.relationship
                }}</span>
              </div>
            </div>

            <!-- 过敏史 -->
            <div class="section-divider"></div>
            <h4 class="section-title">过敏史</h4>
            <div class="allergies-list">
              <div
                v-for="allergy in selectedMember.allergies"
                :key="allergy"
                class="allergy-tag"
              >
                {{ allergy }}
              </div>
              <div
                v-if="selectedMember.allergies.length === 0"
                class="empty-state"
              >
                暂无过敏史记录
              </div>
            </div>

            <!-- 既往病史 -->
            <div class="section-divider"></div>
            <h4 class="section-title">既往病史</h4>
            <div class="medical-history">
              <div
                v-for="history in selectedMember.medicalHistory"
                :key="history.id"
                class="history-item"
              >
                <div class="history-header">
                  <h5 class="history-title">{{ history.disease }}</h5>
                  <span class="history-date">{{
                    formatDate(history.date)
                  }}</span>
                </div>
                <p class="history-description">{{ history.description }}</p>
              </div>
              <div
                v-if="selectedMember.medicalHistory.length === 0"
                class="empty-state"
              >
                暂无既往病史记录
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 医疗信息 -->
        <div v-if="activeTab === 'medical'" class="tab-panel">
          <ContentCard title="医疗信息" subtitle="就诊记录和医疗档案">
            <template #actions>
              <button class="btn btn-primary" @click="addMedicalRecord">
                添加记录
              </button>
            </template>

            <div class="medical-records">
              <div
                v-for="record in selectedMember.medicalRecords"
                :key="record.id"
                class="medical-record-item"
              >
                <div class="record-header">
                  <div class="record-info">
                    <h4 class="record-title">{{ record.title }}</h4>
                    <p class="record-hospital">{{ record.hospital }}</p>
                  </div>
                  <div class="record-meta">
                    <span class="record-date">{{
                      formatDate(record.date)
                    }}</span>
                    <span class="record-type">{{ record.type }}</span>
                  </div>
                </div>
                <div class="record-content">
                  <p class="record-diagnosis">诊断：{{ record.diagnosis }}</p>
                  <p class="record-treatment">治疗：{{ record.treatment }}</p>
                  <div
                    v-if="record.attachments.length > 0"
                    class="record-attachments"
                  >
                    <h5>相关文件：</h5>
                    <div class="attachments-list">
                      <div
                        v-for="attachment in record.attachments"
                        :key="attachment.id"
                        class="attachment-item"
                      >
                        <span class="attachment-icon">📄</span>
                        <span class="attachment-name">{{
                          attachment.name
                        }}</span>
                        <button class="attachment-view">查看</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="selectedMember.medicalRecords.length === 0"
                class="empty-state"
              >
                暂无医疗记录
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 紧急联系人 -->
        <div v-if="activeTab === 'emergency'" class="tab-panel">
          <ContentCard title="紧急联系人" subtitle="紧急情况下的联系人信息">
            <template #actions>
              <button class="btn btn-primary" @click="addEmergencyContact">
                添加联系人
              </button>
            </template>

            <div class="emergency-contacts">
              <div
                v-for="contact in selectedMember.emergencyContacts"
                :key="contact.id"
                class="contact-item"
              >
                <div class="contact-avatar">
                  <span>{{ contact.name.charAt(0) }}</span>
                </div>
                <div class="contact-info">
                  <h4 class="contact-name">{{ contact.name }}</h4>
                  <p class="contact-relationship">{{ contact.relationship }}</p>
                  <p class="contact-phone">{{ contact.phone }}</p>
                </div>
                <div class="contact-actions">
                  <button class="btn btn-sm btn-outline">编辑</button>
                  <button class="btn btn-sm btn-danger">删除</button>
                </div>
              </div>
              <div
                v-if="selectedMember.emergencyContacts.length === 0"
                class="empty-state"
              >
                暂无紧急联系人
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 健康趋势 -->
        <div v-if="activeTab === 'trends'" class="tab-panel">
          <ContentCard title="健康趋势" subtitle="健康指标变化趋势和AI分析">
            <!-- 健康指标卡片 -->
            <div class="health-metrics">
              <StatCard
                v-for="metric in selectedMember.healthMetrics"
                :key="metric.id"
                :title="metric.name"
                :value="metric.value"
                :unit="metric.unit"
                :status="metric.status"
                :trend="metric.trend"
                :icon="metric.icon"
                :icon-color="metric.iconColor"
                :icon-bg-color="metric.iconBgColor"
                size="md"
              />
            </div>

            <!-- AI健康分析 -->
            <div class="ai-analysis-section">
              <h4 class="section-title">AI健康分析</h4>
              <div class="analysis-card">
                <div class="analysis-header">
                  <span class="analysis-icon">🤖</span>
                  <h5 class="analysis-title">智能健康评估</h5>
                  <span class="analysis-date">{{
                    formatDate(new Date())
                  }}</span>
                </div>
                <div class="analysis-content">
                  <p class="analysis-summary">
                    基于最近30天的健康数据分析，{{
                      selectedMember.name
                    }}的整体健康状况良好。
                    血压控制稳定，建议继续保持规律作息和适量运动。
                  </p>
                  <div class="analysis-recommendations">
                    <h6>个性化建议：</h6>
                    <ul>
                      <li>建议每周进行3-4次中等强度有氧运动</li>
                      <li>注意控制钠盐摄入，多食用富含钾的食物</li>
                      <li>保持规律作息，确保充足睡眠</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- 健康趋势图表占位符 -->
            <div class="trends-chart">
              <h4 class="section-title">健康趋势图表</h4>
              <div class="chart-placeholder">
                <p>健康趋势图表将在这里显示</p>
                <small>（图表组件待实现）</small>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>

    <!-- 未选择成员时的提示 -->
    <div v-else class="no-member-selected">
      <ContentCard>
        <div class="empty-state-large">
          <div class="empty-icon">👤</div>
          <h3>请选择家庭成员</h3>
          <p>选择一个家庭成员来查看其个人信息和健康档案</p>
        </div>
      </ContentCard>
    </div>

    <!-- 编辑基本信息模态框 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑基本信息</h3>
          <button class="modal-close" @click="closeEditModal">
            <Close />
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="saveBasicInfo">
            <div class="form-grid">
              <div class="form-group">
                <label for="name">姓名</label>
                <input
                  id="name"
                  v-model="editForm.name"
                  type="text"
                  class="form-input"
                  required
                />
              </div>

              <div class="form-group">
                <label for="gender">性别</label>
                <select
                  id="gender"
                  v-model="editForm.gender"
                  class="form-select"
                >
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>

              <div class="form-group">
                <label for="birthday">出生日期</label>
                <input
                  id="birthday"
                  v-model="editForm.birthday"
                  type="date"
                  class="form-input"
                />
              </div>

              <div class="form-group">
                <label for="bloodType">血型</label>
                <select
                  id="bloodType"
                  v-model="editForm.bloodType"
                  class="form-select"
                >
                  <option value="">请选择</option>
                  <option value="A">A型</option>
                  <option value="B">B型</option>
                  <option value="AB">AB型</option>
                  <option value="O">O型</option>
                </select>
              </div>

              <div class="form-group">
                <label for="phone">联系电话</label>
                <input
                  id="phone"
                  v-model="editForm.phone"
                  type="tel"
                  class="form-input"
                />
              </div>

              <div class="form-group">
                <label for="email">邮箱地址</label>
                <input
                  id="email"
                  v-model="editForm.email"
                  type="email"
                  class="form-input"
                />
              </div>
            </div>

            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                @click="closeEditModal"
              >
                取消
              </button>
              <button type="submit" class="btn btn-primary">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { Close } from "@element-plus/icons-vue";

// 模拟数据
const currentUser = {
  id: 1,
  name: "张先生",
  gender: "男",
  birthday: new Date("1978-05-15"),
  age: 45,
  bloodType: "A型",
  phone: "138****8888",
  email: "<EMAIL>",
  idCard: "110101********1234",
  relationship: "本人",
  allergies: ["青霉素", "海鲜"],
  medicalHistory: [
    {
      id: 1,
      disease: "高血压",
      date: new Date("2020-03-15"),
      description: "轻度高血压，需要定期监测和药物控制",
    },
  ],
  medicalRecords: [
    {
      id: 1,
      title: "年度体检",
      hospital: "市人民医院",
      date: new Date("2024-01-15"),
      type: "体检",
      diagnosis: "整体健康状况良好",
      treatment: "建议定期复查血压",
      attachments: [
        { id: 1, name: "体检报告.pdf" },
        { id: 2, name: "血液检查结果.pdf" },
      ],
    },
  ],
  emergencyContacts: [
    {
      id: 1,
      name: "李女士",
      relationship: "配偶",
      phone: "139****9999",
    },
  ],
  healthMetrics: [
    {
      id: 1,
      name: "血压",
      value: "120/80",
      unit: "mmHg",
      status: "normal",
      trend: { direction: "stable", value: 0, period: "本周" },
      icon: "Heart",
      iconColor: "#EF4444",
      iconBgColor: "#FEE2E2",
    },
    {
      id: 2,
      name: "血糖",
      value: "5.6",
      unit: "mmol/L",
      status: "normal",
      trend: { direction: "down", value: 3, period: "本周" },
      icon: "Drop",
      iconColor: "#3B82F6",
      iconBgColor: "#DBEAFE",
    },
  ],
};

// familyMembers 将在下面定义为 ref

const route = useRoute();

// 当前选中的成员
const selectedMember = ref(currentUser);

// 当前激活的标签页
const activeTab = ref("basic");

// 编辑模态框状态
const showEditModal = ref(false);

// 编辑表单数据
const editForm = ref({
  name: "",
  gender: "",
  birthday: "",
  bloodType: "",
  phone: "",
  email: "",
});

// 标签页配置
const tabs = ref([
  { key: "basic", title: "基本信息", icon: "👤" },
  { key: "medical", title: "医疗信息", icon: "🏥" },
  { key: "emergency", title: "紧急联系人", icon: "📞" },
  { key: "trends", title: "健康趋势", icon: "📈" },
]);

// 家庭成员数据
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    gender: "男",
    birthday: new Date("1978-05-15"),
    age: 45,
    bloodType: "A型",
    phone: "138****8888",
    idCard: "110101********1234",
    relationship: "本人",
    allergies: ["青霉素", "海鲜"],
    medicalHistory: [
      {
        id: 1,
        disease: "高血压",
        date: new Date("2020-03-15"),
        description: "轻度高血压，需要定期监测和药物控制",
      },
    ],
    medicalRecords: [
      {
        id: 1,
        title: "年度体检",
        hospital: "市人民医院",
        date: new Date("2024-01-15"),
        type: "体检",
        diagnosis: "整体健康状况良好",
        treatment: "建议定期复查血压",
        attachments: [
          { id: 1, name: "体检报告.pdf" },
          { id: 2, name: "血液检查结果.pdf" },
        ],
      },
    ],
    emergencyContacts: [
      {
        id: 1,
        name: "李女士",
        relationship: "配偶",
        phone: "139****9999",
      },
    ],
    healthMetrics: [
      {
        id: 1,
        name: "血压",
        value: "120/80",
        unit: "mmHg",
        status: "normal",
        trend: { direction: "stable", value: 0, period: "本周" },
        icon: "Heart",
        iconColor: "#EF4444",
        iconBgColor: "#FEE2E2",
      },
      {
        id: 2,
        name: "血糖",
        value: "5.6",
        unit: "mmol/L",
        status: "normal",
        trend: { direction: "down", value: 3, period: "本周" },
        icon: "Drop",
        iconColor: "#3B82F6",
        iconBgColor: "#DBEAFE",
      },
    ],
  },
  {
    id: 2,
    name: "李女士",
    gender: "女",
    birthday: new Date("1982-08-20"),
    age: 42,
    bloodType: "B型",
    phone: "139****9999",
    email: "<EMAIL>",
    idCard: "110101********5678",
    relationship: "配偶",
    allergies: [],
    medicalHistory: [],
    medicalRecords: [],
    emergencyContacts: [
      {
        id: 1,
        name: "张先生",
        relationship: "配偶",
        phone: "138****8888",
      },
    ],
    healthMetrics: [
      {
        id: 1,
        name: "血压",
        value: "115/75",
        unit: "mmHg",
        status: "normal",
        trend: { direction: "stable", value: 0, period: "本周" },
        icon: "Heart",
        iconColor: "#EF4444",
        iconBgColor: "#FEE2E2",
      },
    ],
  },
]);

// 选择成员
const selectMember = (member: any) => {
  selectedMember.value = member;
};

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期为输入框格式
const formatDateForInput = (date: Date) => {
  return date.toISOString().split("T")[0];
};

// 计算年龄
const calculateAge = (birthday: Date) => {
  const today = new Date();
  let age = today.getFullYear() - birthday.getFullYear();
  const monthDiff = today.getMonth() - birthday.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthday.getDate())
  ) {
    age--;
  }

  return age;
};

// 编辑基本信息
const editBasicInfo = () => {
  if (selectedMember.value) {
    // 填充编辑表单
    editForm.value = {
      name: selectedMember.value.name,
      gender: selectedMember.value.gender,
      birthday: selectedMember.value.birthday
        ? formatDateForInput(selectedMember.value.birthday)
        : "",
      bloodType: selectedMember.value.bloodType || "",
      phone: selectedMember.value.phone || "",
      email: selectedMember.value.email || "",
    };
    showEditModal.value = true;
  }
};

// 关闭编辑模态框
const closeEditModal = () => {
  showEditModal.value = false;
};

// 保存基本信息
const saveBasicInfo = () => {
  if (selectedMember.value) {
    // 更新成员信息
    Object.assign(selectedMember.value, {
      ...editForm.value,
      birthday: editForm.value.birthday
        ? new Date(editForm.value.birthday)
        : null,
      age: editForm.value.birthday
        ? calculateAge(new Date(editForm.value.birthday))
        : selectedMember.value.age,
    });

    // 更新家庭成员列表中的对应成员
    const memberIndex = familyMembers.value.findIndex(
      (m) => m.id === selectedMember.value.id
    );
    if (memberIndex !== -1) {
      familyMembers.value[memberIndex] = { ...selectedMember.value };
    }

    console.log("保存成功:", selectedMember.value);
    closeEditModal();
  }
};

// 添加医疗记录
const addMedicalRecord = () => {
  console.log("添加医疗记录");
};

// 添加紧急联系人
const addEmergencyContact = () => {
  console.log("添加紧急联系人");
};

// 组件挂载时的初始化
onMounted(() => {
  // 如果URL中有成员ID参数，自动选择该成员
  const memberId = route.query.member;
  if (memberId) {
    const member = familyMembers.value.find(
      (m) => m.id === parseInt(memberId as string)
    );
    if (member) {
      selectedMember.value = member;
    }
  } else {
    // 默认选择第一个成员
    if (familyMembers.value.length > 0) {
      selectedMember.value = familyMembers.value[0];
    }
  }
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}

.member-selector {
  margin-bottom: var(--spacing-6);
}

.member-tabs {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.member-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.member-tab:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.member-tab.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: white;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.member-tab.active .member-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.member-name {
  font-weight: var(--font-weight-medium);
}

.personal-info-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.tabs-navigation {
  display: flex;
  gap: var(--spacing-2);
  border-bottom: 1px solid var(--theme-border);
  padding-bottom: var(--spacing-4);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: none;
  color: var(--theme-text-secondary);
  cursor: pointer;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.tab-button:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.tab-button.active {
  background-color: var(--color-primary);
  color: white;
}

.tab-icon {
  font-size: var(--font-size-lg);
}

.tab-text {
  font-weight: var(--font-weight-medium);
}

.tab-content {
  flex: 1;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  font-weight: var(--font-weight-medium);
}

.info-value {
  font-size: var(--font-size-base);
  color: var(--theme-text-primary);
}

.section-divider {
  height: 1px;
  background-color: var(--theme-border);
  margin: var(--spacing-6) 0;
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.allergies-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.allergy-tag {
  padding: var(--spacing-1) var(--spacing-3);
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.medical-history {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.history-item {
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.history-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.history-date {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.history-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.medical-records {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.medical-record-item {
  padding: var(--spacing-6);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.record-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.record-hospital {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.record-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
}

.record-date {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.record-type {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.record-content p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-normal);
}

.record-attachments h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: var(--spacing-4) 0 var(--spacing-2) 0;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-sm);
}

.attachment-icon {
  font-size: var(--font-size-lg);
}

.attachment-name {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
}

.attachment-view {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
}

.emergency-contacts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.contact-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.contact-relationship,
.contact-phone {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.contact-actions {
  display: flex;
  gap: var(--spacing-2);
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.ai-analysis-section {
  margin-bottom: var(--spacing-8);
}

.analysis-card {
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-6);
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.analysis-icon {
  font-size: var(--font-size-xl);
}

.analysis-title {
  flex: 1;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.analysis-date {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.analysis-summary {
  font-size: var(--font-size-base);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

.analysis-recommendations h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.analysis-recommendations ul {
  margin: 0;
  padding-left: var(--spacing-4);
}

.analysis-recommendations li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
}

.trends-chart {
  margin-bottom: var(--spacing-8);
}

.chart-placeholder {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-4);
  color: var(--theme-text-secondary);
  font-style: italic;
}

.no-member-selected {
  margin-top: var(--spacing-8);
}

.empty-state-large {
  text-align: center;
  padding: var(--spacing-12);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state-large h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state-large p {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-tabs {
    flex-direction: column;
  }

  .tabs-navigation {
    flex-wrap: wrap;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .record-meta {
    align-items: flex-start;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .contact-actions {
    align-self: flex-end;
  }

  .health-metrics {
    grid-template-columns: 1fr;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--theme-border);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--theme-text-secondary);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.modal-close:hover {
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.modal-body {
  padding: var(--spacing-6);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-group label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.form-input,
.form-select {
  padding: var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-normal);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--theme-border);
}

.btn {
  padding: var(--spacing-3) var(--spacing-6);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  border-color: var(--theme-border);
}

.btn-secondary:hover {
  background: var(--theme-bg-tertiary);
}
</style>
