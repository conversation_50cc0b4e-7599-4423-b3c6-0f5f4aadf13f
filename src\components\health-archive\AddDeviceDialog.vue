<template>
  <el-dialog
    v-model="visible"
    title="添加健康设备"
    width="600px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="add-device-content">
      <!-- 设备类型选择 -->
      <div class="device-types">
        <h4 class="section-title">选择设备类型</h4>
        <div class="device-type-grid">
          <div
            v-for="type in deviceTypes"
            :key="type.id"
            class="device-type-card"
            :class="{ selected: deviceForm.type === type.id }"
            @click="selectDeviceType(type)"
          >
            <div class="device-type-icon">{{ type.icon }}</div>
            <div class="device-type-name">{{ type.name }}</div>
            <div class="device-type-desc">{{ type.description }}</div>
          </div>
        </div>
      </div>

      <!-- 设备配置表单 -->
      <div v-if="deviceForm.type" class="device-config">
        <h4 class="section-title">设备配置</h4>
        <el-form :model="deviceForm" :rules="deviceRules" ref="formRef" label-width="100px">
          <el-form-item label="设备名称" prop="name">
            <el-input
              v-model="deviceForm.name"
              placeholder="请输入设备名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="设备型号" prop="model">
            <el-select
              v-model="deviceForm.model"
              placeholder="选择设备型号"
              style="width: 100%"
              filterable
              allow-create
            >
              <el-option
                v-for="model in getDeviceModels(deviceForm.type)"
                :key="model.id"
                :label="model.name"
                :value="model.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="连接方式" prop="connectionType">
            <el-radio-group v-model="deviceForm.connectionType">
              <el-radio label="bluetooth">蓝牙连接</el-radio>
              <el-radio label="wifi">WiFi连接</el-radio>
              <el-radio label="usb">USB连接</el-radio>
              <el-radio label="manual">手动录入</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="deviceForm.connectionType === 'bluetooth'" label="蓝牙地址">
            <el-input
              v-model="deviceForm.bluetoothAddress"
              placeholder="请输入蓝牙MAC地址"
              clearable
            />
            <el-button @click="scanBluetoothDevices" :loading="scanning" style="margin-left: 10px;">
              扫描设备
            </el-button>
          </el-form-item>

          <el-form-item v-if="deviceForm.connectionType === 'wifi'" label="WiFi配置">
            <el-input
              v-model="deviceForm.wifiSSID"
              placeholder="WiFi名称"
              style="margin-bottom: 10px;"
            />
            <el-input
              v-model="deviceForm.wifiPassword"
              type="password"
              placeholder="WiFi密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="数据同步" prop="syncSettings">
            <el-checkbox-group v-model="deviceForm.syncSettings.dataTypes">
              <el-checkbox label="heart_rate">心率数据</el-checkbox>
              <el-checkbox label="blood_pressure">血压数据</el-checkbox>
              <el-checkbox label="blood_glucose">血糖数据</el-checkbox>
              <el-checkbox label="weight">体重数据</el-checkbox>
              <el-checkbox label="sleep">睡眠数据</el-checkbox>
              <el-checkbox label="exercise">运动数据</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="同步频率">
            <el-select v-model="deviceForm.syncSettings.frequency" style="width: 100%">
              <el-option label="实时同步" value="realtime" />
              <el-option label="每小时" value="hourly" />
              <el-option label="每天" value="daily" />
              <el-option label="手动同步" value="manual" />
            </el-select>
          </el-form-item>

          <el-form-item label="关联成员">
            <el-select
              v-model="deviceForm.memberId"
              placeholder="选择关联的家庭成员"
              style="width: 100%"
            >
              <el-option
                v-for="member in familyMembers"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="设备描述">
            <el-input
              v-model="deviceForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入设备描述（可选）"
            />
          </el-form-item>

          <el-form-item label="提醒设置">
            <el-checkbox v-model="deviceForm.notifications.lowBattery">电量低提醒</el-checkbox>
            <el-checkbox v-model="deviceForm.notifications.syncFailed">同步失败提醒</el-checkbox>
            <el-checkbox v-model="deviceForm.notifications.dataAbnormal">数据异常提醒</el-checkbox>
          </el-form-item>
        </el-form>
      </div>

      <!-- 连接测试 -->
      <div v-if="deviceForm.type && deviceForm.connectionType !== 'manual'" class="connection-test">
        <h4 class="section-title">连接测试</h4>
        <div class="test-section">
          <el-button
            type="primary"
            @click="testConnection"
            :loading="testing"
            :disabled="!canTestConnection"
          >
            {{ testing ? '测试中...' : '测试连接' }}
          </el-button>
          <div v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
            <el-icon>
              <Check v-if="testResult.success" />
              <Close v-else />
            </el-icon>
            <span>{{ testResult.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">取消</el-button>
        <el-button
          type="primary"
          @click="saveDevice"
          :loading="saving"
          :disabled="!canSaveDevice"
        >
          添加设备
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', device: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const saving = ref(false)
const scanning = ref(false)
const testing = ref(false)

const formRef = ref<FormInstance>()

// 设备类型
const deviceTypes = ref([
  {
    id: 'blood_pressure',
    name: '血压计',
    icon: '🩺',
    description: '监测血压数据'
  },
  {
    id: 'glucose_meter',
    name: '血糖仪',
    icon: '🩸',
    description: '监测血糖数据'
  },
  {
    id: 'scale',
    name: '体重秤',
    icon: '⚖️',
    description: '监测体重和体脂'
  },
  {
    id: 'fitness_tracker',
    name: '运动手环',
    icon: '⌚',
    description: '监测运动和睡眠'
  },
  {
    id: 'heart_monitor',
    name: '心率监测器',
    icon: '❤️',
    description: '监测心率数据'
  },
  {
    id: 'thermometer',
    name: '体温计',
    icon: '🌡️',
    description: '监测体温数据'
  }
])

// 家庭成员
const familyMembers = ref([
  { id: '1', name: '张先生' },
  { id: '2', name: '李女士' },
  { id: '3', name: '张小明' }
])

// 设备表单
const deviceForm = reactive({
  type: '',
  name: '',
  model: '',
  connectionType: 'bluetooth',
  bluetoothAddress: '',
  wifiSSID: '',
  wifiPassword: '',
  syncSettings: {
    dataTypes: [] as string[],
    frequency: 'daily'
  },
  memberId: '',
  description: '',
  notifications: {
    lowBattery: true,
    syncFailed: true,
    dataAbnormal: false
  }
})

// 测试结果
const testResult = ref<{ success: boolean; message: string } | null>(null)

// 表单验证规则
const deviceRules: FormRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请选择设备型号', trigger: 'change' }
  ],
  connectionType: [
    { required: true, message: '请选择连接方式', trigger: 'change' }
  ]
}

const canTestConnection = computed(() => {
  if (deviceForm.connectionType === 'bluetooth') {
    return deviceForm.bluetoothAddress.length > 0
  }
  if (deviceForm.connectionType === 'wifi') {
    return deviceForm.wifiSSID.length > 0
  }
  return deviceForm.connectionType === 'usb'
})

const canSaveDevice = computed(() => {
  return deviceForm.type && deviceForm.name && deviceForm.model
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (!val) {
    resetForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const resetForm = () => {
  Object.assign(deviceForm, {
    type: '',
    name: '',
    model: '',
    connectionType: 'bluetooth',
    bluetoothAddress: '',
    wifiSSID: '',
    wifiPassword: '',
    syncSettings: {
      dataTypes: [],
      frequency: 'daily'
    },
    memberId: '',
    description: '',
    notifications: {
      lowBattery: true,
      syncFailed: true,
      dataAbnormal: false
    }
  })
  testResult.value = null
  formRef.value?.resetFields()
}

const selectDeviceType = (type: any) => {
  deviceForm.type = type.id
  deviceForm.name = type.name
  
  // 根据设备类型设置默认同步数据类型
  const defaultDataTypes: Record<string, string[]> = {
    blood_pressure: ['blood_pressure'],
    glucose_meter: ['blood_glucose'],
    scale: ['weight'],
    fitness_tracker: ['heart_rate', 'sleep', 'exercise'],
    heart_monitor: ['heart_rate'],
    thermometer: ['temperature']
  }
  
  deviceForm.syncSettings.dataTypes = defaultDataTypes[type.id] || []
}

const getDeviceModels = (deviceType: string) => {
  const models: Record<string, any[]> = {
    blood_pressure: [
      { id: 'omron_hem_7136', name: '欧姆龙 HEM-7136' },
      { id: 'yuwell_ye660d', name: '鱼跃 YE660D' },
      { id: 'xiaomi_ihealth', name: '小米 iHealth' }
    ],
    glucose_meter: [
      { id: 'roche_accu_chek', name: '罗氏 Accu-Chek' },
      { id: 'johnson_onetouch', name: '强生 OneTouch' },
      { id: 'abbott_freestyle', name: '雅培 FreeStyle' }
    ],
    scale: [
      { id: 'xiaomi_scale_2', name: '小米体脂秤2' },
      { id: 'huawei_scale_3', name: '华为智能体脂秤3' },
      { id: 'picooc_s3', name: 'PICOOC S3' }
    ],
    fitness_tracker: [
      { id: 'xiaomi_band_7', name: '小米手环7' },
      { id: 'huawei_band_7', name: '华为手环7' },
      { id: 'apple_watch_s8', name: 'Apple Watch Series 8' }
    ]
  }
  
  return models[deviceType] || []
}

const scanBluetoothDevices = async () => {
  scanning.value = true
  try {
    // 模拟蓝牙扫描
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟找到设备
    const mockDevices = [
      'AA:BB:CC:DD:EE:FF',
      '11:22:33:44:55:66',
      'FF:EE:DD:CC:BB:AA'
    ]
    
    const randomDevice = mockDevices[Math.floor(Math.random() * mockDevices.length)]
    deviceForm.bluetoothAddress = randomDevice
    
    ElMessage.success('扫描完成，已自动填入设备地址')
  } catch (error) {
    ElMessage.error('蓝牙扫描失败')
  } finally {
    scanning.value = false
  }
}

const testConnection = async () => {
  testing.value = true
  testResult.value = null
  
  try {
    // 模拟连接测试
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const success = Math.random() > 0.3 // 70% 成功率
    
    testResult.value = {
      success,
      message: success ? '设备连接成功！' : '设备连接失败，请检查设备状态和连接配置'
    }
    
    if (success) {
      ElMessage.success('设备连接测试成功')
    } else {
      ElMessage.error('设备连接测试失败')
    }
  } catch (error) {
    testResult.value = {
      success: false,
      message: '连接测试出现错误'
    }
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const saveDevice = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newDevice = {
      id: Date.now().toString(),
      ...deviceForm,
      icon: deviceTypes.value.find(t => t.id === deviceForm.type)?.icon || '📱',
      status: 'online',
      battery: Math.floor(Math.random() * 100),
      lastSync: new Date(),
      dataCount: Math.floor(Math.random() * 1000),
      createdAt: new Date()
    }
    
    ElMessage.success('设备添加成功')
    emit('success', newDevice)
    visible.value = false
  } catch (error) {
    console.error('保存设备失败:', error)
    ElMessage.error('保存设备失败')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  if (saving.value) {
    ElMessage.warning('正在保存中，请稍候...')
    return
  }
  visible.value = false
}
</script>

<style scoped>
.add-device-content {
  max-height: 600px;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.device-types {
  margin-bottom: 24px;
}

.device-type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.device-type-card {
  padding: 16px;
  border: 2px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.device-type-card:hover {
  border-color: var(--color-primary);
  background: var(--theme-bg-secondary);
}

.device-type-card.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.device-type-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.device-type-name {
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.device-type-desc {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.device-config {
  margin-bottom: 24px;
}

.connection-test {
  margin-bottom: 24px;
}

.test-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.test-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
}

.test-result.success {
  background: var(--color-success-light);
  color: var(--color-success);
}

.test-result.error {
  background: var(--color-danger-light);
  color: var(--color-danger);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
