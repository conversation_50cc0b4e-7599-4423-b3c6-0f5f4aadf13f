<template>
  <el-dialog
    v-model="visible"
    title="设备设置"
    width="600px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="device-config-content">
      <!-- 设备基本信息 -->
      <div class="device-info">
        <div class="device-header">
          <div class="device-icon">{{ device?.icon || '📱' }}</div>
          <div class="device-details">
            <h3 class="device-name">{{ device?.name }}</h3>
            <p class="device-model">{{ device?.model }}</p>
            <div class="device-status">
              <span class="status-dot" :class="`status-${device?.status}`"></span>
              <span class="status-text">{{ getStatusText(device?.status) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置选项 -->
      <div class="config-sections">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本设置 -->
          <el-tab-pane label="基本设置" name="basic">
            <el-form :model="configForm" label-width="120px">
              <el-form-item label="设备名称">
                <el-input v-model="configForm.name" placeholder="请输入设备名称" />
              </el-form-item>

              <el-form-item label="关联成员">
                <el-select v-model="configForm.memberId" placeholder="选择关联成员" style="width: 100%">
                  <el-option
                    v-for="member in familyMembers"
                    :key="member.id"
                    :label="member.name"
                    :value="member.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="设备描述">
                <el-input
                  v-model="configForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入设备描述"
                />
              </el-form-item>

              <el-form-item label="设备状态">
                <el-switch
                  v-model="configForm.enabled"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 同步设置 -->
          <el-tab-pane label="同步设置" name="sync">
            <el-form :model="configForm.syncSettings" label-width="120px">
              <el-form-item label="同步数据类型">
                <el-checkbox-group v-model="configForm.syncSettings.dataTypes">
                  <el-checkbox label="heart_rate">心率数据</el-checkbox>
                  <el-checkbox label="blood_pressure">血压数据</el-checkbox>
                  <el-checkbox label="blood_glucose">血糖数据</el-checkbox>
                  <el-checkbox label="weight">体重数据</el-checkbox>
                  <el-checkbox label="sleep">睡眠数据</el-checkbox>
                  <el-checkbox label="exercise">运动数据</el-checkbox>
                  <el-checkbox label="temperature">体温数据</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="同步频率">
                <el-select v-model="configForm.syncSettings.frequency" style="width: 100%">
                  <el-option label="实时同步" value="realtime" />
                  <el-option label="每15分钟" value="15min" />
                  <el-option label="每小时" value="hourly" />
                  <el-option label="每天" value="daily" />
                  <el-option label="手动同步" value="manual" />
                </el-select>
              </el-form-item>

              <el-form-item label="自动同步">
                <el-switch
                  v-model="configForm.syncSettings.autoSync"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>

              <el-form-item label="同步时间段">
                <el-time-picker
                  v-model="configForm.syncSettings.syncTimeRange"
                  is-range
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="HH:mm"
                  value-format="HH:mm"
                />
              </el-form-item>

              <el-form-item label="数据保留期">
                <el-select v-model="configForm.syncSettings.dataRetention" style="width: 100%">
                  <el-option label="1个月" value="1month" />
                  <el-option label="3个月" value="3months" />
                  <el-option label="6个月" value="6months" />
                  <el-option label="1年" value="1year" />
                  <el-option label="永久保留" value="forever" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 提醒设置 -->
          <el-tab-pane label="提醒设置" name="notifications">
            <el-form :model="configForm.notifications" label-width="120px">
              <el-form-item label="电量低提醒">
                <el-switch v-model="configForm.notifications.lowBattery" />
                <div v-if="configForm.notifications.lowBattery" style="margin-top: 10px;">
                  <el-slider
                    v-model="configForm.notifications.batteryThreshold"
                    :min="5"
                    :max="50"
                    :step="5"
                    show-stops
                    :format-tooltip="(val) => `${val}%`"
                  />
                  <span style="font-size: 12px; color: var(--theme-text-secondary);">
                    电量低于 {{ configForm.notifications.batteryThreshold }}% 时提醒
                  </span>
                </div>
              </el-form-item>

              <el-form-item label="同步失败提醒">
                <el-switch v-model="configForm.notifications.syncFailed" />
              </el-form-item>

              <el-form-item label="数据异常提醒">
                <el-switch v-model="configForm.notifications.dataAbnormal" />
              </el-form-item>

              <el-form-item label="离线提醒">
                <el-switch v-model="configForm.notifications.offline" />
                <div v-if="configForm.notifications.offline" style="margin-top: 10px;">
                  <el-select v-model="configForm.notifications.offlineDelay" style="width: 200px;">
                    <el-option label="5分钟后" value="5min" />
                    <el-option label="15分钟后" value="15min" />
                    <el-option label="30分钟后" value="30min" />
                    <el-option label="1小时后" value="1hour" />
                  </el-select>
                </div>
              </el-form-item>

              <el-form-item label="提醒方式">
                <el-checkbox-group v-model="configForm.notifications.methods">
                  <el-checkbox label="app">应用内通知</el-checkbox>
                  <el-checkbox label="email">邮件通知</el-checkbox>
                  <el-checkbox label="sms">短信通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 高级设置 -->
          <el-tab-pane label="高级设置" name="advanced">
            <el-form :model="configForm.advanced" label-width="120px">
              <el-form-item label="数据校准">
                <el-button @click="calibrateDevice" :loading="calibrating">
                  校准设备
                </el-button>
                <p style="font-size: 12px; color: var(--theme-text-secondary); margin-top: 5px;">
                  定期校准可以提高数据准确性
                </p>
              </el-form-item>

              <el-form-item label="固件版本">
                <div class="firmware-info">
                  <span>{{ configForm.advanced.firmwareVersion || 'v1.2.3' }}</span>
                  <el-button size="small" @click="checkFirmwareUpdate" :loading="checkingUpdate">
                    检查更新
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="数据导出">
                <el-button @click="exportDeviceData" :loading="exporting">
                  导出历史数据
                </el-button>
              </el-form-item>

              <el-form-item label="重置设备">
                <el-button type="danger" @click="resetDevice" :loading="resetting">
                  恢复出厂设置
                </el-button>
                <p style="font-size: 12px; color: var(--color-danger); margin-top: 5px;">
                  注意：此操作将清除所有设备数据和配置
                </p>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          保存设置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
  device: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated', device: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const saving = ref(false)
const calibrating = ref(false)
const checkingUpdate = ref(false)
const exporting = ref(false)
const resetting = ref(false)
const activeTab = ref('basic')

// 家庭成员
const familyMembers = ref([
  { id: '1', name: '张先生' },
  { id: '2', name: '李女士' },
  { id: '3', name: '张小明' }
])

// 配置表单
const configForm = reactive({
  name: '',
  memberId: '',
  description: '',
  enabled: true,
  syncSettings: {
    dataTypes: [] as string[],
    frequency: 'daily',
    autoSync: true,
    syncTimeRange: ['08:00', '22:00'],
    dataRetention: '6months'
  },
  notifications: {
    lowBattery: true,
    batteryThreshold: 20,
    syncFailed: true,
    dataAbnormal: false,
    offline: true,
    offlineDelay: '15min',
    methods: ['app']
  },
  advanced: {
    firmwareVersion: '',
    lastCalibration: null as Date | null
  }
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.device) {
    loadDeviceConfig()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadDeviceConfig = () => {
  if (props.device) {
    Object.assign(configForm, {
      name: props.device.name,
      memberId: props.device.memberId || '',
      description: props.device.description || '',
      enabled: props.device.enabled !== false,
      syncSettings: {
        dataTypes: props.device.syncSettings?.dataTypes || [],
        frequency: props.device.syncSettings?.frequency || 'daily',
        autoSync: props.device.syncSettings?.autoSync !== false,
        syncTimeRange: props.device.syncSettings?.syncTimeRange || ['08:00', '22:00'],
        dataRetention: props.device.syncSettings?.dataRetention || '6months'
      },
      notifications: {
        lowBattery: props.device.notifications?.lowBattery !== false,
        batteryThreshold: props.device.notifications?.batteryThreshold || 20,
        syncFailed: props.device.notifications?.syncFailed !== false,
        dataAbnormal: props.device.notifications?.dataAbnormal || false,
        offline: props.device.notifications?.offline !== false,
        offlineDelay: props.device.notifications?.offlineDelay || '15min',
        methods: props.device.notifications?.methods || ['app']
      },
      advanced: {
        firmwareVersion: props.device.firmwareVersion || 'v1.2.3',
        lastCalibration: props.device.lastCalibration || null
      }
    })
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    syncing: '同步中',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

const calibrateDevice = async () => {
  calibrating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 3000))
    configForm.advanced.lastCalibration = new Date()
    ElMessage.success('设备校准完成')
  } catch (error) {
    ElMessage.error('设备校准失败')
  } finally {
    calibrating.value = false
  }
}

const checkFirmwareUpdate = async () => {
  checkingUpdate.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    const hasUpdate = Math.random() > 0.7
    if (hasUpdate) {
      ElMessage.info('发现新版本固件，请联系厂商更新')
    } else {
      ElMessage.success('当前已是最新版本')
    }
  } catch (error) {
    ElMessage.error('检查更新失败')
  } finally {
    checkingUpdate.value = false
  }
}

const exportDeviceData = async () => {
  exporting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据导出成功，请查看下载文件')
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exporting.value = false
  }
}

const resetDevice = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复设备出厂设置吗？此操作将清除所有数据和配置，且无法恢复。',
      '确认重置',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    resetting.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('设备重置成功')
    visible.value = false
  } catch (error) {
    // 用户取消
  } finally {
    resetting.value = false
  }
}

const saveConfig = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const updatedDevice = {
      ...props.device,
      ...configForm,
      updatedAt: new Date()
    }
    
    ElMessage.success('设备设置保存成功')
    emit('updated', updatedDevice)
    visible.value = false
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  if (saving.value) {
    ElMessage.warning('正在保存中，请稍候...')
    return
  }
  visible.value = false
}
</script>

<style scoped>
.device-config-content {
  max-height: 600px;
  overflow-y: auto;
}

.device-info {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.device-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.device-icon {
  font-size: 48px;
}

.device-details {
  flex: 1;
}

.device-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin: 0 0 4px 0;
}

.device-model {
  font-size: 14px;
  color: var(--theme-text-secondary);
  margin: 0 0 8px 0;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.status-online {
  background: var(--color-success);
}

.status-dot.status-offline {
  background: var(--color-danger);
}

.status-dot.status-syncing {
  background: var(--color-warning);
}

.status-text {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.config-sections {
  margin-bottom: 24px;
}

.firmware-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
