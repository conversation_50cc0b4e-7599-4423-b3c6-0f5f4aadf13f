// 通用类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页类型
export interface Pagination {
  current: number;
  pageSize: number;
  total: number;
}

export interface PaginatedResponse<T = any> {
  list: T[];
  pagination: Pagination;
}

// 通用状态枚举
export enum Status {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  DISABLED = 'disabled'
}

// 健康状态枚举
export enum HealthStatus {
  NORMAL = 'normal',
  WARNING = 'warning',
  DANGER = 'danger',
  UNKNOWN = 'unknown'
}

// 性别枚举
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

// 血型枚举
export enum BloodType {
  A = 'A',
  B = 'B',
  AB = 'AB',
  O = 'O',
  UNKNOWN = 'unknown'
}

// 优先级枚举
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 任务状态枚举
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 活动类型枚举
export enum ActivityType {
  MEDICATION = 'medication',
  EXERCISE = 'exercise',
  CHECKUP = 'checkup',
  DIET = 'diet',
  SLEEP = 'sleep',
  OTHER = 'other'
}

// 通知类型枚举
export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

// 设备类型枚举
export enum DeviceType {
  BLOOD_PRESSURE = 'blood_pressure',
  BLOOD_GLUCOSE = 'blood_glucose',
  WEIGHT_SCALE = 'weight_scale',
  HEART_RATE = 'heart_rate',
  THERMOMETER = 'thermometer',
  OTHER = 'other'
}

// 报告类型枚举
export enum ReportType {
  BLOOD_TEST = 'blood_test',
  URINE_TEST = 'urine_test',
  X_RAY = 'x_ray',
  CT_SCAN = 'ct_scan',
  MRI = 'mri',
  ECG = 'ecg',
  ULTRASOUND = 'ultrasound',
  OTHER = 'other'
}

// 关系类型枚举
export enum RelationshipType {
  SELF = 'self',
  SPOUSE = 'spouse',
  PARENT = 'parent',
  CHILD = 'child',
  SIBLING = 'sibling',
  GRANDPARENT = 'grandparent',
  GRANDCHILD = 'grandchild',
  OTHER = 'other'
}

// 基础实体接口
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 文件上传类型
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  url?: string;
  error?: string;
}

// 地址信息类型
export interface Address {
  province: string;
  city: string;
  district: string;
  street: string;
  detail: string;
  postalCode?: string;
}

// 联系人信息类型
export interface ContactInfo {
  phone?: string;
  email?: string;
  address?: Address;
}

// 紧急联系人类型
export interface EmergencyContact {
  id: string;
  name: string;
  relationship: RelationshipType;
  phone: string;
  email?: string;
  priority: Priority;
  isDefault: boolean;
}

// 医疗信息类型
export interface MedicalInfo {
  allergies: string[];
  chronicDiseases: string[];
  medications: string[];
  surgeries: string[];
  familyHistory: string[];
  notes?: string;
}

// 健康指标类型
export interface HealthMetric {
  id: string;
  type: string;
  value: number;
  unit: string;
  recordedAt: string;
  status: HealthStatus;
  notes?: string;
}

// 健康趋势类型
export interface HealthTrend {
  metric: string;
  period: string;
  data: Array<{
    date: string;
    value: number;
  }>;
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
}

// 菜单项类型
export interface MenuItem {
  id: string;
  title: string;
  icon?: string;
  path?: string;
  children?: MenuItem[];
  meta?: {
    requiresAuth?: boolean;
    roles?: string[];
    hidden?: boolean;
  };
}

// 面包屑类型
export interface BreadcrumbItem {
  title: string;
  path?: string;
  icon?: string;
}

// 统计卡片类型
export interface StatCard {
  title: string;
  value: string | number;
  unit?: string;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    value: number;
    period: string;
  };
  status?: HealthStatus;
  icon?: string;
  color?: string;
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }>;
}

// 表格列定义类型
export interface TableColumn {
  key: string;
  title: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => any;
}

// 表格数据类型
export interface TableData<T = any> {
  columns: TableColumn[];
  data: T[];
  pagination?: Pagination;
  loading?: boolean;
}

// 表单字段类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'date' | 'number' | 'checkbox' | 'radio';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  disabled?: boolean;
  hidden?: boolean;
}

// 表单配置类型
export interface FormConfig {
  fields: FormField[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelWidth?: string;
  size?: 'small' | 'default' | 'large';
}

// 导出所有类型
export * from './auth';
export * from './user';
export * from './family';
export * from './health';
export * from './calendar';
export * from './activities';
export * from './common';
