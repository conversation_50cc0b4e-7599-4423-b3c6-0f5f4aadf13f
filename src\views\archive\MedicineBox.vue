<template>
  <div class="page-container">
    <!-- 药品统计 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">💊</div>
        <div class="stat-info">
          <div class="stat-value">{{ medicineStats.total }}</div>
          <div class="stat-label">总药品数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-info">
          <div class="stat-value">{{ medicineStats.expiringSoon }}</div>
          <div class="stat-label">即将过期</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📉</div>
        <div class="stat-info">
          <div class="stat-value">{{ medicineStats.lowStock }}</div>
          <div class="stat-label">库存不足</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-info">
          <div class="stat-value">
            {{ medicineStats.byCategory.prescription }}
          </div>
          <div class="stat-label">处方药</div>
        </div>
      </div>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions-section">
      <div class="quick-actions-grid">
        <ContentCard
          title="即将过期"
          :subtitle="`${medicineStats.expiringSoon}种药品`"
          icon="Warning"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          hoverable
          size="sm"
          @click="handleQuickFilter('expiring')"
        >
          <p class="quick-action-desc">查看即将过期的药品</p>
        </ContentCard>

        <ContentCard
          title="库存不足"
          :subtitle="`${medicineStats.lowStock}种药品`"
          icon="AlertTriangle"
          icon-color="#EF4444"
          icon-bg-color="#FEE2E2"
          hoverable
          size="sm"
          @click="handleQuickFilter('lowStock')"
        >
          <p class="quick-action-desc">查看库存不足的药品</p>
        </ContentCard>

        <ContentCard
          title="处方药"
          :subtitle="`${medicineStats.byCategory.prescription}种药品`"
          icon="FileText"
          icon-color="#8B5CF6"
          icon-bg-color="#EDE9FE"
          hoverable
          size="sm"
          @click="handleQuickFilter('prescription')"
        >
          <p class="quick-action-desc">查看处方药品</p>
        </ContentCard>

        <ContentCard
          title="用药提醒"
          subtitle="设置提醒"
          icon="Bell"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          hoverable
          size="sm"
          @click="showReminderDialog = true"
        >
          <p class="quick-action-desc">设置用药提醒</p>
        </ContentCard>
      </div>
    </div>

    <!-- 药品管理内容 -->
    <div class="content-card">
      <div class="card-header">
        <div class="header-left">
          <h3>个人药箱</h3>
          <p>管理您的个人药品和用药提醒</p>
        </div>
        <div class="header-actions">
          <el-button @click="exportMedicineList">
            <el-icon><Download /></el-icon>
            导出清单
          </el-button>
          <el-button @click="importMedicineList">
            <el-icon><Upload /></el-icon>
            导入清单
          </el-button>
          <el-button type="primary" @click="showAddMedicineDialog = true">
            <el-icon><Plus /></el-icon>
            添加药品
          </el-button>
        </div>
      </div>

      <!-- 搜索和筛选工具栏 -->
      <div class="toolbar">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索药品名称..."
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-controls">
          <el-select
            v-model="filters.category"
            placeholder="药品分类"
            clearable
          >
            <el-option label="处方药" value="prescription" />
            <el-option label="非处方药" value="otc" />
            <el-option label="保健品" value="supplement" />
            <el-option label="其他" value="other" />
          </el-select>
          <el-select v-model="filters.status" placeholder="状态" clearable>
            <el-option label="正常" value="normal" />
            <el-option label="库存不足" value="low" />
            <el-option label="即将过期" value="expiring" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </div>
      </div>

      <!-- 药品列表 -->
      <div class="medicine-list">
        <el-table
          :data="filteredMedicines"
          v-loading="loading"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="药品名称" min-width="150" />
          <el-table-column prop="category" label="分类" width="100">
            <template #default="{ row }">
              <el-tag :type="getCategoryTagType(row.category)" size="small">
                {{ getCategoryLabel(row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dosage" label="规格" width="100" />
          <el-table-column label="库存" width="100">
            <template #default="{ row }">
              <span :class="{ 'low-stock': row.quantity <= row.minQuantity }">
                {{ row.quantity }}{{ row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="expiryDate" label="有效期" width="120" />
          <el-table-column
            prop="manufacturer"
            label="生产厂家"
            min-width="120"
          />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  size="small"
                  type="info"
                  @click="viewMedicineDetails(row)"
                >
                  详情
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="editMedicine(row)"
                >
                  编辑
                </el-button>
                <el-dropdown
                  @command="(command) => handleDropdownAction(command, row)"
                >
                  <el-button size="small" type="warning">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="adjustStock"
                        >调整库存</el-dropdown-item
                      >
                      <el-dropdown-item command="delete" divided
                        >删除药品</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 对话框 -->
    <AddMedicineDialog
      v-model="showAddMedicineDialog"
      type="personal"
      @success="handleAddMedicineSuccess"
    />

    <EditMedicineDialog
      v-model="showEditMedicineDialog"
      :medicine="selectedMedicine"
      type="personal"
      @success="handleEditMedicineSuccess"
    />

    <MedicineDetailsDialog
      v-model="showDetailsDialog"
      :medicine="selectedMedicine"
      type="personal"
      @edit="handleEditFromDetails"
    />

    <StockAdjustmentDialog
      v-model="showStockAdjustmentDialog"
      :medicine="selectedMedicine"
      type="personal"
      @success="handleStockAdjustmentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Search,
  Plus,
  Download,
  Upload,
  ArrowDown,
} from "@element-plus/icons-vue";
import {
  medicineService,
  type Medicine,
  type MedicineStats,
} from "@/services/medicineService";
import AddMedicineDialog from "@/components/medicine/AddMedicineDialog.vue";
import EditMedicineDialog from "@/components/medicine/EditMedicineDialog.vue";
import MedicineDetailsDialog from "@/components/medicine/MedicineDetailsDialog.vue";
import StockAdjustmentDialog from "@/components/medicine/StockAdjustmentDialog.vue";

// 使用导入的类型

const loading = ref(false);
const currentUserId = ref("1");
const searchQuery = ref("");

// 筛选
const filters = ref({
  category: "",
  status: "",
});

// 分页
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
});

// 药品数据
const medicines = ref<Medicine[]>([]);

const selectedMedicines = ref<Medicine[]>([]);
const selectedMedicine = ref<Medicine | null>(null);

// 对话框状态
const showAddMedicineDialog = ref(false);
const showEditMedicineDialog = ref(false);
const showDetailsDialog = ref(false);
const showStockAdjustmentDialog = ref(false);
const showReminderDialog = ref(false);

// 药品统计
const medicineStats = ref<MedicineStats>({
  total: 0,
  expiringSoon: 0,
  lowStock: 0,
  expired: 0,
  byCategory: {
    prescription: 0,
    otc: 0,
    supplement: 0,
    other: 0,
  },
});

// 计算属性
const filteredMedicines = computed(() => {
  let filtered = medicines.value;

  if (filters.value.category) {
    filtered = filtered.filter((m) => m.category === filters.value.category);
  }

  if (searchQuery.value) {
    const search = searchQuery.value.toLowerCase();
    filtered = filtered.filter(
      (m) =>
        m.name.toLowerCase().includes(search) ||
        m.manufacturer.toLowerCase().includes(search)
    );
  }

  const start = (pagination.value.page - 1) * pagination.value.limit;
  const end = start + pagination.value.limit;
  return filtered.slice(start, end);
});

// 方法
const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    prescription: "处方药",
    otc: "非处方药",
    supplement: "保健品",
    other: "其他",
  };
  return labels[category] || category;
};

const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    prescription: "danger",
    otc: "success",
    supplement: "info",
    other: "warning",
  };
  return types[category] || "";
};

const handleSelectionChange = (selection: Medicine[]) => {
  selectedMedicines.value = selection;
};

const handleSizeChange = (size: number) => {
  pagination.value.limit = size;
  pagination.value.page = 1;
};

const handleCurrentChange = (page: number) => {
  pagination.value.page = page;
};

const loadMedicines = async () => {
  loading.value = true;
  try {
    const result = await medicineService.getMedicines(currentUserId.value, {
      category: filters.value.category,
      status: filters.value.status,
      search: searchQuery.value,
    });

    medicines.value = result;
    pagination.value.total = result.length;

    // 加载统计数据
    const stats = await medicineService.getMedicineStats(currentUserId.value);
    medicineStats.value = stats;
  } catch (error) {
    console.error("加载个人药品失败:", error);
    ElMessage.error("加载个人药品失败");
  } finally {
    loading.value = false;
  }
};

const viewMedicineDetails = (medicine: Medicine) => {
  selectedMedicine.value = medicine;
  showDetailsDialog.value = true;
};

const editMedicine = (medicine: Medicine) => {
  selectedMedicine.value = medicine;
  showEditMedicineDialog.value = true;
};

const adjustStock = (medicine: Medicine) => {
  selectedMedicine.value = medicine;
  showStockAdjustmentDialog.value = true;
};

const handleDropdownAction = (command: string, medicine: Medicine) => {
  switch (command) {
    case "adjustStock":
      adjustStock(medicine);
      break;
    case "delete":
      handleDeleteMedicine(medicine);
      break;
  }
};

const handleDeleteMedicine = async (medicine: Medicine) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除药品"${medicine.name}"吗？此操作不可恢复。`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await medicineService.removeMedicine(medicine.id);
    ElMessage.success("删除药品成功");
    loadMedicines();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除药品失败");
    }
  }
};

const handleEditFromDetails = (medicine: Medicine) => {
  showDetailsDialog.value = false;
  selectedMedicine.value = medicine;
  showEditMedicineDialog.value = true;
};

const handleAddMedicineSuccess = async (data: any) => {
  try {
    await medicineService.addMedicine(currentUserId.value, data);
    ElMessage.success("添加药品成功");
    loadMedicines();
  } catch (error) {
    ElMessage.error("添加药品失败");
  }
};

const handleEditMedicineSuccess = async (data: any) => {
  if (!selectedMedicine.value) return;

  try {
    await medicineService.updateMedicine(selectedMedicine.value.id, data);
    ElMessage.success("更新药品成功");
    loadMedicines();
  } catch (error) {
    ElMessage.error("更新药品失败");
  }
};

const handleStockAdjustmentSuccess = async (adjustment: any) => {
  try {
    await medicineService.adjustStock(adjustment);
    ElMessage.success("库存调整成功");
    loadMedicines();
  } catch (error) {
    ElMessage.error("库存调整失败");
  }
};

const handleQuickFilter = (type: string) => {
  switch (type) {
    case "expiring":
      filters.value.status = "expiring";
      searchQuery.value = "";
      ElMessage.info("已筛选即将过期的药品");
      break;
    case "lowStock":
      filters.value.status = "low";
      searchQuery.value = "";
      ElMessage.info("已筛选库存不足的药品");
      break;
    case "prescription":
      filters.value.category = "prescription";
      filters.value.status = "all";
      searchQuery.value = "";
      ElMessage.info("已筛选处方药品");
      break;
  }
};

const exportMedicineList = async () => {
  try {
    const csvContent = await medicineService.exportMedicineList(
      currentUserId.value
    );
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `个人药箱清单_${new Date().toLocaleDateString()}.csv`;
    link.click();
    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error("导出失败");
  }
};

const importMedicineList = () => {
  ElMessage.info("导入功能开发中...");
};

// 初始化
onMounted(() => {
  loadMedicines();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-left p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.toolbar {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.medicine-list {
  padding: 20px;
}

.low-stock {
  color: #f56c6c;
  font-weight: 500;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}

.quick-actions-section {
  margin-bottom: 24px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.quick-action-desc {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}
</style>
