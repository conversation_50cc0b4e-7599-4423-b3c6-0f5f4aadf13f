<template>
  <el-dialog
    v-model="visible"
    title="权限管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="permission-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 成员权限 -->
        <el-tab-pane label="成员权限" name="members">
          <div class="members-permission">
            <div class="toolbar">
              <el-button type="primary" @click="showBatchPermissionDialog = true">
                批量设置权限
              </el-button>
              <el-button @click="showPermissionTemplateDialog = true">
                权限模板
              </el-button>
            </div>

            <el-table :data="membersData" style="width: 100%">
              <el-table-column prop="name" label="成员姓名" width="120" />
              <el-table-column prop="role" label="角色" width="100" />
              <el-table-column label="查看健康数据" width="120" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.permissions.viewHealth"
                    @change="updateMemberPermission(row, 'viewHealth')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="管理提醒" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.permissions.manageReminders"
                    @change="updateMemberPermission(row, 'manageReminders')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="邀请成员" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.permissions.inviteMembers"
                    :disabled="!row.isAdmin"
                    @change="updateMemberPermission(row, 'inviteMembers')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="管理设置" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.permissions.manageFamilySettings"
                    :disabled="!row.isAdmin"
                    @change="updateMemberPermission(row, 'manageFamilySettings')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="移除成员" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.permissions.removeMembers"
                    :disabled="!row.isAdmin"
                    @change="updateMemberPermission(row, 'removeMembers')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button
                    type="text"
                    size="small"
                    @click="editMemberPermission(row)"
                  >
                    详细设置
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 权限模板 -->
        <el-tab-pane label="权限模板" name="templates">
          <div class="permission-templates">
            <div class="toolbar">
              <el-button type="primary" @click="createPermissionTemplate">
                创建模板
              </el-button>
            </div>

            <div class="templates-grid">
              <div
                v-for="template in permissionTemplates"
                :key="template.id"
                class="template-card"
              >
                <div class="template-header">
                  <h4 class="template-name">{{ template.name }}</h4>
                  <div class="template-actions">
                    <el-button
                      type="text"
                      size="small"
                      @click="applyTemplate(template)"
                    >
                      应用
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="editTemplate(template)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="deleteTemplate(template)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div class="template-description">
                  {{ template.description }}
                </div>
                <div class="template-permissions">
                  <el-tag
                    v-for="permission in getTemplatePermissions(template)"
                    :key="permission"
                    size="small"
                    class="permission-tag"
                  >
                    {{ getPermissionName(permission) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限日志 -->
        <el-tab-pane label="权限日志" name="logs">
          <div class="permission-logs">
            <div class="toolbar">
              <el-date-picker
                v-model="logDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="loadPermissionLogs"
              />
              <el-button @click="exportLogs">导出日志</el-button>
            </div>

            <el-table :data="permissionLogs" style="width: 100%">
              <el-table-column prop="timestamp" label="时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.timestamp) }}
                </template>
              </el-table-column>
              <el-table-column prop="operator" label="操作者" width="120" />
              <el-table-column prop="target" label="目标成员" width="120" />
              <el-table-column prop="action" label="操作类型" width="120" />
              <el-table-column prop="permission" label="权限项" width="120" />
              <el-table-column prop="oldValue" label="原值" width="80" />
              <el-table-column prop="newValue" label="新值" width="80" />
              <el-table-column prop="reason" label="备注" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 批量权限设置对话框 -->
    <el-dialog
      v-model="showBatchPermissionDialog"
      title="批量权限设置"
      width="500px"
      append-to-body
    >
      <div class="batch-permission-content">
        <el-form label-width="120px">
          <el-form-item label="选择成员">
            <el-checkbox-group v-model="selectedMembers">
              <el-checkbox
                v-for="member in membersData"
                :key="member.id"
                :label="member.id"
              >
                {{ member.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="权限设置">
            <div class="batch-permissions">
              <div class="permission-item">
                <span>查看健康数据</span>
                <el-radio-group v-model="batchPermissions.viewHealth">
                  <el-radio label="true">开启</el-radio>
                  <el-radio label="false">关闭</el-radio>
                  <el-radio label="">不变</el-radio>
                </el-radio-group>
              </div>
              <div class="permission-item">
                <span>管理健康提醒</span>
                <el-radio-group v-model="batchPermissions.manageReminders">
                  <el-radio label="true">开启</el-radio>
                  <el-radio label="false">关闭</el-radio>
                  <el-radio label="">不变</el-radio>
                </el-radio-group>
              </div>
              <div class="permission-item">
                <span>邀请新成员</span>
                <el-radio-group v-model="batchPermissions.inviteMembers">
                  <el-radio label="true">开启</el-radio>
                  <el-radio label="false">关闭</el-radio>
                  <el-radio label="">不变</el-radio>
                </el-radio-group>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="applyBatchPermissions">
            应用设置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
  familyId?: number | string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const activeTab = ref('members')
const showBatchPermissionDialog = ref(false)
const showPermissionTemplateDialog = ref(false)
const logDateRange = ref<[Date, Date] | null>(null)
const selectedMembers = ref<number[]>([])

// 成员数据
const membersData = ref([
  {
    id: 1,
    name: '张先生',
    role: '父亲',
    isAdmin: true,
    permissions: {
      viewHealth: true,
      manageReminders: true,
      inviteMembers: true,
      manageFamilySettings: true,
      removeMembers: true
    }
  },
  {
    id: 2,
    name: '李女士',
    role: '母亲',
    isAdmin: true,
    permissions: {
      viewHealth: true,
      manageReminders: true,
      inviteMembers: true,
      manageFamilySettings: true,
      removeMembers: true
    }
  },
  {
    id: 3,
    name: '张小明',
    role: '儿子',
    isAdmin: false,
    permissions: {
      viewHealth: true,
      manageReminders: false,
      inviteMembers: false,
      manageFamilySettings: false,
      removeMembers: false
    }
  }
])

// 权限模板
const permissionTemplates = ref([
  {
    id: 1,
    name: '管理员模板',
    description: '拥有所有权限的管理员模板',
    permissions: {
      viewHealth: true,
      manageReminders: true,
      inviteMembers: true,
      manageFamilySettings: true,
      removeMembers: true
    }
  },
  {
    id: 2,
    name: '普通成员模板',
    description: '只能查看健康数据的普通成员模板',
    permissions: {
      viewHealth: true,
      manageReminders: false,
      inviteMembers: false,
      manageFamilySettings: false,
      removeMembers: false
    }
  }
])

// 权限日志
const permissionLogs = ref([
  {
    id: 1,
    timestamp: new Date('2024-01-20 10:30:00'),
    operator: '张先生',
    target: '张小明',
    action: '修改权限',
    permission: '管理健康提醒',
    oldValue: '关闭',
    newValue: '开启',
    reason: '允许管理家庭健康提醒'
  }
])

// 批量权限设置
const batchPermissions = reactive({
  viewHealth: '',
  manageReminders: '',
  inviteMembers: ''
})

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const updateMemberPermission = (member: any, permission: string) => {
  console.log('更新权限:', member.name, permission, member.permissions[permission])
  // 这里可以调用API更新权限
}

const editMemberPermission = (member: any) => {
  console.log('编辑成员权限:', member)
  // 打开详细权限设置对话框
}

const createPermissionTemplate = () => {
  console.log('创建权限模板')
  // 打开创建模板对话框
}

const applyTemplate = (template: any) => {
  console.log('应用模板:', template)
  // 应用权限模板
}

const editTemplate = (template: any) => {
  console.log('编辑模板:', template)
  // 编辑权限模板
}

const deleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限模板"${template.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('权限模板删除成功')
  } catch (error) {
    // 用户取消
  }
}

const getTemplatePermissions = (template: any) => {
  return Object.keys(template.permissions).filter(key => template.permissions[key])
}

const getPermissionName = (permission: string) => {
  const names: Record<string, string> = {
    viewHealth: '查看健康数据',
    manageReminders: '管理提醒',
    inviteMembers: '邀请成员',
    manageFamilySettings: '管理设置',
    removeMembers: '移除成员'
  }
  return names[permission] || permission
}

const loadPermissionLogs = () => {
  console.log('加载权限日志:', logDateRange.value)
  // 根据日期范围加载权限日志
}

const exportLogs = () => {
  console.log('导出权限日志')
  ElMessage.success('权限日志导出成功')
}

const applyBatchPermissions = () => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请选择要设置权限的成员')
    return
  }

  console.log('批量设置权限:', selectedMembers.value, batchPermissions)
  ElMessage.success('批量权限设置成功')
  showBatchPermissionDialog.value = false
}

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  loading.value = true
  try {
    // 模拟保存权限设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('权限设置保存成功！')
    emit('success')
    visible.value = false
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.permission-content {
  max-height: 600px;
  overflow-y: auto;
}

.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.template-card {
  padding: 16px;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.template-actions {
  display: flex;
  gap: 8px;
}

.template-description {
  color: var(--theme-text-secondary);
  font-size: 12px;
  margin-bottom: 12px;
}

.template-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-tag {
  font-size: 11px;
}

.batch-permissions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
