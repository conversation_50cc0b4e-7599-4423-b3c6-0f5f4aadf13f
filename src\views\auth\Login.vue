<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                />
              </svg>
            </div>
            <h1 class="logo-text">家庭健康管理</h1>
          </div>
          <p class="brand-slogan">守护您和家人的健康每一天</p>

          <!-- 价值展示 -->
          <div class="value-points">
            <div class="value-item">
              <div class="value-icon">🏠</div>
              <div class="value-text">
                <h3>家庭中心化管理</h3>
                <p>统一管理家庭成员的健康信息</p>
              </div>
            </div>
            <div class="value-item">
              <div class="value-icon">🤖</div>
              <div class="value-text">
                <h3>AI智能分析</h3>
                <p>基于健康数据提供个性化建议</p>
              </div>
            </div>
            <div class="value-item">
              <div class="value-icon">📊</div>
              <div class="value-text">
                <h3>健康数据可视化</h3>
                <p>直观展示健康趋势和指标</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">欢迎回来</h2>
            <p class="form-subtitle">请登录您的账户</p>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <!-- 用户名/手机号输入 -->
            <div class="form-group">
              <label for="username" class="form-label">用户名/手机号</label>
              <input
                id="username"
                v-model="loginForm.username"
                type="text"
                class="form-input"
                placeholder="请输入用户名或手机号"
                required
              />
            </div>

            <!-- 密码输入 -->
            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="password-input-wrapper">
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  placeholder="请输入密码"
                  required
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <span v-if="showPassword">👁️</span>
                  <span v-else>👁️‍🗨️</span>
                </button>
              </div>
            </div>

            <!-- 记住密码和忘记密码 -->
            <div class="form-options">
              <label class="checkbox-wrapper">
                <input
                  v-model="loginForm.rememberMe"
                  type="checkbox"
                  class="checkbox"
                />
                <span class="checkbox-label">记住密码</span>
              </label>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <!-- 登录按钮 -->
            <button type="submit" class="login-btn" :disabled="loading">
              <span v-if="loading">登录中...</span>
              <span v-else>登录</span>
            </button>

            <!-- 微信一键登录 -->
            <div class="divider">
              <span class="divider-text">或</span>
            </div>

            <button
              type="button"
              class="wechat-login-btn"
              @click="handleWechatLogin"
            >
              <span class="wechat-icon">💬</span>
              微信一键登录
            </button>
          </form>

          <!-- 注册链接 -->
          <div class="form-footer">
            <p class="register-prompt">
              还没有账户？
              <router-link to="/register" class="register-link"
                >立即注册</router-link
              >
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 表单数据
const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
});

// 状态
const showPassword = ref(false);
const loading = ref(false);

// 登录处理
const handleLogin = async () => {
  loading.value = true;

  try {
    // 模拟登录API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟登录成功
    console.log("登录成功:", loginForm.value);

    // 设置认证token
    localStorage.setItem("auth_token", "mock_token_" + Date.now());

    // 跳转到首页
    router.push("/dashboard");
  } catch (error) {
    console.error("登录失败:", error);
  } finally {
    loading.value = false;
  }
};

// 微信登录处理
const handleWechatLogin = () => {
  console.log("微信登录");
  // 这里应该调用微信登录API
  // 模拟登录成功后跳转
  localStorage.setItem("auth_token", "wechat_token_" + Date.now());
  router.push("/dashboard");
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  margin: var(--spacing-8);
}

.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: var(--spacing-12);
  display: flex;
  align-items: center;
}

.brand-content {
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.logo-icon {
  width: 48px;
  height: 48px;
  color: white;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.brand-slogan {
  font-size: var(--font-size-lg);
  margin: 0 0 var(--spacing-8) 0;
  opacity: 0.9;
}

.value-points {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.value-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.value-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.value-text h3 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-1) 0;
}

.value-text p {
  font-size: var(--font-size-sm);
  margin: 0;
  opacity: 0.8;
}

.form-section {
  flex: 1;
  padding: var(--spacing-12);
  display: flex;
  align-items: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.form-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.form-subtitle {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.form-input {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: border-color var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  color: var(--theme-text-secondary);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
}

.checkbox-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.forgot-password {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.login-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.divider {
  position: relative;
  text-align: center;
  margin: var(--spacing-4) 0;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--theme-border);
}

.divider-text {
  background-color: var(--theme-bg-primary);
  padding: 0 var(--spacing-4);
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
}

.wechat-login-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  transition: background-color var(--transition-normal);
}

.wechat-login-btn:hover {
  background-color: #06ad56;
}

.wechat-icon {
  font-size: var(--font-size-lg);
}

.form-footer {
  text-align: center;
  margin-top: var(--spacing-6);
}

.register-prompt {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.register-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.register-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: var(--spacing-4);
  }

  .brand-section {
    padding: var(--spacing-8);
  }

  .form-section {
    padding: var(--spacing-8);
  }

  .value-points {
    display: none;
  }
}
</style>
