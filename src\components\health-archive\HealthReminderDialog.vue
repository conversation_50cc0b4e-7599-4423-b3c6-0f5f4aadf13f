<template>
  <el-dialog
    v-model="visible"
    title="健康提醒设置"
    width="800px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="reminder-content">
      <!-- 提醒列表 -->
      <div class="reminders-section">
        <div class="section-header">
          <h4 class="section-title">我的提醒</h4>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加提醒
          </el-button>
        </div>

        <div class="reminders-list">
          <div
            v-for="reminder in reminders"
            :key="reminder.id"
            class="reminder-item"
            :class="{ disabled: !reminder.enabled }"
          >
            <div class="reminder-info">
              <div class="reminder-header">
                <div class="reminder-title">{{ reminder.title }}</div>
                <el-switch
                  v-model="reminder.enabled"
                  @change="toggleReminder(reminder)"
                />
              </div>
              <div class="reminder-details">
                <span class="reminder-type">{{ getReminderTypeName(reminder.type) }}</span>
                <span class="reminder-time">{{ reminder.time }}</span>
                <span class="reminder-frequency">{{ getFrequencyName(reminder.frequency) }}</span>
              </div>
              <div v-if="reminder.description" class="reminder-description">
                {{ reminder.description }}
              </div>
            </div>
            <div class="reminder-actions">
              <el-button size="small" @click="editReminder(reminder)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="deleteReminder(reminder.id)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="reminders.length === 0" class="empty-reminders">
          <el-empty description="暂无健康提醒，点击添加提醒开始设置" />
        </div>
      </div>
    </div>

    <!-- 添加/编辑提醒对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingReminder ? '编辑提醒' : '添加提醒'"
      width="500px"
      :append-to-body="true"
    >
      <el-form :model="reminderForm" :rules="reminderRules" ref="formRef" label-width="100px">
        <el-form-item label="提醒标题" prop="title">
          <el-input v-model="reminderForm.title" placeholder="请输入提醒标题" />
        </el-form-item>

        <el-form-item label="提醒类型" prop="type">
          <el-select v-model="reminderForm.type" placeholder="选择提醒类型" style="width: 100%">
            <el-option label="用药提醒" value="medication" />
            <el-option label="体检提醒" value="checkup" />
            <el-option label="运动提醒" value="exercise" />
            <el-option label="测量提醒" value="measurement" />
            <el-option label="复诊提醒" value="followup" />
            <el-option label="自定义提醒" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item label="提醒时间" prop="time">
          <el-time-picker
            v-model="reminderForm.time"
            placeholder="选择提醒时间"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="重复频率" prop="frequency">
          <el-select v-model="reminderForm.frequency" placeholder="选择重复频率" style="width: 100%">
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="工作日" value="weekdays" />
            <el-option label="周末" value="weekends" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="reminderForm.frequency === 'weekly'" label="重复日期">
          <el-checkbox-group v-model="reminderForm.weekdays">
            <el-checkbox label="1">周一</el-checkbox>
            <el-checkbox label="2">周二</el-checkbox>
            <el-checkbox label="3">周三</el-checkbox>
            <el-checkbox label="4">周四</el-checkbox>
            <el-checkbox label="5">周五</el-checkbox>
            <el-checkbox label="6">周六</el-checkbox>
            <el-checkbox label="0">周日</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="提醒对象">
          <el-select v-model="reminderForm.memberId" placeholder="选择提醒对象" style="width: 100%">
            <el-option label="所有成员" value="" />
            <el-option
              v-for="member in familyMembers"
              :key="member.id"
              :label="member.name"
              :value="member.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="提醒描述">
          <el-input
            v-model="reminderForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入提醒描述（可选）"
          />
        </el-form-item>

        <el-form-item label="提前提醒">
          <el-select v-model="reminderForm.advance" placeholder="选择提前时间" style="width: 100%">
            <el-option label="准时提醒" :value="0" />
            <el-option label="提前5分钟" :value="5" />
            <el-option label="提前10分钟" :value="10" />
            <el-option label="提前15分钟" :value="15" />
            <el-option label="提前30分钟" :value="30" />
            <el-option label="提前1小时" :value="60" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveReminder" :loading="saving">
            {{ editingReminder ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

interface Reminder {
  id: string
  title: string
  type: string
  time: string
  frequency: string
  weekdays?: string[]
  memberId: string
  description: string
  advance: number
  enabled: boolean
  nextReminder?: Date
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const showAddDialog = ref(false)
const saving = ref(false)
const editingReminder = ref<Reminder | null>(null)

const formRef = ref<FormInstance>()

// 提醒列表
const reminders = ref<Reminder[]>([
  {
    id: '1',
    title: '早晨血压测量',
    type: 'measurement',
    time: '08:00',
    frequency: 'daily',
    memberId: '1',
    description: '每天早晨起床后测量血压',
    advance: 0,
    enabled: true,
    nextReminder: new Date()
  },
  {
    id: '2',
    title: '降压药服用',
    type: 'medication',
    time: '09:00',
    frequency: 'daily',
    memberId: '1',
    description: '服用降压药物',
    advance: 5,
    enabled: true,
    nextReminder: new Date()
  },
  {
    id: '3',
    title: '年度体检',
    type: 'checkup',
    time: '09:00',
    frequency: 'monthly',
    memberId: '',
    description: '定期进行全面体检',
    advance: 60,
    enabled: false,
    nextReminder: new Date()
  }
])

// 家庭成员
const familyMembers = ref([
  { id: '1', name: '张先生' },
  { id: '2', name: '李女士' },
  { id: '3', name: '张小明' }
])

// 提醒表单
const reminderForm = reactive<Partial<Reminder>>({
  title: '',
  type: '',
  time: '',
  frequency: 'daily',
  weekdays: [],
  memberId: '',
  description: '',
  advance: 0,
  enabled: true
})

// 表单验证规则
const reminderRules: FormRules = {
  title: [
    { required: true, message: '请输入提醒标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择提醒类型', trigger: 'change' }
  ],
  time: [
    { required: true, message: '请选择提醒时间', trigger: 'change' }
  ],
  frequency: [
    { required: true, message: '请选择重复频率', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const getReminderTypeName = (type: string) => {
  const names: Record<string, string> = {
    medication: '用药提醒',
    checkup: '体检提醒',
    exercise: '运动提醒',
    measurement: '测量提醒',
    followup: '复诊提醒',
    custom: '自定义提醒'
  }
  return names[type] || type
}

const getFrequencyName = (frequency: string) => {
  const names: Record<string, string> = {
    daily: '每天',
    weekly: '每周',
    monthly: '每月',
    weekdays: '工作日',
    weekends: '周末',
    custom: '自定义'
  }
  return names[frequency] || frequency
}

const toggleReminder = (reminder: Reminder) => {
  ElMessage.success(`提醒已${reminder.enabled ? '启用' : '禁用'}`)
}

const editReminder = (reminder: Reminder) => {
  editingReminder.value = reminder
  Object.assign(reminderForm, reminder)
  showAddDialog.value = true
}

const deleteReminder = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个提醒吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = reminders.value.findIndex(r => r.id === id)
    if (index > -1) {
      reminders.value.splice(index, 1)
      ElMessage.success('提醒删除成功')
    }
  } catch (error) {
    // 用户取消
  }
}

const saveReminder = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingReminder.value) {
      // 更新现有提醒
      const index = reminders.value.findIndex(r => r.id === editingReminder.value!.id)
      if (index > -1) {
        reminders.value[index] = { ...reminders.value[index], ...reminderForm }
      }
      ElMessage.success('提醒更新成功')
    } else {
      // 添加新提醒
      const newReminder: Reminder = {
        id: Date.now().toString(),
        ...reminderForm as Reminder
      }
      reminders.value.push(newReminder)
      ElMessage.success('提醒添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    console.error('保存提醒失败:', error)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingReminder.value = null
  Object.assign(reminderForm, {
    title: '',
    type: '',
    time: '',
    frequency: 'daily',
    weekdays: [],
    memberId: '',
    description: '',
    advance: 0,
    enabled: true
  })
  formRef.value?.resetFields()
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.reminder-content {
  max-height: 600px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.reminders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.reminder-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  transition: all 0.3s;
}

.reminder-item.disabled {
  opacity: 0.6;
}

.reminder-info {
  flex: 1;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reminder-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-primary);
}

.reminder-details {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.reminder-type,
.reminder-time,
.reminder-frequency {
  font-size: 12px;
  color: var(--theme-text-secondary);
  padding: 2px 8px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
}

.reminder-description {
  font-size: 14px;
  color: var(--theme-text-secondary);
  line-height: 1.4;
}

.reminder-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.empty-reminders {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
