<template>
  <el-dialog
    v-model="dialogVisible"
    title="活动日志"
    width="800px"
    :lock-scroll="true"
    :modal="true"
    center
  >
    <div class="activity-log-content">
      <div class="filter-section">
        <el-form inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="活动类型">
            <el-select v-model="activityType" placeholder="请选择类型">
              <el-option label="全部" value="" />
              <el-option label="登录" value="login" />
              <el-option label="健康记录" value="health" />
              <el-option label="药品管理" value="medicine" />
              <el-option label="设置修改" value="settings" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchLogs">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <div class="logs-list">
        <div
          v-for="log in activityLogs"
          :key="log.id"
          class="log-item"
        >
          <div class="log-time">{{ formatTime(log.time) }}</div>
          <div class="log-content">
            <div class="log-title">{{ log.title }}</div>
            <div class="log-description">{{ log.description }}</div>
            <div class="log-member">操作人：{{ log.member }}</div>
          </div>
          <div class="log-type" :class="`type-${log.type}`">
            {{ getTypeText(log.type) }}
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = ref(false);
const dateRange = ref([]);
const activityType = ref("");

const activityLogs = reactive([
  {
    id: 1,
    title: "用户登录",
    description: "张先生登录了系统",
    member: "张先生",
    time: new Date(Date.now() - 1000 * 60 * 30),
    type: "login"
  },
  {
    id: 2,
    title: "添加健康记录",
    description: "李女士添加了血压监测记录",
    member: "李女士",
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    type: "health"
  },
  {
    id: 3,
    title: "药品管理",
    description: "张先生添加了新的药品信息",
    member: "张先生",
    time: new Date(Date.now() - 1000 * 60 * 60 * 24),
    type: "medicine"
  }
]);

watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, (val) => {
  emit("update:modelValue", val);
});

const formatTime = (time: Date) => {
  return time.toLocaleString("zh-CN");
};

const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    login: "登录",
    health: "健康",
    medicine: "药品",
    settings: "设置"
  };
  return typeMap[type] || "其他";
};

const searchLogs = () => {
  // 这里可以实现搜索逻辑
  console.log("搜索日志", { dateRange: dateRange.value, activityType: activityType.value });
};

const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.activity-log-content {
  padding: 20px 0;
}

.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.logs-list {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.log-time {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  min-width: 120px;
}

.log-content {
  flex: 1;
}

.log-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.log-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.log-member {
  font-size: 12px;
  color: #9ca3af;
}

.log-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.type-login {
  background: #dbeafe;
  color: #3b82f6;
}

.type-health {
  background: #d1fae5;
  color: #10b981;
}

.type-medicine {
  background: #fee2e2;
  color: #ef4444;
}

.type-settings {
  background: #ede9fe;
  color: #8b5cf6;
}

.dialog-footer {
  text-align: right;
}
</style>
