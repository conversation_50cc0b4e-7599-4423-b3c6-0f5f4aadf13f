<template>
  <el-dialog
    v-model="visible"
    title="健康档案设置"
    width="700px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="settings-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 通用设置 -->
        <el-tab-pane label="通用设置" name="general">
          <el-form :model="settings.general" label-width="120px">
            <el-form-item label="默认视图">
              <el-select v-model="settings.general.defaultView" style="width: 100%">
                <el-option label="时间线视图" value="timeline" />
                <el-option label="列表视图" value="list" />
                <el-option label="卡片视图" value="card" />
                <el-option label="表格视图" value="table" />
              </el-select>
            </el-form-item>

            <el-form-item label="每页显示">
              <el-select v-model="settings.general.pageSize" style="width: 100%">
                <el-option label="10条" :value="10" />
                <el-option label="20条" :value="20" />
                <el-option label="50条" :value="50" />
                <el-option label="100条" :value="100" />
              </el-select>
            </el-form-item>

            <el-form-item label="自动刷新">
              <el-switch v-model="settings.general.autoRefresh" />
              <div v-if="settings.general.autoRefresh" style="margin-top: 10px;">
                <el-select v-model="settings.general.refreshInterval" style="width: 200px;">
                  <el-option label="30秒" :value="30" />
                  <el-option label="1分钟" :value="60" />
                  <el-option label="5分钟" :value="300" />
                  <el-option label="10分钟" :value="600" />
                </el-select>
              </div>
            </el-form-item>

            <el-form-item label="主题模式">
              <el-radio-group v-model="settings.general.theme">
                <el-radio label="light">浅色模式</el-radio>
                <el-radio label="dark">深色模式</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言设置">
              <el-select v-model="settings.general.language" style="width: 100%">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="繁体中文" value="zh-TW" />
                <el-option label="English" value="en-US" />
                <el-option label="日本語" value="ja-JP" />
              </el-select>
            </el-form-item>

            <el-form-item label="时区设置">
              <el-select v-model="settings.general.timezone" style="width: 100%">
                <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 隐私设置 -->
        <el-tab-pane label="隐私设置" name="privacy">
          <el-form :model="settings.privacy" label-width="120px">
            <el-form-item label="数据加密">
              <el-switch v-model="settings.privacy.dataEncryption" />
              <p style="font-size: 12px; color: var(--theme-text-secondary); margin-top: 5px;">
                启用后将对敏感健康数据进行加密存储
              </p>
            </el-form-item>

            <el-form-item label="访问日志">
              <el-switch v-model="settings.privacy.accessLog" />
              <p style="font-size: 12px; color: var(--theme-text-secondary); margin-top: 5px;">
                记录数据访问和操作日志
              </p>
            </el-form-item>

            <el-form-item label="数据共享">
              <el-checkbox-group v-model="settings.privacy.dataSharing">
                <el-checkbox label="family">家庭成员</el-checkbox>
                <el-checkbox label="doctor">医生</el-checkbox>
                <el-checkbox label="research">匿名研究</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="自动备份">
              <el-switch v-model="settings.privacy.autoBackup" />
              <div v-if="settings.privacy.autoBackup" style="margin-top: 10px;">
                <el-select v-model="settings.privacy.backupFrequency" style="width: 200px;">
                  <el-option label="每天" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="每月" value="monthly" />
                </el-select>
              </div>
            </el-form-item>

            <el-form-item label="数据保留期">
              <el-select v-model="settings.privacy.dataRetention" style="width: 100%">
                <el-option label="1年" value="1year" />
                <el-option label="3年" value="3years" />
                <el-option label="5年" value="5years" />
                <el-option label="永久保留" value="forever" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notifications">
          <el-form :model="settings.notifications" label-width="120px">
            <el-form-item label="桌面通知">
              <el-switch v-model="settings.notifications.desktop" />
            </el-form-item>

            <el-form-item label="邮件通知">
              <el-switch v-model="settings.notifications.email" />
              <div v-if="settings.notifications.email" style="margin-top: 10px;">
                <el-input
                  v-model="settings.notifications.emailAddress"
                  placeholder="请输入邮箱地址"
                  style="width: 300px;"
                />
              </div>
            </el-form-item>

            <el-form-item label="短信通知">
              <el-switch v-model="settings.notifications.sms" />
              <div v-if="settings.notifications.sms" style="margin-top: 10px;">
                <el-input
                  v-model="settings.notifications.phoneNumber"
                  placeholder="请输入手机号码"
                  style="width: 300px;"
                />
              </div>
            </el-form-item>

            <el-form-item label="通知类型">
              <el-checkbox-group v-model="settings.notifications.types">
                <el-checkbox label="reminder">健康提醒</el-checkbox>
                <el-checkbox label="abnormal">异常数据</el-checkbox>
                <el-checkbox label="sync">同步状态</el-checkbox>
                <el-checkbox label="system">系统消息</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="免打扰时间">
              <el-time-picker
                v-model="settings.notifications.quietHours"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 导入导出 -->
        <el-tab-pane label="导入导出" name="import-export">
          <div class="import-export-section">
            <div class="section-item">
              <h4>数据导出</h4>
              <p>导出您的健康档案数据，包括报告、设备数据等</p>
              <el-button type="primary" @click="exportData" :loading="exporting">
                导出所有数据
              </el-button>
            </div>

            <div class="section-item">
              <h4>数据导入</h4>
              <p>从备份文件或其他系统导入健康数据</p>
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :on-change="handleImportFile"
                accept=".json,.csv,.xlsx"
                :limit="1"
              >
                <el-button type="primary">选择导入文件</el-button>
              </el-upload>
            </div>

            <div class="section-item">
              <h4>数据备份</h4>
              <p>将数据备份到云端，确保数据安全</p>
              <el-button @click="backupData" :loading="backing">
                立即备份
              </el-button>
            </div>

            <div class="section-item">
              <h4>数据清理</h4>
              <p>清理过期或无用的数据，释放存储空间</p>
              <el-button type="warning" @click="cleanData" :loading="cleaning">
                清理数据
              </el-button>
            </div>
          </div>
        </el-tab-pane>

        <!-- 高级设置 -->
        <el-tab-pane label="高级设置" name="advanced">
          <el-form :model="settings.advanced" label-width="120px">
            <el-form-item label="开发者模式">
              <el-switch v-model="settings.advanced.developerMode" />
              <p style="font-size: 12px; color: var(--theme-text-secondary); margin-top: 5px;">
                启用调试功能和详细日志
              </p>
            </el-form-item>

            <el-form-item label="API端点">
              <el-input
                v-model="settings.advanced.apiEndpoint"
                placeholder="https://api.example.com"
              />
            </el-form-item>

            <el-form-item label="缓存大小">
              <el-slider
                v-model="settings.advanced.cacheSize"
                :min="50"
                :max="500"
                :step="50"
                show-stops
                :format-tooltip="(val) => `${val}MB`"
              />
            </el-form-item>

            <el-form-item label="日志级别">
              <el-select v-model="settings.advanced.logLevel" style="width: 100%">
                <el-option label="错误" value="error" />
                <el-option label="警告" value="warn" />
                <el-option label="信息" value="info" />
                <el-option label="调试" value="debug" />
              </el-select>
            </el-form-item>

            <el-form-item label="重置设置">
              <el-button type="danger" @click="resetSettings" :loading="resetting">
                恢复默认设置
              </el-button>
              <p style="font-size: 12px; color: var(--color-danger); margin-top: 5px;">
                注意：此操作将清除所有自定义设置
              </p>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">取消</el-button>
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存设置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadFile, UploadInstance } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'settingsChanged', settings: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const saving = ref(false)
const exporting = ref(false)
const backing = ref(false)
const cleaning = ref(false)
const resetting = ref(false)
const activeTab = ref('general')

const uploadRef = ref<UploadInstance>()

// 设置数据
const settings = reactive({
  general: {
    defaultView: 'timeline',
    pageSize: 20,
    autoRefresh: true,
    refreshInterval: 300,
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai'
  },
  privacy: {
    dataEncryption: true,
    accessLog: true,
    dataSharing: ['family'],
    autoBackup: true,
    backupFrequency: 'weekly',
    dataRetention: '5years'
  },
  notifications: {
    desktop: true,
    email: false,
    sms: false,
    emailAddress: '',
    phoneNumber: '',
    types: ['reminder', 'abnormal'],
    quietHours: ['22:00', '08:00']
  },
  advanced: {
    developerMode: false,
    apiEndpoint: '',
    cacheSize: 100,
    logLevel: 'info'
  }
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadSettings()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadSettings = () => {
  // 从本地存储或API加载设置
  const savedSettings = localStorage.getItem('healthArchiveSettings')
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings))
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    // 保存到本地存储
    localStorage.setItem('healthArchiveSettings', JSON.stringify(settings))
    
    // 模拟保存到服务器
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('设置保存成功')
    emit('settingsChanged', settings)
    visible.value = false
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const exportData = async () => {
  exporting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const exportData = {
      settings,
      exportDate: new Date().toISOString(),
      version: '1.0'
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `健康档案设置_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('设置导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleImportFile = async (file: UploadFile) => {
  try {
    if (!file.raw) return
    
    const text = await file.raw.text()
    const importData = JSON.parse(text)
    
    if (importData.settings) {
      Object.assign(settings, importData.settings)
      ElMessage.success('设置导入成功')
    } else {
      ElMessage.error('文件格式不正确')
    }
  } catch (error) {
    ElMessage.error('导入失败')
  }
}

const backupData = async () => {
  backing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据备份成功')
  } catch (error) {
    ElMessage.error('备份失败')
  } finally {
    backing.value = false
  }
}

const cleanData = async () => {
  try {
    await ElMessageBox.confirm('确定要清理过期数据吗？', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    cleaning.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('数据清理完成')
  } catch (error) {
    // 用户取消
  } finally {
    cleaning.value = false
  }
}

const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复默认设置吗？所有自定义配置将被清除。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    resetting.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 重置为默认值
    Object.assign(settings, {
      general: {
        defaultView: 'timeline',
        pageSize: 20,
        autoRefresh: true,
        refreshInterval: 300,
        theme: 'light',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai'
      },
      privacy: {
        dataEncryption: true,
        accessLog: true,
        dataSharing: ['family'],
        autoBackup: true,
        backupFrequency: 'weekly',
        dataRetention: '5years'
      },
      notifications: {
        desktop: true,
        email: false,
        sms: false,
        emailAddress: '',
        phoneNumber: '',
        types: ['reminder', 'abnormal'],
        quietHours: ['22:00', '08:00']
      },
      advanced: {
        developerMode: false,
        apiEndpoint: '',
        cacheSize: 100,
        logLevel: 'info'
      }
    })
    
    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    // 用户取消
  } finally {
    resetting.value = false
  }
}

const handleClose = () => {
  if (saving.value) {
    ElMessage.warning('正在保存中，请稍候...')
    return
  }
  visible.value = false
}
</script>

<style scoped>
.settings-content {
  max-height: 600px;
  overflow-y: auto;
}

.import-export-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-item {
  padding: 16px;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-secondary);
}

.section-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.section-item p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--theme-text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
