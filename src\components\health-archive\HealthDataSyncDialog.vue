<template>
  <el-dialog
    v-model="visible"
    title="健康数据同步"
    width="700px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="sync-content">
      <!-- 同步状态概览 -->
      <div class="sync-overview">
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ syncStats.successCount }}</div>
              <div class="card-label">成功同步</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ syncStats.failedCount }}</div>
              <div class="card-label">同步失败</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon info">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">
                {{ formatLastSync(syncStats.lastSyncTime) }}
              </div>
              <div class="card-label">最后同步</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据源列表 -->
      <div class="data-sources">
        <h4 class="section-title">数据源管理</h4>
        <div class="sources-list">
          <div
            v-for="source in dataSources"
            :key="source.id"
            class="source-item"
            :class="{ selected: selectedSources.includes(source.id) }"
          >
            <div class="source-header">
              <el-checkbox
                :model-value="selectedSources.includes(source.id)"
                @change="toggleSourceSelection(source.id)"
              />
              <div class="source-icon">
                <el-icon>
                  <component :is="getSourceIcon(source.type)" />
                </el-icon>
              </div>
              <div class="source-info">
                <div class="source-name">{{ source.name }}</div>
                <div class="source-type">
                  {{ getSourceTypeName(source.type) }}
                </div>
              </div>
              <div class="source-status">
                <el-tag :type="getStatusColor(source.status)" size="small">
                  {{ getStatusName(source.status) }}
                </el-tag>
              </div>
            </div>

            <div class="source-details">
              <div class="detail-item">
                <span class="detail-label">记录数量:</span>
                <span class="detail-value">{{ source.recordCount }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">最后同步:</span>
                <span class="detail-value">{{
                  formatDateTime(source.lastSync)
                }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">同步频率:</span>
                <span class="detail-value">{{ source.syncFrequency }}</span>
              </div>
            </div>

            <div class="source-actions">
              <el-button size="small" @click="configureSource(source)">
                配置
              </el-button>
              <el-button
                size="small"
                @click="testConnection(source)"
                :loading="source.testing"
              >
                测试连接
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="syncSingleSource(source)"
                :loading="source.syncing"
              >
                立即同步
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 同步设置 -->
      <div class="sync-settings">
        <h4 class="section-title">同步设置</h4>
        <el-form :model="syncConfig" label-width="120px">
          <el-form-item label="同步模式">
            <el-radio-group v-model="syncConfig.mode">
              <el-radio label="incremental">增量同步</el-radio>
              <el-radio label="full">完全同步</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker
              v-model="syncConfig.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="数据类型">
            <el-checkbox-group v-model="syncConfig.dataTypes">
              <el-checkbox label="vital_signs">生命体征</el-checkbox>
              <el-checkbox label="exercise">运动数据</el-checkbox>
              <el-checkbox label="sleep">睡眠数据</el-checkbox>
              <el-checkbox label="diet">饮食记录</el-checkbox>
              <el-checkbox label="medication">用药记录</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="冲突处理">
            <el-select
              v-model="syncConfig.conflictResolution"
              style="width: 100%"
            >
              <el-option label="保留最新数据" value="keep_latest" />
              <el-option label="保留本地数据" value="keep_local" />
              <el-option label="保留远程数据" value="keep_remote" />
              <el-option label="手动处理" value="manual" />
            </el-select>
          </el-form-item>

          <el-form-item label="自动同步">
            <el-switch v-model="syncConfig.autoSync" />
            <el-select
              v-if="syncConfig.autoSync"
              v-model="syncConfig.autoSyncInterval"
              style="margin-left: 12px; width: 150px"
            >
              <el-option label="每小时" value="hourly" />
              <el-option label="每天" value="daily" />
              <el-option label="每周" value="weekly" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 同步进度 -->
      <div v-if="syncing" class="sync-progress">
        <h4 class="section-title">同步进度</h4>
        <div class="progress-info">
          <div class="progress-text">
            {{ syncStatus.message }}
          </div>
          <el-progress
            :percentage="syncStatus.progress"
            :status="syncStatus.status === 'error' ? 'exception' : undefined"
          />
        </div>

        <div class="sync-details">
          <div class="detail-row">
            <span>已处理:</span>
            <span>{{ syncStatus.processed }} / {{ syncStatus.total }}</span>
          </div>
          <div class="detail-row">
            <span>成功:</span>
            <span class="success-text">{{ syncStatus.success }}</span>
          </div>
          <div class="detail-row">
            <span>失败:</span>
            <span class="error-text">{{ syncStatus.failed }}</span>
          </div>
          <div class="detail-row">
            <span>预计剩余时间:</span>
            <span>{{ syncStatus.estimatedTime }}</span>
          </div>
        </div>
      </div>

      <!-- 同步结果 -->
      <div v-if="syncResult" class="sync-result">
        <el-result
          :icon="syncResult.success ? 'success' : 'error'"
          :title="syncResult.success ? '同步完成' : '同步失败'"
          :sub-title="syncResult.message"
        >
          <template #extra>
            <el-button
              v-if="syncResult.success"
              type="primary"
              @click="viewSyncReport"
            >
              查看同步报告
            </el-button>
            <el-button
              v-if="!syncResult.success"
              type="primary"
              @click="retrySync"
            >
              重新同步
            </el-button>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="syncing">
          {{ syncing ? "同步中..." : "关闭" }}
        </el-button>
        <el-button
          type="primary"
          @click="startSync"
          :loading="syncing"
          :disabled="selectedSources.length === 0 || syncing"
        >
          {{ syncing ? "同步中..." : "开始同步" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Check,
  Warning,
  Clock,
  Phone,
  Watch,
  Scale,
  Monitor,
} from "@element-plus/icons-vue";
import {
  healthArchiveService,
  type HealthSyncResult,
} from "@/services/healthArchiveService";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success", result: HealthSyncResult): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const syncing = ref(false);
const selectedSources = ref<string[]>([]);

// 同步统计
const syncStats = reactive({
  successCount: 156,
  failedCount: 3,
  lastSyncTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
});

// 数据源
const dataSources = ref([
  {
    id: "fitness_band",
    name: "智能手环",
    type: "device",
    status: "connected",
    recordCount: 1250,
    lastSync: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
    syncFrequency: "每小时",
    testing: false,
    syncing: false,
  },
  {
    id: "blood_pressure",
    name: "血压计",
    type: "device",
    status: "connected",
    recordCount: 89,
    lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
    syncFrequency: "手动",
    testing: false,
    syncing: false,
  },
  {
    id: "smart_scale",
    name: "智能体重秤",
    type: "device",
    status: "disconnected",
    recordCount: 45,
    lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
    syncFrequency: "每天",
    testing: false,
    syncing: false,
  },
  {
    id: "health_app",
    name: "健康应用",
    type: "app",
    status: "partial",
    recordCount: 320,
    lastSync: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小时前
    syncFrequency: "每天",
    testing: false,
    syncing: false,
  },
]);

// 同步配置
const syncConfig = reactive({
  mode: "incremental",
  dateRange: null as [Date, Date] | null,
  dataTypes: ["vital_signs", "exercise", "sleep"],
  conflictResolution: "keep_latest",
  autoSync: true,
  autoSyncInterval: "daily",
});

// 同步状态
const syncStatus = reactive({
  message: "",
  progress: 0,
  status: "normal",
  processed: 0,
  total: 0,
  success: 0,
  failed: 0,
  estimatedTime: "",
});

// 同步结果
const syncResult = ref<{ success: boolean; message: string } | null>(null);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
  if (!val) {
    resetState();
  }
});

const resetState = () => {
  syncing.value = false;
  syncResult.value = null;
  selectedSources.value = [];
};

const toggleSourceSelection = (sourceId: string) => {
  const index = selectedSources.value.indexOf(sourceId);
  if (index > -1) {
    selectedSources.value.splice(index, 1);
  } else {
    selectedSources.value.push(sourceId);
  }
};

const getSourceIcon = (type: string) => {
  const icons: Record<string, any> = {
    device: Watch,
    app: Phone,
    manual: Monitor,
  };
  return icons[type] || Monitor;
};

const getSourceTypeName = (type: string) => {
  const names: Record<string, string> = {
    device: "硬件设备",
    app: "应用程序",
    manual: "手动录入",
  };
  return names[type] || type;
};

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    connected: "已连接",
    disconnected: "未连接",
    partial: "部分连接",
    error: "连接错误",
  };
  return names[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    connected: "success",
    disconnected: "danger",
    partial: "warning",
    error: "danger",
  };
  return colors[status] || "";
};

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

const formatLastSync = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));

  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60));
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    const days = Math.floor(hours / 24);
    return `${days}天前`;
  }
};

const configureSource = (source: any) => {
  ElMessage.info(`配置 ${source.name} 的同步设置`);
};

const testConnection = async (source: any) => {
  source.testing = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    source.status = "connected";
    ElMessage.success(`${source.name} 连接测试成功`);
  } catch (error) {
    source.status = "error";
    ElMessage.error(`${source.name} 连接测试失败`);
  } finally {
    source.testing = false;
  }
};

const syncSingleSource = async (source: any) => {
  source.syncing = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 3000));
    source.lastSync = new Date();
    source.recordCount += Math.floor(Math.random() * 10);
    ElMessage.success(`${source.name} 同步完成`);
  } catch (error) {
    ElMessage.error(`${source.name} 同步失败`);
  } finally {
    source.syncing = false;
  }
};

const startSync = async () => {
  if (selectedSources.value.length === 0) {
    ElMessage.warning("请选择要同步的数据源");
    return;
  }

  syncing.value = true;
  syncResult.value = null;

  try {
    // 模拟同步进度
    await simulateSyncProgress();

    // 调用同步API
    const result = await healthArchiveService.syncHealthData(
      selectedSources.value
    );

    syncResult.value = {
      success: result.success,
      message: `成功同步 ${result.syncedCount} 条记录，失败 ${result.failedCount} 条`,
    };

    // 更新统计数据
    syncStats.successCount += result.syncedCount;
    syncStats.failedCount += result.failedCount;
    syncStats.lastSyncTime = result.lastSyncTime;

    ElMessage.success("数据同步完成");
    emit("success", result);
  } catch (error) {
    console.error("同步失败:", error);
    syncResult.value = {
      success: false,
      message: "同步过程中发生错误，请重试",
    };
    ElMessage.error("同步失败，请重试");
  } finally {
    syncing.value = false;
  }
};

const simulateSyncProgress = async () => {
  const totalSteps = 100;
  syncStatus.total = totalSteps;
  syncStatus.processed = 0;
  syncStatus.success = 0;
  syncStatus.failed = 0;

  for (let i = 0; i <= totalSteps; i++) {
    await new Promise((resolve) => setTimeout(resolve, 50));

    syncStatus.processed = i;
    syncStatus.progress = Math.round((i / totalSteps) * 100);

    if (i < totalSteps) {
      syncStatus.success = Math.floor(i * 0.95);
      syncStatus.failed = i - syncStatus.success;

      const remaining = totalSteps - i;
      const estimatedSeconds = Math.ceil(remaining * 0.05);
      syncStatus.estimatedTime = `${estimatedSeconds}秒`;

      if (i < 30) {
        syncStatus.message = "正在连接数据源...";
      } else if (i < 70) {
        syncStatus.message = "正在获取数据...";
      } else {
        syncStatus.message = "正在处理数据...";
      }
    } else {
      syncStatus.message = "同步完成";
      syncStatus.estimatedTime = "0秒";
    }
  }
};

const viewSyncReport = () => {
  ElMessage.info("查看同步报告功能开发中...");
};

const retrySync = () => {
  syncResult.value = null;
  startSync();
};

const handleClose = () => {
  if (syncing.value) {
    ElMessage.warning("正在同步中，请稍候...");
    return;
  }
  visible.value = false;
};
</script>

<style scoped>
.sync-content {
  max-height: 700px;
  overflow-y: auto;
}

.sync-overview {
  margin-bottom: 24px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.card-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 24px;
}

.card-icon.success {
  background: var(--color-success-light);
  color: var(--color-success);
}

.card-icon.warning {
  background: var(--color-warning-light);
  color: var(--color-warning);
}

.card-icon.info {
  background: var(--color-info-light);
  color: var(--color-info);
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--theme-text-primary);
}

.card-label {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.source-item {
  padding: 16px;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  transition: all 0.3s;
}

.source-item.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.source-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.source-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-bg-secondary);
  border-radius: 50%;
  color: var(--color-primary);
}

.source-info {
  flex: 1;
}

.source-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.source-type {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.source-details {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
  padding-left: 52px;
}

.detail-item {
  font-size: 12px;
}

.detail-label {
  color: var(--theme-text-secondary);
}

.detail-value {
  color: var(--theme-text-primary);
  font-weight: 500;
}

.source-actions {
  display: flex;
  gap: 8px;
  padding-left: 52px;
}

.sync-settings {
  margin-bottom: 24px;
}

.sync-progress {
  margin-bottom: 24px;
}

.progress-info {
  margin-bottom: 16px;
}

.progress-text {
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--theme-text-primary);
}

.sync-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.success-text {
  color: var(--color-success);
  font-weight: 500;
}

.error-text {
  color: var(--color-danger);
  font-weight: 500;
}

.sync-result {
  margin-bottom: 24px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
