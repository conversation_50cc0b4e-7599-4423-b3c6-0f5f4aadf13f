<template>
  <el-dialog
    v-model="visible"
    title="编辑健康档案"
    width="600px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="edit-archive-content">
      <el-form :model="archiveForm" :rules="archiveRules" ref="formRef" label-width="100px">
        <el-form-item label="档案名称" prop="name">
          <el-input
            v-model="archiveForm.name"
            placeholder="请输入档案名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="档案类型" prop="type">
          <el-select v-model="archiveForm.type" placeholder="选择档案类型" style="width: 100%">
            <el-option label="体检报告" value="checkup" />
            <el-option label="化验单" value="lab" />
            <el-option label="影像资料" value="imaging" />
            <el-option label="处方单" value="prescription" />
            <el-option label="病历记录" value="medical_record" />
            <el-option label="疫苗接种" value="vaccination" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="所属成员" prop="memberId">
          <el-select v-model="archiveForm.memberId" placeholder="选择家庭成员" style="width: 100%">
            <el-option
              v-for="member in familyMembers"
              :key="member.id"
              :label="member.name"
              :value="member.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="档案日期" prop="date">
          <el-date-picker
            v-model="archiveForm.date"
            type="date"
            placeholder="选择档案日期"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="医院/机构">
          <el-input
            v-model="archiveForm.hospital"
            placeholder="请输入医院或医疗机构名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="医生姓名">
          <el-input
            v-model="archiveForm.doctor"
            placeholder="请输入医生姓名"
            clearable
          />
        </el-form-item>

        <el-form-item label="档案描述">
          <el-input
            v-model="archiveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入档案描述（可选）"
          />
        </el-form-item>

        <el-form-item label="现有文件">
          <div class="existing-files">
            <div
              v-for="file in archiveForm.existingFiles"
              :key="file.id"
              class="file-item"
            >
              <div class="file-info">
                <el-icon><Document /></el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ file.size }}</span>
              </div>
              <div class="file-actions">
                <el-button size="small" @click="downloadFile(file)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button size="small" type="danger" @click="removeExistingFile(file)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="添加文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
            multiple
            :limit="5"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、图片、Word 格式，最多上传5个文件，单个文件不超过20MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="标签">
          <el-tag
            v-for="tag in archiveForm.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            class="tag-input"
            size="small"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
            style="width: 100px;"
          />
          <el-button v-else class="button-new-tag" size="small" @click="showInput">
            + 添加标签
          </el-button>
        </el-form-item>

        <el-form-item label="隐私设置">
          <el-radio-group v-model="archiveForm.privacy">
            <el-radio label="private">仅自己可见</el-radio>
            <el-radio label="family">家庭成员可见</el-radio>
            <el-radio label="doctor">医生可见</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="档案状态">
          <el-select v-model="archiveForm.status" placeholder="选择档案状态" style="width: 100%">
            <el-option label="正常" value="normal" />
            <el-option label="有异常" value="abnormal" />
            <el-option label="需要复查" value="recheck" />
            <el-option label="已解析" value="parsed" />
            <el-option label="处理中" value="processing" />
          </el-select>
        </el-form-item>

        <el-form-item label="提醒设置">
          <el-checkbox v-model="archiveForm.enableReminder">启用复查提醒</el-checkbox>
          <div v-if="archiveForm.enableReminder" style="margin-top: 10px;">
            <el-date-picker
              v-model="archiveForm.reminderDate"
              type="date"
              placeholder="选择提醒日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">取消</el-button>
        <el-button type="danger" @click="deleteArchive" :loading="deleting">
          删除档案
        </el-button>
        <el-button type="primary" @click="saveArchive" :loading="saving">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Document, Download, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadInstance } from 'element-plus'

interface Props {
  modelValue: boolean
  archive: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated', archive: any): void
  (e: 'deleted', archiveId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const saving = ref(false)
const deleting = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')

const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const inputRef = ref()

// 家庭成员
const familyMembers = ref([
  { id: '1', name: '张先生' },
  { id: '2', name: '李女士' },
  { id: '3', name: '张小明' }
])

// 档案表单
const archiveForm = reactive({
  id: '',
  name: '',
  type: '',
  memberId: '',
  date: '',
  hospital: '',
  doctor: '',
  description: '',
  existingFiles: [] as any[],
  newFiles: [] as UploadFile[],
  tags: [] as string[],
  privacy: 'family',
  status: 'normal',
  enableReminder: false,
  reminderDate: ''
})

// 表单验证规则
const archiveRules: FormRules = {
  name: [
    { required: true, message: '请输入档案名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择档案类型', trigger: 'change' }
  ],
  memberId: [
    { required: true, message: '请选择所属成员', trigger: 'change' }
  ],
  date: [
    { required: true, message: '请选择档案日期', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.archive) {
    loadArchiveData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

const loadArchiveData = () => {
  if (props.archive) {
    Object.assign(archiveForm, {
      id: props.archive.id,
      name: props.archive.name,
      type: props.archive.type,
      memberId: props.archive.memberId,
      date: props.archive.date,
      hospital: props.archive.hospital || '',
      doctor: props.archive.doctor || '',
      description: props.archive.description || '',
      existingFiles: props.archive.files || [],
      newFiles: [],
      tags: props.archive.tags || [],
      privacy: props.archive.privacy || 'family',
      status: props.archive.status || 'normal',
      enableReminder: props.archive.enableReminder || false,
      reminderDate: props.archive.reminderDate || ''
    })
  }
}

const resetForm = () => {
  Object.assign(archiveForm, {
    id: '',
    name: '',
    type: '',
    memberId: '',
    date: '',
    hospital: '',
    doctor: '',
    description: '',
    existingFiles: [],
    newFiles: [],
    tags: [],
    privacy: 'family',
    status: 'normal',
    enableReminder: false,
    reminderDate: ''
  })
  formRef.value?.resetFields()
  uploadRef.value?.clearFiles()
}

const handleFileChange = (file: UploadFile) => {
  archiveForm.newFiles.push(file)
}

const handleFileRemove = (file: UploadFile) => {
  const index = archiveForm.newFiles.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    archiveForm.newFiles.splice(index, 1)
  }
}

const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/jpg',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)
  
  if (!isValidType) {
    ElMessage.error('只支持 PDF、图片、Word 格式的文件')
    return false
  }
  
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('文件大小不能超过 20MB')
    return false
  }
  
  return true
}

const removeExistingFile = async (file: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = archiveForm.existingFiles.findIndex(f => f.id === file.id)
    if (index > -1) {
      archiveForm.existingFiles.splice(index, 1)
      ElMessage.success('文件删除成功')
    }
  } catch (error) {
    // 用户取消
  }
}

const downloadFile = (file: any) => {
  ElMessage.info(`下载文件: ${file.name}`)
  // 这里实现文件下载逻辑
}

const removeTag = (tag: string) => {
  const index = archiveForm.tags.indexOf(tag)
  if (index > -1) {
    archiveForm.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !archiveForm.tags.includes(inputValue.value)) {
    archiveForm.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const saveArchive = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const updatedArchive = {
      ...props.archive,
      ...archiveForm,
      files: [...archiveForm.existingFiles, ...archiveForm.newFiles.map(f => ({
        id: Date.now().toString(),
        name: f.name,
        size: formatFileSize(f.size || 0),
        type: f.raw?.type
      }))],
      updatedAt: new Date()
    }
    
    ElMessage.success('档案修改成功')
    emit('updated', updatedArchive)
    visible.value = false
  } catch (error) {
    console.error('保存档案失败:', error)
    ElMessage.error('保存档案失败')
  } finally {
    saving.value = false
  }
}

const deleteArchive = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个档案吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    deleting.value = true
    
    // 模拟删除过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('档案删除成功')
    emit('deleted', archiveForm.id)
    visible.value = false
  } catch (error) {
    // 用户取消
  } finally {
    deleting.value = false
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleClose = () => {
  if (saving.value || deleting.value) {
    ElMessage.warning('正在处理中，请稍候...')
    return
  }
  visible.value = false
}
</script>

<style scoped>
.edit-archive-content {
  max-height: 600px;
  overflow-y: auto;
}

.existing-files {
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  padding: 12px;
  background: var(--theme-bg-secondary);
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--theme-border-light);
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.file-size {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-demo {
  width: 100%;
}

.tag-input {
  width: 100px;
  margin-left: 8px;
  vertical-align: bottom;
}

.button-new-tag {
  margin-left: 8px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
