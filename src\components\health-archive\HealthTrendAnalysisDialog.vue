<template>
  <el-dialog
    v-model="visible"
    title="健康趋势分析"
    width="1000px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="trend-content">
      <!-- 分析选项 -->
      <div class="analysis-options">
        <el-form :model="analysisForm" label-width="100px" inline>
          <el-form-item label="分析类型">
            <el-select v-model="analysisForm.type" placeholder="选择分析类型" style="width: 150px">
              <el-option label="血压趋势" value="blood_pressure" />
              <el-option label="血糖趋势" value="blood_glucose" />
              <el-option label="体重变化" value="weight" />
              <el-option label="运动数据" value="exercise" />
              <el-option label="睡眠质量" value="sleep" />
              <el-option label="心率变化" value="heart_rate" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-select v-model="analysisForm.period" placeholder="选择时间范围" style="width: 120px">
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近3个月" value="3m" />
              <el-option label="最近6个月" value="6m" />
              <el-option label="最近1年" value="1y" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="家庭成员">
            <el-select v-model="analysisForm.memberId" placeholder="选择成员" style="width: 120px">
              <el-option label="全部成员" value="" />
              <el-option
                v-for="member in familyMembers"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="generateAnalysis" :loading="loading">
              生成分析
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 趋势图表 -->
      <div v-if="analysisResult" class="trend-charts">
        <div class="chart-container">
          <h4 class="chart-title">{{ getAnalysisTitle() }}</h4>
          <div class="chart-placeholder">
            <div class="chart-mock">
              <div class="chart-line"></div>
              <div class="chart-points">
                <div v-for="i in 10" :key="i" class="chart-point" :style="getPointStyle(i)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析摘要 -->
        <div class="analysis-summary">
          <h4 class="summary-title">分析摘要</h4>
          <div class="summary-cards">
            <div class="summary-card">
              <div class="card-label">平均值</div>
              <div class="card-value">{{ analysisResult.average }}</div>
            </div>
            <div class="summary-card">
              <div class="card-label">最高值</div>
              <div class="card-value">{{ analysisResult.max }}</div>
            </div>
            <div class="summary-card">
              <div class="card-label">最低值</div>
              <div class="card-value">{{ analysisResult.min }}</div>
            </div>
            <div class="summary-card">
              <div class="card-label">变化趋势</div>
              <div class="card-value" :class="getTrendClass()">
                {{ analysisResult.trend }}
              </div>
            </div>
          </div>
        </div>

        <!-- 健康建议 -->
        <div class="health-suggestions">
          <h4 class="suggestions-title">健康建议</h4>
          <div class="suggestions-list">
            <div
              v-for="(suggestion, index) in analysisResult.suggestions"
              :key="index"
              class="suggestion-item"
            >
              <el-icon class="suggestion-icon"><InfoFilled /></el-icon>
              <span class="suggestion-text">{{ suggestion }}</span>
            </div>
          </div>
        </div>

        <!-- 异常提醒 -->
        <div v-if="analysisResult.alerts.length > 0" class="health-alerts">
          <h4 class="alerts-title">异常提醒</h4>
          <div class="alerts-list">
            <el-alert
              v-for="(alert, index) in analysisResult.alerts"
              :key="index"
              :title="alert.title"
              :description="alert.description"
              :type="alert.type"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!analysisResult && !loading" class="empty-state">
        <el-empty description="请选择分析选项并点击生成分析" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="analysisResult" type="primary" @click="exportAnalysis">
          导出报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)

// 分析表单
const analysisForm = reactive({
  type: 'blood_pressure',
  period: '30d',
  memberId: ''
})

// 家庭成员
const familyMembers = ref([
  { id: 1, name: '张先生' },
  { id: 2, name: '李女士' },
  { id: 3, name: '张小明' }
])

// 分析结果
const analysisResult = ref<{
  average: string
  max: string
  min: string
  trend: string
  suggestions: string[]
  alerts: Array<{
    title: string
    description: string
    type: 'success' | 'warning' | 'error' | 'info'
  }>
} | null>(null)

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (!val) {
    resetState()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const resetState = () => {
  analysisResult.value = null
}

const generateAnalysis = async () => {
  loading.value = true
  
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟分析结果
    analysisResult.value = {
      average: getAnalysisValue('average'),
      max: getAnalysisValue('max'),
      min: getAnalysisValue('min'),
      trend: getTrendValue(),
      suggestions: getSuggestions(),
      alerts: getAlerts()
    }
    
    ElMessage.success('趋势分析生成成功')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('趋势分析生成失败')
  } finally {
    loading.value = false
  }
}

const getAnalysisTitle = () => {
  const titles: Record<string, string> = {
    blood_pressure: '血压趋势分析',
    blood_glucose: '血糖趋势分析',
    weight: '体重变化趋势',
    exercise: '运动数据趋势',
    sleep: '睡眠质量趋势',
    heart_rate: '心率变化趋势'
  }
  return titles[analysisForm.type] || '健康趋势分析'
}

const getAnalysisValue = (type: string) => {
  const values: Record<string, Record<string, string>> = {
    blood_pressure: {
      average: '120/80 mmHg',
      max: '135/85 mmHg',
      min: '110/75 mmHg'
    },
    blood_glucose: {
      average: '5.8 mmol/L',
      max: '7.2 mmol/L',
      min: '4.5 mmol/L'
    },
    weight: {
      average: '68.5 kg',
      max: '70.2 kg',
      min: '67.1 kg'
    },
    exercise: {
      average: '8,500 步/天',
      max: '12,000 步',
      min: '5,200 步'
    },
    sleep: {
      average: '7.5 小时',
      max: '9.2 小时',
      min: '6.1 小时'
    },
    heart_rate: {
      average: '72 bpm',
      max: '85 bpm',
      min: '65 bpm'
    }
  }
  return values[analysisForm.type]?.[type] || '--'
}

const getTrendValue = () => {
  const trends = ['上升', '下降', '稳定', '波动']
  return trends[Math.floor(Math.random() * trends.length)]
}

const getTrendClass = () => {
  if (!analysisResult.value) return ''
  const trend = analysisResult.value.trend
  if (trend === '上升') return 'trend-up'
  if (trend === '下降') return 'trend-down'
  if (trend === '稳定') return 'trend-stable'
  return 'trend-fluctuate'
}

const getSuggestions = () => {
  const suggestions: Record<string, string[]> = {
    blood_pressure: [
      '保持规律的作息时间，避免熬夜',
      '适量运动，建议每周3-5次有氧运动',
      '控制盐分摄入，每日不超过6克',
      '保持心情愉悦，避免过度紧张'
    ],
    blood_glucose: [
      '控制碳水化合物摄入，选择低GI食物',
      '餐后适量运动，有助于血糖控制',
      '定期监测血糖，记录变化趋势',
      '保持健康体重，避免肥胖'
    ],
    weight: [
      '保持均衡饮食，控制热量摄入',
      '增加运动量，提高基础代谢',
      '规律作息，保证充足睡眠',
      '多喝水，促进新陈代谢'
    ]
  }
  return suggestions[analysisForm.type] || ['保持健康的生活方式', '定期体检，关注健康指标']
}

const getAlerts = () => {
  const alerts = []
  if (Math.random() > 0.7) {
    alerts.push({
      title: '血压偏高提醒',
      description: '最近几次测量血压偏高，建议及时就医咨询',
      type: 'warning' as const
    })
  }
  if (Math.random() > 0.8) {
    alerts.push({
      title: '异常波动',
      description: '数据波动较大，建议保持规律的生活习惯',
      type: 'info' as const
    })
  }
  return alerts
}

const getPointStyle = (index: number) => {
  const left = (index - 1) * 10 + '%'
  const bottom = Math.random() * 60 + 20 + '%'
  return { left, bottom }
}

const exportAnalysis = () => {
  ElMessage.success('分析报告导出功能开发中...')
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.trend-content {
  max-height: 700px;
  overflow-y: auto;
}

.analysis-options {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.trend-charts {
  margin-bottom: 24px;
}

.chart-container {
  margin-bottom: 24px;
  padding: 20px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 300px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
}

.chart-mock {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  transform: translateY(-50%);
}

.chart-points {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--color-primary);
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.analysis-summary {
  margin-bottom: 24px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.summary-card {
  text-align: center;
  padding: 16px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.card-label {
  font-size: 12px;
  color: var(--theme-text-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--theme-text-primary);
}

.card-value.trend-up {
  color: var(--color-danger);
}

.card-value.trend-down {
  color: var(--color-success);
}

.card-value.trend-stable {
  color: var(--color-info);
}

.card-value.trend-fluctuate {
  color: var(--color-warning);
}

.health-suggestions,
.health-alerts {
  margin-bottom: 24px;
}

.suggestions-title,
.alerts-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
}

.suggestion-icon {
  color: var(--color-primary);
  margin-top: 2px;
  flex-shrink: 0;
}

.suggestion-text {
  font-size: 14px;
  color: var(--theme-text-primary);
  line-height: 1.5;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.empty-state {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
