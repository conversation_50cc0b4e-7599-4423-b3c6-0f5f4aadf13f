<template>
  <el-dialog
    v-model="visible"
    title="编辑家庭信息"
    width="700px"
    :before-close="handleClose"
  >
    <div class="family-edit-content">
      <!-- 家庭基本信息 -->
      <div class="edit-section">
        <h3 class="section-title">基本信息</h3>
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="家庭名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入家庭名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家庭ID">
                <el-input v-model="formData.familyId" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="家庭地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入家庭地址" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮政编码">
                <el-input v-model="formData.zipCode" placeholder="请输入邮政编码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="家庭描述">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入家庭描述"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 家庭设置 -->
      <div class="edit-section">
        <h3 class="section-title">家庭设置</h3>
        <el-form label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="隐私设置">
                <el-select v-model="formData.privacy" placeholder="请选择隐私级别">
                  <el-option label="公开" value="public" />
                  <el-option label="仅家庭成员" value="family" />
                  <el-option label="私密" value="private" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据共享">
                <el-switch
                  v-model="formData.dataSharing"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="健康提醒">
                <el-switch
                  v-model="formData.healthReminder"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用药提醒">
                <el-switch
                  v-model="formData.medicineReminder"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="体检提醒">
                <el-switch
                  v-model="formData.checkupReminder"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急通知">
                <el-switch
                  v-model="formData.emergencyNotification"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 紧急联系人 -->
      <div class="edit-section">
        <h3 class="section-title">紧急联系人</h3>
        <el-form label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人姓名">
                <el-input v-model="formData.emergencyContact.name" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人电话">
                <el-input v-model="formData.emergencyContact.phone" placeholder="请输入联系人电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="与家庭关系">
            <el-input v-model="formData.emergencyContact.relationship" placeholder="请输入与家庭的关系" />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  familyId?: number | string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const saving = ref(false)
const formRef = ref()

const formData = reactive({
  familyId: 'FAM001',
  name: '张家大院',
  address: '北京市朝阳区某某街道123号',
  phone: '138-0000-0000',
  zipCode: '100000',
  description: '我们是一个温馨的大家庭，关爱彼此的健康',
  privacy: 'family',
  dataSharing: true,
  healthReminder: true,
  medicineReminder: true,
  checkupReminder: true,
  emergencyNotification: true,
  emergencyContact: {
    name: '王医生',
    phone: '139-0000-0000',
    relationship: '家庭医生'
  }
})

const rules = {
  name: [
    { required: true, message: '请输入家庭名称', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.familyId) {
    loadFamilyData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadFamilyData = async () => {
  // 模拟加载家庭数据
  // 实际项目中这里会调用API获取数据
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('家庭信息保存成功')
    emit('success')
    visible.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.family-edit-content {
  max-height: 600px;
  overflow-y: auto;
}

.edit-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
