<template>
  <nav class="breadcrumb" aria-label="面包屑导航">
    <ol class="breadcrumb-list">
      <li
        v-for="(item, index) in breadcrumbItems"
        :key="index"
        class="breadcrumb-item"
        :class="{ 'is-active': index === breadcrumbItems.length - 1 }"
      >
        <router-link
          v-if="item.path && index !== breadcrumbItems.length - 1"
          :to="item.path"
          class="breadcrumb-link"
        >
          <component v-if="item.icon" :is="item.icon" class="breadcrumb-icon" />
          {{ item.title }}
        </router-link>
        <span v-else class="breadcrumb-text">
          <component v-if="item.icon" :is="item.icon" class="breadcrumb-icon" />
          {{ item.title }}
        </span>
        <component
          v-if="index < breadcrumbItems.length - 1"
          :is="separator"
          class="breadcrumb-separator"
        />
      </li>
    </ol>
    
    <!-- 返回按钮 -->
    <button
      v-if="showBackButton && canGoBack"
      class="back-button"
      @click="goBack"
      title="返回上一页"
    >
      <ArrowLeft class="back-icon" />
      <span class="back-text">返回</span>
    </button>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

interface BreadcrumbItem {
  title: string;
  path?: string;
  icon?: any;
}

interface Props {
  items?: BreadcrumbItem[];
  separator?: any;
  showBackButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  separator: ArrowRight,
  showBackButton: true
});

const route = useRoute();
const router = useRouter();

// 生成面包屑项目
const breadcrumbItems = computed(() => {
  if (props.items) {
    return props.items;
  }

  // 根据路由自动生成面包屑
  const pathSegments = route.path.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [
    { title: '首页', path: '/dashboard', icon: 'House' }
  ];

  // 路由映射
  const routeMap: Record<string, { title: string; icon?: string }> = {
    'dashboard': { title: '首页', icon: 'House' },
    'archive': { title: '档案', icon: 'User' },
    'family': { title: '家庭', icon: 'UserFilled' },
    'assistant': { title: '助手', icon: 'ChatDotRound' },
    'calendar': { title: '日历', icon: 'Calendar' },
    'activities': { title: '活动', icon: 'Trophy' },
    'settings': { title: '设置', icon: 'Setting' },
    'personal-info': { title: '个人信息', icon: 'User' },
    'health-records': { title: '健康记录', icon: 'Document' },
    'medical-history': { title: '病史档案', icon: 'FolderOpened' },
    'health-overview': { title: '健康概要', icon: 'DataAnalysis' },
    'emergency-card': { title: '紧急资料卡', icon: 'Warning' },
    'activity-list': { title: '活动列表', icon: 'List' },
    'activity-analysis': { title: '数据分析', icon: 'TrendCharts' },
    'activity-goals': { title: '目标设置', icon: 'Flag' }
  };

  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const routeInfo = routeMap[segment];
    
    if (routeInfo) {
      items.push({
        title: routeInfo.title,
        path: index === pathSegments.length - 1 ? undefined : currentPath,
        icon: routeInfo.icon
      });
    }
  });

  return items;
});

// 是否可以返回
const canGoBack = computed(() => {
  return window.history.length > 1;
});

// 返回上一页
const goBack = () => {
  if (canGoBack.value) {
    router.back();
  }
};
</script>

<style scoped>
.breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  margin-bottom: var(--spacing-4);
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-2);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  color: var(--theme-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-normal);
}

.breadcrumb-link:hover {
  color: var(--color-primary);
}

.breadcrumb-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.breadcrumb-icon {
  width: 14px;
  height: 14px;
}

.breadcrumb-separator {
  width: 12px;
  height: 12px;
  color: var(--theme-text-tertiary);
}

.breadcrumb-item.is-active .breadcrumb-text {
  color: var(--color-primary);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.back-button:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: var(--color-primary-light);
}

.back-icon {
  width: 14px;
  height: 14px;
}

.back-text {
  font-weight: var(--font-weight-medium);
}
</style>
