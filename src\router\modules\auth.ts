import type { RouteRecordRaw } from 'vue-router';

// 认证相关路由
export const authRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录 - 家庭健康管理系统',
      requiresGuest: true,
      layout: 'auth'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册 - 家庭健康管理系统',
      requiresGuest: true,
      layout: 'auth'
    }
  },
  {
    path: '/onboarding',
    name: 'Onboarding',
    component: () => import('@/views/auth/Onboarding.vue'),
    meta: {
      title: '产品引导 - 家庭健康管理系统',
      requiresGuest: true,
      layout: 'auth'
    }
  },
  {
    path: '/family-setup',
    name: 'FamilySetup',
    component: () => import('@/views/auth/FamilySetup.vue'),
    meta: {
      title: '家庭设置 - 家庭健康管理系统',
      requiresAuth: true,
      layout: 'auth'
    }
  }
];
