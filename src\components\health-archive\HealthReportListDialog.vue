<template>
  <el-dialog
    v-model="visible"
    title="健康报告管理"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="report-list-content">
      <!-- 筛选工具栏 -->
      <div class="toolbar">
        <div class="filters">
          <el-select
            v-model="filters.memberId"
            placeholder="选择成员"
            clearable
            @change="loadReports"
          >
            <el-option label="全部成员" value="" />
            <el-option
              v-for="member in familyMembers"
              :key="member.id"
              :label="member.name"
              :value="member.id"
            />
          </el-select>

          <el-select
            v-model="filters.type"
            placeholder="报告类型"
            clearable
            @change="loadReports"
          >
            <el-option label="全部类型" value="" />
            <el-option label="体检报告" value="checkup" />
            <el-option label="化验报告" value="lab" />
            <el-option label="影像报告" value="imaging" />
            <el-option label="处方单" value="prescription" />
            <el-option label="其他" value="other" />
          </el-select>

          <el-select
            v-model="filters.status"
            placeholder="处理状态"
            clearable
            @change="loadReports"
          >
            <el-option label="全部状态" value="" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="处理失败" value="failed" />
            <el-option label="需要审核" value="manual_review" />
          </el-select>

          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadReports"
          />
        </div>

        <div class="actions">
          <el-button type="primary" @click="showUploadDialog = true">
            <el-icon><Plus /></el-icon>
            上传报告
          </el-button>
          <el-button @click="loadReports">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 报告列表 -->
      <div class="reports-list">
        <el-table
          :data="reports"
          style="width: 100%"
          v-loading="loading"
          @row-click="handleRowClick"
        >
          <el-table-column label="缩略图" width="80">
            <template #default="{ row }">
              <div class="thumbnail">
                <img
                  v-if="row.thumbnailUrl"
                  :src="row.thumbnailUrl"
                  :alt="row.name"
                  class="thumbnail-img"
                />
                <el-icon v-else class="thumbnail-icon">
                  <Document />
                </el-icon>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="报告名称" min-width="200">
            <template #default="{ row }">
              <div class="report-name">
                <div class="name">{{ row.name }}</div>
                <div class="meta">
                  {{ formatDate(row.reportDate) }} · {{ row.fileSize }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(row.type)" size="small">
                {{ getTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="memberName" label="成员" width="100" />

          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <div class="status-cell">
                <el-tag :type="getStatusColor(row.status)" size="small">
                  {{ getStatusName(row.status) }}
                </el-tag>
                <el-progress
                  v-if="row.status === 'processing'"
                  :percentage="row.ocrProgress"
                  :stroke-width="4"
                  :show-text="false"
                  class="progress-bar"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="confidence" label="识别准确度" width="120">
            <template #default="{ row }">
              <div v-if="row.status === 'completed'" class="confidence">
                <span class="confidence-value">{{ row.confidence }}%</span>
                <el-icon
                  v-if="row.needsReview"
                  class="review-icon"
                  :title="'需要人工审核'"
                >
                  <Warning />
                </el-icon>
              </div>
              <span v-else class="not-available">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="uploadDate" label="上传时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.uploadDate) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <div class="actions-cell">
                <el-button
                  type="text"
                  size="small"
                  @click.stop="viewReport(row)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="row.status === 'completed'"
                  type="text"
                  size="small"
                  @click.stop="editReport(row)"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click.stop="downloadReport(row)"
                >
                  下载
                </el-button>
                <el-dropdown @command="handleCommand">
                  <el-button type="text" size="small" @click.stop>
                    更多<el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`reprocess-${row.id}`">
                        重新处理
                      </el-dropdown-item>
                      <el-dropdown-item :command="`share-${row.id}`">
                        分享报告
                      </el-dropdown-item>
                      <el-dropdown-item :command="`delete-${row.id}`" divided>
                        删除报告
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadReports"
            @current-change="loadReports"
          />
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <HealthReportUploadDialog
      v-model="showUploadDialog"
      @success="handleUploadSuccess"
    />

    <!-- 报告详情对话框 -->
    <!-- <HealthReportDetailDialog
      v-model="showDetailDialog"
      :report-id="selectedReportId"
      @updated="loadReports"
    /> -->

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Refresh,
  Document,
  Warning,
  ArrowDown,
} from "@element-plus/icons-vue";
import {
  healthArchiveService,
  type HealthReport,
} from "@/services/healthArchiveService";
import HealthReportUploadDialog from "./HealthReportUploadDialog.vue";
// import HealthReportDetailDialog from './HealthReportDetailDialog.vue'

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const showUploadDialog = ref(false);
const showDetailDialog = ref(false);
const selectedReportId = ref("");

// 筛选条件
const filters = reactive({
  memberId: "",
  type: "",
  status: "",
  dateRange: null as [Date, Date] | null,
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 数据
const reports = ref<HealthReport[]>([]);
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      loadReports();
    }
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const loadReports = async () => {
  loading.value = true;
  try {
    const result = await healthArchiveService.getHealthReportList(
      filters.memberId || undefined,
      filters.type || undefined,
      filters.status || undefined,
      currentPage.value,
      pageSize.value
    );

    reports.value = result.reports;
    total.value = result.total;
  } catch (error) {
    console.error("加载报告列表失败:", error);
    ElMessage.error("加载报告列表失败");
  } finally {
    loading.value = false;
  }
};

const getTypeName = (type: string) => {
  const names: Record<string, string> = {
    checkup: "体检",
    lab: "化验",
    imaging: "影像",
    prescription: "处方",
    other: "其他",
  };
  return names[type] || type;
};

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    checkup: "primary",
    lab: "success",
    imaging: "warning",
    prescription: "info",
    other: "",
  };
  return colors[type] || "";
};

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    processing: "处理中",
    completed: "已完成",
    failed: "失败",
    manual_review: "需审核",
  };
  return names[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    processing: "warning",
    completed: "success",
    failed: "danger",
    manual_review: "info",
  };
  return colors[status] || "";
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  }).format(date);
};

const handleRowClick = (row: HealthReport) => {
  viewReport(row);
};

const viewReport = (report: HealthReport) => {
  selectedReportId.value = report.id;
  showDetailDialog.value = true;
};

const editReport = (report: HealthReport) => {
  selectedReportId.value = report.id;
  showDetailDialog.value = true;
};

const downloadReport = (report: HealthReport) => {
  // 模拟下载
  ElMessage.success(`正在下载 ${report.name}`);
};

const handleCommand = async (command: string) => {
  const [action, reportId] = command.split("-");

  switch (action) {
    case "reprocess":
      await reprocessReport(reportId);
      break;
    case "share":
      shareReport(reportId);
      break;
    case "delete":
      await deleteReport(reportId);
      break;
  }
};

const reprocessReport = async (reportId: string) => {
  try {
    await ElMessageBox.confirm("确定要重新处理这个报告吗？", "确认操作", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    ElMessage.success("已开始重新处理报告");
    loadReports();
  } catch (error) {
    // 用户取消
  }
};

const shareReport = (reportId: string) => {
  ElMessage.info("分享功能开发中...");
};

const deleteReport = async (reportId: string) => {
  try {
    await ElMessageBox.confirm(
      "确定要删除这个报告吗？删除后无法恢复。",
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    ElMessage.success("报告删除成功");
    loadReports();
  } catch (error) {
    // 用户取消
  }
};

const handleUploadSuccess = (reportId: string) => {
  showUploadDialog.value = false;
  loadReports();
  ElMessage.success("报告上传成功");
};

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.report-list-content {
  max-height: 700px;
  overflow-y: auto;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.actions {
  display: flex;
  gap: 8px;
}

.thumbnail {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-icon {
  font-size: 24px;
  color: var(--theme-text-secondary);
}

.report-name .name {
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.report-name .meta {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.status-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  width: 80px;
}

.confidence {
  display: flex;
  align-items: center;
  gap: 4px;
}

.confidence-value {
  font-weight: 500;
}

.review-icon {
  color: var(--color-warning);
  font-size: 14px;
}

.not-available {
  color: var(--theme-text-secondary);
}

.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
