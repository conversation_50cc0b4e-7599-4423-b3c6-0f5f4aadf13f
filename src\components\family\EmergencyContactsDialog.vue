<template>
  <el-dialog
    v-model="dialogVisible"
    title="紧急联系人"
    width="700px"
    :lock-scroll="true"
    :modal="true"
    center
  >
    <div class="emergency-contacts-content">
      <div class="contacts-list">
        <div
          v-for="(contact, index) in emergencyContacts"
          :key="index"
          class="contact-item"
        >
          <el-form :model="contact" inline>
            <el-form-item label="姓名">
              <el-input v-model="contact.name" placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="关系">
              <el-select v-model="contact.relationship" placeholder="请选择关系">
                <el-option label="配偶" value="spouse" />
                <el-option label="父母" value="parent" />
                <el-option label="子女" value="child" />
                <el-option label="兄弟姐妹" value="sibling" />
                <el-option label="朋友" value="friend" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="电话">
              <el-input v-model="contact.phone" placeholder="请输入电话号码" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="removeContact(index)">删除</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <div class="add-contact">
        <el-button type="primary" @click="addContact">添加联系人</el-button>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = ref(false);

const emergencyContacts = reactive([
  {
    name: "李女士",
    relationship: "spouse",
    phone: "139****9999"
  },
  {
    name: "张父",
    relationship: "parent", 
    phone: "136****6666"
  }
]);

watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, (val) => {
  emit("update:modelValue", val);
});

const addContact = () => {
  emergencyContacts.push({
    name: "",
    relationship: "",
    phone: ""
  });
};

const removeContact = (index: number) => {
  emergencyContacts.splice(index, 1);
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSave = () => {
  ElMessage.success("紧急联系人保存成功");
  emit("success");
  handleClose();
};
</script>

<style scoped>
.emergency-contacts-content {
  padding: 20px 0;
}

.contact-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.add-contact {
  text-align: center;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
