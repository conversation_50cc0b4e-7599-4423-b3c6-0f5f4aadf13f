<template>
  <el-dialog
    v-model="visible"
    title="邀请家人加入"
    width="500px"
    :before-close="handleClose"
  >
    <div class="invite-content">
      <!-- 邀请方式选择 -->
      <div class="invite-methods">
        <h4 class="method-title">选择邀请方式</h4>
        <el-radio-group v-model="inviteMethod" class="method-group">
          <el-radio label="link" size="large">
            <div class="method-option">
              <div class="method-icon">
                <el-icon><Link /></el-icon>
              </div>
              <div class="method-info">
                <div class="method-name">邀请链接</div>
                <div class="method-desc">生成邀请链接分享给家人</div>
              </div>
            </div>
          </el-radio>
          <el-radio label="code" size="large">
            <div class="method-option">
              <div class="method-icon">
                <el-icon><Key /></el-icon>
              </div>
              <div class="method-info">
                <div class="method-name">邀请码</div>
                <div class="method-desc">生成6位数字邀请码</div>
              </div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 邀请链接 -->
      <div v-if="inviteMethod === 'link'" class="invite-section">
        <h4 class="section-title">邀请链接</h4>
        <div class="link-container">
          <el-input
            v-model="inviteLink"
            readonly
            placeholder="点击生成邀请链接"
          >
            <template #append>
              <el-button @click="copyLink" :disabled="!inviteLink">
                复制
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="link-actions">
          <el-button type="primary" @click="generateLink" :loading="generating">
            {{ inviteLink ? "重新生成" : "生成链接" }}
          </el-button>
          <el-button v-if="inviteLink" @click="shareLink"> 分享链接 </el-button>
        </div>
        <div class="link-info">
          <p class="info-text">
            <el-icon><InfoFilled /></el-icon>
            链接有效期为7天，过期后需要重新生成
          </p>
        </div>
      </div>

      <!-- 邀请码 -->
      <div v-if="inviteMethod === 'code'" class="invite-section">
        <h4 class="section-title">邀请码</h4>
        <div class="code-container">
          <div v-if="inviteCode" class="invite-code">
            {{ inviteCode }}
          </div>
          <div v-else class="code-placeholder">点击生成邀请码</div>
        </div>
        <div class="code-actions">
          <el-button type="primary" @click="generateCode" :loading="generating">
            {{ inviteCode ? "重新生成" : "生成邀请码" }}
          </el-button>
          <el-button v-if="inviteCode" @click="copyCode">
            复制邀请码
          </el-button>
        </div>
        <div class="code-info">
          <p class="info-text">
            <el-icon><InfoFilled /></el-icon>
            邀请码有效期为24小时，请及时分享给家人
          </p>
        </div>
      </div>

      <!-- 邀请设置 -->
      <div class="invite-settings">
        <h4 class="section-title">邀请设置</h4>
        <el-form label-width="100px">
          <el-form-item label="默认角色">
            <el-select v-model="defaultRole" placeholder="选择新成员的默认角色">
              <el-option label="家庭成员" value="member" />
              <el-option label="子女" value="child" />
              <el-option label="父母" value="parent" />
              <el-option label="其他亲属" value="relative" />
            </el-select>
          </el-form-item>
          <el-form-item label="权限设置">
            <el-checkbox-group v-model="permissions">
              <el-checkbox label="view_health" size="large"
                >查看健康数据</el-checkbox
              >
              <el-checkbox label="manage_reminders" size="large"
                >管理健康提醒</el-checkbox
              >
              <el-checkbox label="emergency_contact" size="large"
                >紧急联系权限</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存设置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { Link, Key, InfoFilled } from "@element-plus/icons-vue";
import { familyService, type InviteData } from "@/services/familyService";

interface Props {
  modelValue: boolean;
  familyId?: number | string;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const inviteMethod = ref("link");
const inviteLink = ref("");
const inviteCode = ref("");
const generating = ref(false);
const defaultRole = ref("member");
const permissions = ref(["view_health"]);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
  if (!val) {
    resetData();
  }
});

const resetData = () => {
  inviteLink.value = "";
  inviteCode.value = "";
  defaultRole.value = "member";
  permissions.value = ["view_health"];
};

const generateLink = async () => {
  if (!props.familyId) {
    ElMessage.error("家庭ID不能为空");
    return;
  }

  generating.value = true;
  try {
    const inviteData: InviteData = {
      familyId: props.familyId,
      method: "link",
      defaultRole: defaultRole.value,
      permissions: permissions.value,
      expiresIn: 24 * 7, // 7天
    };

    const result = await familyService.generateInvite(inviteData);
    inviteLink.value = result.inviteLink!;
    ElMessage.success("邀请链接生成成功！");
  } catch (error) {
    ElMessage.error("生成邀请链接失败");
  } finally {
    generating.value = false;
  }
};

const generateCode = async () => {
  if (!props.familyId) {
    ElMessage.error("家庭ID不能为空");
    return;
  }

  generating.value = true;
  try {
    const inviteData: InviteData = {
      familyId: props.familyId,
      method: "code",
      defaultRole: defaultRole.value,
      permissions: permissions.value,
      expiresIn: 24, // 24小时
    };

    const result = await familyService.generateInvite(inviteData);
    inviteCode.value = result.inviteCode!;
    ElMessage.success("邀请码生成成功！");
  } catch (error) {
    ElMessage.error("生成邀请码失败");
  } finally {
    generating.value = false;
  }
};

const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(inviteLink.value);
    ElMessage.success("邀请链接已复制到剪贴板");
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }
};

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(inviteCode.value);
    ElMessage.success("邀请码已复制到剪贴板");
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }
};

const shareLink = () => {
  if (navigator.share) {
    navigator.share({
      title: "加入我的家庭健康管理",
      text: "邀请您加入我们的家庭健康管理系统",
      url: inviteLink.value,
    });
  } else {
    copyLink();
  }
};

const handleClose = () => {
  visible.value = false;
};

const handleSave = () => {
  ElMessage.success("邀请设置已保存");
  emit("success");
  visible.value = false;
};
</script>

<style scoped>
.invite-content {
  max-height: 600px;
  overflow-y: auto;
}

.method-title,
.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 12px;
}

.method-group {
  width: 100%;
}

.method-group :deep(.el-radio) {
  width: 100%;
  margin-right: 0;
  margin-bottom: 12px;
}

.method-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
  transition: all 0.3s;
}

.method-option:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-light);
  border-radius: 50%;
  color: var(--color-primary);
}

.method-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.method-desc {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.invite-section {
  margin: 24px 0;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.link-container,
.code-container {
  margin-bottom: 16px;
}

.invite-code {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  padding: 20px;
  background: var(--theme-bg-primary);
  border: 2px dashed var(--color-primary);
  border-radius: var(--border-radius-md);
  color: var(--color-primary);
  letter-spacing: 4px;
}

.code-placeholder {
  text-align: center;
  padding: 20px;
  color: var(--theme-text-secondary);
  border: 2px dashed var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.link-actions,
.code-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.info-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--theme-text-secondary);
  margin: 0;
}

.invite-settings {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--theme-border-light);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
