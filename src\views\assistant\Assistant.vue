<template>
  <div class="page-container">
    <!-- 功能导航卡片 -->
    <div class="assistant-navigation">
      <div class="nav-grid">
        <ContentCard
          v-for="item in navigationItems"
          :key="item.id"
          :title="item.title"
          :subtitle="item.subtitle"
          :icon="item.icon"
          :icon-color="item.iconColor"
          :icon-bg-color="item.iconBgColor"
          hoverable
          size="md"
          class="nav-card"
          @click="navigateTo(item.path)"
        >
          <p class="nav-description">{{ item.description }}</p>
          <div class="nav-stats">
            <span class="stat-item">
              <span class="stat-label">{{ item.stats.label }}</span>
              <span class="stat-value">{{ item.stats.value }}</span>
            </span>
          </div>
        </ContentCard>
      </div>
    </div>

    <!-- 快速咨询 -->
    <div class="quick-consultation-section">
      <h2 class="section-title">快速咨询</h2>
      <ContentCard title="常见健康问题" subtitle="点击快速获取专业建议">
        <div class="quick-questions-grid">
          <button
            v-for="question in quickQuestions"
            :key="question.id"
            class="question-btn"
            @click="handleQuickQuestion(question)"
          >
            <span class="question-icon">{{ question.icon }}</span>
            <span class="question-text">{{ question.text }}</span>
          </button>
        </div>
      </ContentCard>
    </div>

    <!-- AI分析概览 -->
    <div class="ai-analysis-section">
      <h2 class="section-title">AI分析概览</h2>
      <div class="analysis-grid">
        <StatCard
          v-for="analysis in aiAnalysis"
          :key="analysis.id"
          :title="analysis.title"
          :value="analysis.value"
          :unit="analysis.unit"
          :status="analysis.status"
          :trend="analysis.trend"
          :icon="analysis.icon"
          :icon-color="analysis.iconColor"
          :icon-bg-color="analysis.iconBgColor"
          size="md"
        />
      </div>
    </div>

    <!-- 最新报告 -->
    <div class="recent-reports-section">
      <h2 class="section-title">最新报告</h2>
      <ContentCard title="AI生成的健康报告" subtitle="基于您的健康数据智能分析">
        <div v-if="recentReports.length === 0" class="empty-state">
          <p>暂无健康报告</p>
          <button class="btn btn-primary" @click="generateReport">
            生成新报告
          </button>
        </div>
        <div v-else class="reports-list">
          <div
            v-for="report in recentReports"
            :key="report.id"
            class="report-item"
          >
            <div class="report-icon" :class="`report-${report.type}`">
              <component :is="getReportIcon(report.type)" />
            </div>
            <div class="report-content">
              <h4 class="report-title">{{ report.title }}</h4>
              <p class="report-summary">{{ report.summary }}</p>
              <div class="report-meta">
                <span class="report-date">{{ formatDate(report.date) }}</span>
                <span class="report-status" :class="`status-${report.status}`">
                  {{ getStatusText(report.status) }}
                </span>
              </div>
            </div>
            <div class="report-actions">
              <button
                class="btn btn-sm btn-outline"
                @click="viewReport(report)"
              >
                查看详情
              </button>
              <button
                class="btn btn-sm btn-secondary"
                @click="downloadReport(report)"
              >
                下载
              </button>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 健康建议 -->
    <div class="health-suggestions-section">
      <h2 class="section-title">个性化建议</h2>
      <div class="suggestions-grid">
        <ContentCard
          v-for="suggestion in healthSuggestions"
          :key="suggestion.id"
          :title="suggestion.title"
          :icon="suggestion.icon"
          :icon-color="suggestion.iconColor"
          :icon-bg-color="suggestion.iconBgColor"
          size="sm"
          class="suggestion-card"
        >
          <p class="suggestion-content">{{ suggestion.content }}</p>
          <div class="suggestion-footer">
            <span
              class="suggestion-priority"
              :class="`priority-${suggestion.priority}`"
            >
              {{ getPriorityText(suggestion.priority) }}
            </span>
            <button
              class="btn btn-xs btn-primary"
              @click="applySuggestion(suggestion)"
            >
              采纳建议
            </button>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

const router = useRouter();

// 导航项目
const navigationItems = ref([
  {
    id: 1,
    title: "智能对话",
    subtitle: "AI健康咨询",
    description: "与AI助手进行自然语言对话，获取个性化健康建议",
    icon: "ChatDotRound",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
    path: "/assistant/smart-chat",
    stats: { label: "对话次数", value: "28" },
  },
  {
    id: 2,
    title: "健康分析",
    subtitle: "数据深度分析",
    description: "基于健康数据进行深度分析，识别健康趋势和风险",
    icon: "TrendCharts",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
    path: "/assistant/health-analysis",
    stats: { label: "分析报告", value: "12" },
  },
  {
    id: 3,
    title: "健康报告",
    subtitle: "AI生成报告",
    description: "自动生成详细的健康分析报告和改善建议",
    icon: "Document",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
    path: "/assistant/health-report",
    stats: { label: "报告数量", value: "8" },
  },
  {
    id: 4,
    title: "健康建议",
    subtitle: "个性化建议",
    description: "根据个人健康状况提供定制化的健康改善建议",
    icon: "Lightbulb",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
    path: "/assistant/health-suggestions",
    stats: { label: "建议条数", value: "15" },
  },
  {
    id: 5,
    title: "健康预测",
    subtitle: "趋势预测",
    description: "基于历史数据预测健康趋势和潜在风险",
    icon: "Crystal",
    iconColor: "#EF4444",
    iconBgColor: "#FEE2E2",
    path: "/assistant/health-prediction",
    stats: { label: "预测模型", value: "5" },
  },
]);

// 快速咨询问题
const quickQuestions = ref([
  { id: 1, icon: "🩺", text: "血压偏高怎么办？" },
  { id: 2, icon: "💊", text: "忘记吃药了怎么办？" },
  { id: 3, icon: "🏃", text: "适合我的运动方案" },
  { id: 4, icon: "🥗", text: "健康饮食建议" },
  { id: 5, icon: "😴", text: "改善睡眠质量" },
  { id: 6, icon: "🧘", text: "压力管理方法" },
]);

// AI分析数据
const aiAnalysis = ref([
  {
    id: 1,
    title: "健康评分",
    value: "85",
    unit: "分",
    status: "normal",
    trend: { direction: "up", value: 5, period: "本月" },
    icon: "Star",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
  },
  {
    id: 2,
    title: "风险评估",
    value: "低",
    unit: "风险",
    status: "success",
    trend: { direction: "down", value: 10, period: "本月" },
    icon: "Shield",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
  },
  {
    id: 3,
    title: "改善建议",
    value: "3",
    unit: "条",
    status: "normal",
    trend: { direction: "stable", value: 0, period: "本周" },
    icon: "Lightbulb",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
  },
  {
    id: 4,
    title: "预测准确率",
    value: "92",
    unit: "%",
    status: "success",
    trend: { direction: "up", value: 2, period: "本月" },
    icon: "Target",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
  },
]);

// 最新报告
const recentReports = ref([
  {
    id: 1,
    type: "comprehensive",
    title: "综合健康分析报告",
    summary: "基于最近30天的健康数据，整体健康状况良好，建议继续保持...",
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    status: "completed",
  },
  {
    id: 2,
    type: "risk",
    title: "心血管风险评估",
    summary: "心血管健康指标正常，建议适量运动和均衡饮食...",
    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    status: "completed",
  },
  {
    id: 3,
    type: "nutrition",
    title: "营养状况分析",
    summary: "营养摄入基本均衡，建议增加维生素D的摄入...",
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    status: "completed",
  },
]);

// 健康建议
const healthSuggestions = ref([
  {
    id: 1,
    title: "运动建议",
    content: "建议每周进行3-4次中等强度有氧运动，每次30分钟",
    priority: "high",
    icon: "Trophy",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
  },
  {
    id: 2,
    title: "饮食调整",
    content: "减少钠盐摄入，增加富含钾的食物，如香蕉、菠菜等",
    priority: "medium",
    icon: "Apple",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
  },
  {
    id: 3,
    title: "睡眠优化",
    content: "保持规律作息，建议每晚11点前入睡，睡眠时间7-8小时",
    priority: "medium",
    icon: "Moon",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
  },
  {
    id: 4,
    title: "定期检查",
    content: "建议3个月后复查血压和血脂，监测健康状况变化",
    priority: "low",
    icon: "Calendar",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
  },
]);

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path);
};

// 处理快速问题
const handleQuickQuestion = (question: any) => {
  router.push(
    `/assistant/smart-chat?question=${encodeURIComponent(question.text)}`
  );
};

// 获取报告图标
const getReportIcon = (type: string) => {
  switch (type) {
    case "comprehensive":
      return "Document";
    case "risk":
      return "Warning";
    case "nutrition":
      return "Apple";
    default:
      return "Document";
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "completed":
      return "已完成";
    case "processing":
      return "生成中";
    case "failed":
      return "失败";
    default:
      return "未知";
  }
};

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case "high":
      return "高优先级";
    case "medium":
      return "中优先级";
    case "low":
      return "低优先级";
    default:
      return "普通";
  }
};

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 查看报告
const viewReport = (report: any) => {
  router.push(`/assistant/health-report?id=${report.id}`);
};

// 下载报告
const downloadReport = (report: any) => {
  console.log("下载报告:", report.title);
};

// 生成报告
const generateReport = () => {
  console.log("生成新报告");
};

// 采纳建议
const applySuggestion = (suggestion: any) => {
  console.log("采纳建议:", suggestion.title);
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}

.assistant-navigation {
  margin-bottom: var(--spacing-8);
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.nav-card {
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.nav-card:hover {
  transform: translateY(-2px);
}

.nav-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

.nav-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.quick-consultation-section {
  margin-bottom: var(--spacing-8);
}

.quick-questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.question-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
}

.question-btn:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.question-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.question-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.ai-analysis-section {
  margin-bottom: var(--spacing-8);
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.recent-reports-section {
  margin-bottom: var(--spacing-8);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.report-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.report-item:hover {
  background-color: var(--theme-bg-secondary);
}

.report-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.report-comprehensive {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

.report-risk {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.report-nutrition {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.report-content {
  flex: 1;
}

.report-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.report-summary {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-normal);
}

.report-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.report-date {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.report-status {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.status-completed {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-processing {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.status-failed {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.report-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.health-suggestions-section {
  margin-bottom: var(--spacing-8);
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
}

.suggestion-card {
  height: 100%;
}

.suggestion-content {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

.suggestion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.suggestion-priority {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.priority-high {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.priority-medium {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.priority-low {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .quick-questions-grid {
    grid-template-columns: 1fr;
  }

  .analysis-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .report-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .report-actions {
    align-self: flex-end;
  }
}
</style>
