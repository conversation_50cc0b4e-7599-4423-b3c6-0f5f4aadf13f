<template>
  <div class="page-container">
    <!-- 活动统计概览 -->
    <div class="activity-stats-section">
      <div class="stats-grid">
        <StatCard
          title="总活动数"
          :value="activityStats.total"
          unit="个"
          icon="Trophy"
          icon-color="#3B82F6"
          icon-bg-color="#DBEAFE"
          status="normal"
        />
        <StatCard
          title="已完成"
          :value="activityStats.completed"
          unit="个"
          icon="CircleCheck"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          status="success"
          :trend="{ direction: 'up', value: 12, period: '本周' }"
        />
        <StatCard
          title="待完成"
          :value="activityStats.pending"
          unit="个"
          icon="Clock"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          status="warning"
        />
        <StatCard
          title="即将到期"
          :value="activityStats.upcoming"
          unit="个"
          icon="Warning"
          icon-color="#EF4444"
          icon-bg-color="#FEE2E2"
          status="danger"
        />
      </div>
    </div>

    <!-- 功能导航 -->
    <div class="activity-navigation">
      <div class="nav-grid">
        <ContentCard
          title="活动列表"
          subtitle="查看所有活动"
          icon="List"
          icon-color="#3B82F6"
          icon-bg-color="#DBEAFE"
          hoverable
          size="sm"
          @click="navigateTo('/activities/activity-list')"
        >
          <p class="nav-description">管理和查看所有健康活动记录</p>
        </ContentCard>

        <ContentCard
          title="活动时间线"
          subtitle="时间轴视图"
          icon="Timeline"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          hoverable
          size="sm"
          @click="navigateTo('/activities/activity-timeline')"
        >
          <p class="nav-description">以时间轴形式查看活动历史</p>
        </ContentCard>

        <ContentCard
          title="数据分析"
          subtitle="活动统计分析"
          icon="TrendCharts"
          icon-color="#8B5CF6"
          icon-bg-color="#EDE9FE"
          hoverable
          size="sm"
          @click="navigateTo('/activities/activity-analysis')"
        >
          <p class="nav-description">查看活动完成率和参与度分析</p>
        </ContentCard>

        <ContentCard
          title="添加活动"
          subtitle="创建新活动"
          icon="Plus"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          hoverable
          size="sm"
          @click="showAddActivityDialog = true"
        >
          <p class="nav-description">创建新的健康活动记录</p>
        </ContentCard>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities-section">
      <h2 class="section-title">最近活动</h2>
      <ContentCard title="活动记录" subtitle="最近7天的活动">
        <div v-if="recentActivities.length === 0" class="empty-state">
          <p>暂无最近活动记录</p>
        </div>
        <div v-else class="activities-list">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
            :class="{ completed: activity.completed }"
          >
            <div class="activity-icon" :class="`activity-${activity.type}`">
              <component :is="getActivityIcon(activity.type)" />
            </div>
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <div class="activity-meta">
                <span class="activity-member">{{ activity.member }}</span>
                <span class="activity-time">{{
                  activity.time || "未设置时间"
                }}</span>
                <span
                  class="activity-status"
                  :class="`status-${activity.status}`"
                >
                  {{ getStatusText(activity.status) }}
                </span>
              </div>
            </div>
            <div class="activity-actions">
              <button
                v-if="!activity.completed"
                class="btn btn-sm btn-success"
                @click="markCompleted(activity)"
              >
                标记完成
              </button>
              <button
                class="btn btn-sm btn-outline"
                @click="editActivity(activity)"
              >
                编辑
              </button>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 模拟数据
const healthStats = {
  activities: {
    total: 156,
    completed: 89,
    pending: 45,
    upcoming: 22,
  },
};

const healthActivities = [
  {
    id: 1,
    title: "晨跑",
    description: "30分钟慢跑",
    type: "exercise",
    member: "张先生",
    date: new Date(),
    time: "06:30",
    completed: true,
    status: "completed",
  },
  {
    id: 2,
    title: "服用维生素",
    description: "每日维生素补充",
    type: "medication",
    member: "李女士",
    date: new Date(),
    time: "08:00",
    completed: false,
    status: "pending",
  },
];

const getActivityTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    exercise: "运动",
    medication: "用药",
    checkup: "检查",
    diet: "饮食",
  };
  return labels[type] || type;
};

const getActivityStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    completed: "已完成",
    pending: "待完成",
    overdue: "已过期",
  };
  return labels[status] || status;
};

const router = useRouter();

// 对话框状态
const showAddActivityDialog = ref(false);

// 活动统计
const activityStats = ref(healthStats.activities);

// 最近活动
const recentActivities = ref(healthActivities);

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path);
};

// 获取活动图标
const getActivityIcon = (type: string) => {
  switch (type) {
    case "exercise":
      return "Trophy";
    case "checkup":
      return "Monitor";
    case "medication":
      return "Pills";
    case "diet":
      return "Apple";
    default:
      return "Document";
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "completed":
      return "已完成";
    case "pending":
      return "待完成";
    case "overdue":
      return "已逾期";
    default:
      return "未知";
  }
};

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));

  if (hours < 1) {
    return "刚刚";
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return time.toLocaleDateString();
  }
};

// 标记完成
const markCompleted = (activity: any) => {
  activity.completed = true;
  activity.status = "completed";
};

// 编辑活动
const editActivity = (activity: any) => {
  console.log("编辑活动:", activity.title);
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.activities-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

.activity-stats-section {
  margin-bottom: var(--spacing-8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.activity-navigation {
  margin-bottom: var(--spacing-8);
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.nav-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.recent-activities-section {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.activity-item:hover {
  background-color: var(--theme-bg-secondary);
}

.activity-item.completed {
  opacity: 0.8;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-exercise {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.activity-checkup {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

.activity-medication {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.activity-diet {
  background-color: var(--color-secondary);
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-2) 0;
}

.activity-meta {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.activity-member,
.activity-time,
.activity-status {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.activity-member {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.activity-time {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.status-completed {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-pending {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.status-overdue {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.activity-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .activity-actions {
    align-self: flex-end;
  }
}
</style>
