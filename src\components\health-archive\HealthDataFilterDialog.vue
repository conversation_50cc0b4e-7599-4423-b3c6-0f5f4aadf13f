<template>
  <el-dialog
    v-model="visible"
    title="健康数据筛选"
    width="900px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="filter-content">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <h4 class="section-title">筛选条件</h4>
        <el-form :model="filterForm" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据类型">
                <el-select
                  v-model="filterForm.types"
                  multiple
                  placeholder="选择数据类型"
                  style="width: 100%"
                >
                  <el-option label="体检记录" value="checkup" />
                  <el-option label="用药记录" value="medication" />
                  <el-option label="运动数据" value="exercise" />
                  <el-option label="饮食记录" value="diet" />
                  <el-option label="睡眠数据" value="sleep" />
                  <el-option label="生命体征" value="vital_signs" />
                  <el-option label="检验结果" value="lab_result" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="家庭成员">
                <el-select
                  v-model="filterForm.memberId"
                  placeholder="选择成员"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="全部成员" value="" />
                  <el-option
                    v-for="member in familyMembers"
                    :key="member.id"
                    :label="member.name"
                    :value="member.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="filterForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据状态">
                <el-select
                  v-model="filterForm.status"
                  multiple
                  placeholder="选择状态"
                  style="width: 100%"
                >
                  <el-option label="正常" value="normal" />
                  <el-option label="需要关注" value="attention" />
                  <el-option label="异常" value="abnormal" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据来源">
                <el-select
                  v-model="filterForm.sources"
                  multiple
                  placeholder="选择数据来源"
                  style="width: 100%"
                >
                  <el-option label="智能设备" value="device" />
                  <el-option label="医院系统" value="hospital" />
                  <el-option label="手动录入" value="manual" />
                  <el-option label="第三方应用" value="app" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示数量">
                <el-select v-model="filterForm.limit" style="width: 100%">
                  <el-option label="50条" :value="50" />
                  <el-option label="100条" :value="100" />
                  <el-option label="200条" :value="200" />
                  <el-option label="500条" :value="500" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="applyFilter" :loading="loading">
              应用筛选
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
            <el-button @click="saveFilter">保存筛选条件</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据统计 -->
      <div v-if="dataResult" class="statistics-section">
        <h4 class="section-title">数据统计</h4>
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ dataResult.total }}</div>
            <div class="stat-label">总记录数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">
              {{ Object.keys(dataResult.aggregations?.byType || {}).length }}
            </div>
            <div class="stat-label">数据类型</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ getDateRangeDays() }}</div>
            <div class="stat-label">时间跨度(天)</div>
          </div>
        </div>

        <!-- 类型分布 -->
        <div class="aggregation-charts">
          <div class="chart-container">
            <h5 class="chart-title">类型分布</h5>
            <div class="chart-content">
              <div
                v-for="(count, type) in dataResult.aggregations?.byType"
                :key="type"
                class="chart-item"
              >
                <div class="chart-label">{{ getTypeName(type) }}</div>
                <div class="chart-bar">
                  <div
                    class="chart-fill"
                    :style="{ width: `${(count / dataResult.total) * 100}%` }"
                  ></div>
                </div>
                <div class="chart-value">{{ count }}</div>
              </div>
            </div>
          </div>

          <div class="chart-container">
            <h5 class="chart-title">状态分布</h5>
            <div class="chart-content">
              <div
                v-for="(count, status) in dataResult.aggregations?.byStatus"
                :key="status"
                class="chart-item"
              >
                <div class="chart-label">{{ getStatusName(status) }}</div>
                <div class="chart-bar">
                  <div
                    class="chart-fill"
                    :class="`status-${status}`"
                    :style="{ width: `${(count / dataResult.total) * 100}%` }"
                  ></div>
                </div>
                <div class="chart-value">{{ count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据列表 -->
      <div v-if="dataResult" class="data-list-section">
        <div class="list-header">
          <h4 class="section-title">数据列表</h4>
          <div class="list-actions">
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>

        <el-table
          :data="dataResult.data"
          style="width: 100%"
          v-loading="loading"
          max-height="400"
        >
          <el-table-column prop="timestamp" label="时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.timestamp) }}
            </template>
          </el-table-column>

          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(row.type)" size="small">
                {{ getTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="title" label="标题" min-width="200" />

          <el-table-column prop="source" label="来源" width="120" />

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 加载更多 -->
        <div v-if="dataResult.hasMore" class="load-more">
          <el-button @click="loadMore" :loading="loadingMore">
            加载更多
          </el-button>
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-if="!dataResult && !loading" class="no-data">
        <el-empty description="请设置筛选条件并点击应用筛选查看数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import { Download, Refresh } from "@element-plus/icons-vue";
import {
  healthArchiveService,
  type HealthDataFilter,
  type HealthDataResult,
} from "@/services/healthArchiveService";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const loadingMore = ref(false);

// 筛选表单
const filterForm = reactive<HealthDataFilter>({
  types: [],
  memberId: "",
  dateRange: null,
  status: [],
  sources: [],
  limit: 100,
  offset: 0,
});

// 数据结果
const dataResult = ref<HealthDataResult | null>(null);

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
  if (!val) {
    resetState();
  }
});

const resetState = () => {
  dataResult.value = null;
  filterForm.offset = 0;
};

const applyFilter = async () => {
  loading.value = true;
  try {
    filterForm.offset = 0;
    const result = await healthArchiveService.getSpecificHealthData(filterForm);
    dataResult.value = result;
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const resetFilter = () => {
  Object.assign(filterForm, {
    types: [],
    memberId: "",
    dateRange: null,
    status: [],
    sources: [],
    limit: 100,
    offset: 0,
  });
  dataResult.value = null;
};

const saveFilter = () => {
  // 保存筛选条件到本地存储
  localStorage.setItem("healthDataFilter", JSON.stringify(filterForm));
  ElMessage.success("筛选条件已保存");
};

const loadMore = async () => {
  if (!dataResult.value) return;

  loadingMore.value = true;
  try {
    filterForm.offset = dataResult.value.data.length;
    const result = await healthArchiveService.getSpecificHealthData(filterForm);

    // 合并数据
    dataResult.value.data.push(...result.data);
    dataResult.value.hasMore = result.hasMore;
  } catch (error) {
    console.error("加载更多失败:", error);
    ElMessage.error("加载更多失败");
  } finally {
    loadingMore.value = false;
  }
};

const refreshData = () => {
  if (dataResult.value) {
    applyFilter();
  }
};

const exportData = () => {
  if (!dataResult.value) {
    ElMessage.warning("没有可导出的数据");
    return;
  }

  ElMessage.success("数据导出功能开发中...");
};

const viewDetail = (item: any) => {
  ElMessage.info(`查看 ${item.title} 的详细信息`);
};

const getDateRangeDays = () => {
  if (!filterForm.dateRange) return 0;
  const [start, end] = filterForm.dateRange;
  return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
};

const getTypeName = (type: string) => {
  const names: Record<string, string> = {
    checkup: "体检",
    medication: "用药",
    exercise: "运动",
    diet: "饮食",
    sleep: "睡眠",
    vital_signs: "生命体征",
    lab_result: "检验",
  };
  return names[type] || type;
};

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    checkup: "primary",
    medication: "success",
    exercise: "warning",
    diet: "info",
    sleep: "",
    vital_signs: "primary",
    lab_result: "success",
  };
  return colors[type] || "";
};

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    normal: "正常",
    attention: "关注",
    abnormal: "异常",
  };
  return names[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    normal: "success",
    attention: "warning",
    abnormal: "danger",
  };
  return colors[status] || "";
};

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.filter-content {
  max-height: 700px;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.statistics-section {
  margin-bottom: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.aggregation-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-container {
  padding: 16px;
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-md);
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 12px;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chart-item {
  display: grid;
  grid-template-columns: 80px 1fr 40px;
  align-items: center;
  gap: 8px;
}

.chart-label {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.chart-bar {
  height: 8px;
  background: var(--theme-bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s;
}

.chart-fill.status-normal {
  background: var(--color-success);
}

.chart-fill.status-attention {
  background: var(--color-warning);
}

.chart-fill.status-abnormal {
  background: var(--color-danger);
}

.chart-value {
  font-size: 12px;
  font-weight: 500;
  color: var(--theme-text-primary);
  text-align: right;
}

.data-list-section {
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-actions {
  display: flex;
  gap: 8px;
}

.load-more {
  text-align: center;
  margin-top: 16px;
}

.no-data {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
