<template>
  <div class="content-card" :class="cardClasses">
    <!-- 卡片头部 -->
    <div v-if="showHeader" class="card-header">
      <div class="card-header-content">
        <!-- 图标和标题 -->
        <div class="card-title-section">
          <div v-if="icon" class="card-icon" :style="iconStyle">
            <component :is="icon" />
          </div>
          <div class="card-title-wrapper">
            <h3 v-if="title" class="card-title">{{ title }}</h3>
            <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
          </div>
        </div>
        
        <!-- 头部操作 -->
        <div v-if="$slots.actions" class="card-actions">
          <slot name="actions" />
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-body" :class="{ 'no-header': !showHeader }">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  title?: string;
  subtitle?: string;
  icon?: any;
  iconColor?: string;
  iconBgColor?: string;
  variant?: 'default' | 'bordered' | 'shadow' | 'flat';
  size?: 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  loading?: boolean;
  showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  hoverable: false,
  loading: false,
  showHeader: true
});

// 计算卡片样式类
const cardClasses = computed(() => {
  return [
    `card-${props.variant}`,
    `card-${props.size}`,
    {
      'card-hoverable': props.hoverable,
      'card-loading': props.loading
    }
  ];
});

// 计算图标样式
const iconStyle = computed(() => {
  const style: Record<string, string> = {};
  
  if (props.iconColor) {
    style.color = props.iconColor;
  }
  
  if (props.iconBgColor) {
    style.backgroundColor = props.iconBgColor;
  }
  
  return style;
});
</script>

<style scoped>
.content-card {
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* 卡片变体 */
.card-default {
  border: 1px solid var(--theme-border);
  box-shadow: var(--shadow-sm);
}

.card-bordered {
  border: 2px solid var(--theme-border);
}

.card-shadow {
  border: 1px solid var(--theme-border);
  box-shadow: var(--shadow-lg);
}

.card-flat {
  border: none;
  box-shadow: none;
}

/* 卡片尺寸 */
.card-sm .card-header {
  padding: var(--spacing-4);
}

.card-sm .card-body {
  padding: var(--spacing-4);
}

.card-sm .card-footer {
  padding: var(--spacing-4);
}

.card-md .card-header {
  padding: var(--spacing-6);
}

.card-md .card-body {
  padding: var(--spacing-6);
}

.card-md .card-footer {
  padding: var(--spacing-6);
}

.card-lg .card-header {
  padding: var(--spacing-8);
}

.card-lg .card-body {
  padding: var(--spacing-8);
}

.card-lg .card-footer {
  padding: var(--spacing-8);
}

/* 悬停效果 */
.card-hoverable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 加载状态 */
.card-loading {
  position: relative;
  overflow: hidden;
}

.card-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
  z-index: 1;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 卡片头部 */
.card-header {
  background-color: var(--theme-bg-secondary);
  border-bottom: 1px solid var(--theme-border);
}

.card-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.card-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
  min-width: 0;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
  flex-shrink: 0;
}

.card-title-wrapper {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: var(--spacing-1) 0 0 0;
  line-height: var(--line-height-normal);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

/* 卡片内容 */
.card-body {
  position: relative;
}

.card-body.no-header {
  border-top: none;
}

/* 卡片底部 */
.card-footer {
  background-color: var(--theme-bg-secondary);
  border-top: 1px solid var(--theme-border);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-3);
  }
  
  .card-title-section {
    flex-direction: row;
    align-items: center;
  }
  
  .card-actions {
    justify-content: flex-end;
  }
  
  .card-sm .card-header,
  .card-sm .card-body,
  .card-sm .card-footer {
    padding: var(--spacing-3);
  }
  
  .card-md .card-header,
  .card-md .card-body,
  .card-md .card-footer {
    padding: var(--spacing-4);
  }
  
  .card-lg .card-header,
  .card-lg .card-body,
  .card-lg .card-footer {
    padding: var(--spacing-6);
  }
}

@media (max-width: 480px) {
  .card-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .card-icon {
    width: 32px;
    height: 32px;
  }
  
  .card-title {
    font-size: var(--font-size-base);
  }
  
  .card-subtitle {
    font-size: var(--font-size-xs);
  }
}
</style>
