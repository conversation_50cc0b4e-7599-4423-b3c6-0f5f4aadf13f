// vite.config.ts
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { resolve } from "path";

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [
      vue(),
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: false,
          }),
        ],
        imports: ["vue", "vue-router"],
        dts: true,
      }),
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: false,
          }),
        ],
        dirs: ["src/components"],
        extensions: ["vue"],
        deep: true,
        dts: true,
      }),
    ],
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
      },
    },
    server: {
      port: 5174,
      open: true,
      cors: true,
    },
    build: {
      target: "es2015",
      outDir: "dist",
      assetsDir: "assets",
      sourcemap: env.VITE_ENABLE_SOURCEMAP === "true",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["vue", "vue-router"],
            element: ["element-plus"],
            echarts: ["echarts", "vue-echarts"],
          },
        },
      },
      chunkSizeWarningLimit: 1000,
    },
    optimizeDeps: {
      include: ["vue", "vue-router", "element-plus", "echarts"],
    },
  };
});
