import type { Router } from 'vue-router';

// 模拟认证状态检查
function isAuthenticated(): boolean {
  // 这里应该检查实际的认证状态
  // 例如检查 localStorage 中的 token 或 store 中的用户状态
  return localStorage.getItem('auth_token') !== null;
}

// 设置路由守卫
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = to.meta.title as string;
    }

    // 检查认证状态
    const authenticated = isAuthenticated();

    // 需要认证的路由
    if (to.meta?.requiresAuth && !authenticated) {
      next('/login');
      return;
    }

    // 游客路由（已登录用户不能访问）
    if (to.meta?.requiresGuest && authenticated) {
      next('/dashboard');
      return;
    }

    // 检查权限
    if (to.meta?.roles && Array.isArray(to.meta.roles)) {
      // 这里应该检查用户角色
      // const userRoles = getUserRoles();
      // const hasPermission = to.meta.roles.some(role => userRoles.includes(role));
      // if (!hasPermission) {
      //   next('/403');
      //   return;
      // }
    }

    next();
  });

  // 全局后置钩子
  router.afterEach((to, from) => {
    // 页面加载完成后的处理
    // 例如：记录页面访问日志、埋点统计等
    console.log(`导航到: ${to.path}`);
    
    // 滚动到页面顶部
    window.scrollTo(0, 0);
  });

  // 全局解析守卫
  router.beforeResolve((to, from, next) => {
    // 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后调用
    next();
  });
}

// 导航失败处理
export function handleNavigationError(error: any) {
  console.error('导航错误:', error);
  
  // 根据错误类型进行不同处理
  if (error.name === 'NavigationDuplicated') {
    // 重复导航错误，通常可以忽略
    return;
  }
  
  if (error.name === 'NavigationAborted') {
    // 导航被中止
    console.warn('导航被中止');
    return;
  }
  
  if (error.name === 'NavigationCancelled') {
    // 导航被取消
    console.warn('导航被取消');
    return;
  }
  
  // 其他错误，可能需要显示错误页面或提示
  console.error('未知导航错误:', error);
}

// 动态路由添加
export function addDynamicRoutes(router: Router, routes: any[]) {
  routes.forEach(route => {
    router.addRoute(route);
  });
}

// 路由权限检查
export function checkRoutePermission(route: any, userPermissions: string[]): boolean {
  if (!route.meta?.permissions) {
    return true;
  }
  
  const requiredPermissions = route.meta.permissions as string[];
  return requiredPermissions.some(permission => userPermissions.includes(permission));
}

// 生成面包屑
export function generateBreadcrumb(route: any): Array<{ title: string; path?: string }> {
  if (route.meta?.breadcrumb) {
    return route.meta.breadcrumb;
  }
  
  // 自动生成面包屑
  const breadcrumb = [];
  const pathSegments = route.path.split('/').filter(Boolean);
  
  let currentPath = '';
  for (const segment of pathSegments) {
    currentPath += `/${segment}`;
    breadcrumb.push({
      title: segment.charAt(0).toUpperCase() + segment.slice(1),
      path: currentPath
    });
  }
  
  return breadcrumb;
}

// 路由缓存管理
export class RouteCache {
  private cache = new Map<string, any>();
  
  set(key: string, value: any) {
    this.cache.set(key, value);
  }
  
  get(key: string) {
    return this.cache.get(key);
  }
  
  has(key: string) {
    return this.cache.has(key);
  }
  
  delete(key: string) {
    return this.cache.delete(key);
  }
  
  clear() {
    this.cache.clear();
  }
  
  // 根据路由规则清理缓存
  clearByPattern(pattern: RegExp) {
    for (const [key] of this.cache) {
      if (pattern.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建全局路由缓存实例
export const routeCache = new RouteCache();
