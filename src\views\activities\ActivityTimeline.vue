<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 时间线筛选 -->
      <div class="timeline-filters">
        <ContentCard size="sm">
          <div class="filters-content">
            <div class="date-range">
              <label class="filter-label">时间范围:</label>
              <select v-model="selectedTimeRange" class="filter-select">
                <option value="7">最近7天</option>
                <option value="30">最近30天</option>
                <option value="90">最近3个月</option>
                <option value="365">最近1年</option>
              </select>
            </div>

            <div class="member-filter">
              <label class="filter-label">家庭成员:</label>
              <select v-model="selectedMember" class="filter-select">
                <option value="">全部成员</option>
                <option
                  v-for="member in familyMembers"
                  :key="member.id"
                  :value="member.id"
                >
                  {{ member.name }}
                </option>
              </select>
            </div>

            <div class="type-filter">
              <label class="filter-label">活动类型:</label>
              <select v-model="selectedType" class="filter-select">
                <option value="">全部类型</option>
                <option value="medication">用药管理</option>
                <option value="exercise">运动锻炼</option>
                <option value="checkup">健康检查</option>
                <option value="appointment">医疗预约</option>
              </select>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 时间线内容 -->
      <div class="timeline-content">
        <ContentCard title="活动时间线">
          <div class="timeline-container">
            <div class="timeline">
              <div
                v-for="(group, date) in groupedActivities"
                :key="date"
                class="timeline-group"
              >
                <!-- 日期标题 -->
                <div class="date-header">
                  <div class="date-line"></div>
                  <div class="date-badge">
                    <span class="date-text">{{ formatDateHeader(date) }}</span>
                  </div>
                  <div class="date-line"></div>
                </div>

                <!-- 该日期的活动列表 -->
                <div class="activities-list">
                  <div
                    v-for="activity in group"
                    :key="activity.id"
                    class="timeline-item"
                    :class="{ completed: activity.status === 'completed' }"
                  >
                    <div class="timeline-marker">
                      <div
                        class="marker-dot"
                        :class="`marker-${activity.type}`"
                      >
                        <span class="marker-icon">{{
                          getTypeIcon(activity.type)
                        }}</span>
                      </div>
                      <div class="marker-line"></div>
                    </div>

                    <div class="timeline-content-item">
                      <div class="activity-card">
                        <div class="activity-header">
                          <div class="activity-info">
                            <h4 class="activity-title">{{ activity.title }}</h4>
                            <p class="activity-description">
                              {{ activity.description }}
                            </p>
                          </div>
                          <div class="activity-meta">
                            <span class="activity-time">{{
                              activity.time
                            }}</span>
                            <span class="activity-member">{{
                              getMemberName(activity.memberId)
                            }}</span>
                          </div>
                        </div>

                        <div class="activity-details">
                          <div class="activity-badges">
                            <span
                              class="type-badge"
                              :class="`type-${activity.type}`"
                            >
                              {{ getTypeLabel(activity.type) }}
                            </span>
                            <span
                              class="status-badge"
                              :class="`status-${activity.status}`"
                            >
                              {{ getStatusLabel(activity.status) }}
                            </span>
                            <span
                              v-if="activity.priority === 'high'"
                              class="priority-badge"
                            >
                              高优先级
                            </span>
                          </div>

                          <div v-if="activity.notes" class="activity-notes">
                            <h5>备注:</h5>
                            <p>{{ activity.notes }}</p>
                          </div>

                          <div
                            v-if="activity.tags.length > 0"
                            class="activity-tags"
                          >
                            <span
                              v-for="tag in activity.tags"
                              :key="tag"
                              class="activity-tag"
                            >
                              {{ tag }}
                            </span>
                          </div>
                        </div>

                        <div
                          v-if="activity.status !== 'completed'"
                          class="activity-actions"
                        >
                          <button
                            class="btn btn-sm btn-success"
                            @click="markAsCompleted(activity)"
                          >
                            标记完成
                          </button>
                          <button
                            class="btn btn-sm btn-outline"
                            @click="editActivity(activity)"
                          >
                            编辑
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div
                v-if="Object.keys(groupedActivities).length === 0"
                class="empty-timeline"
              >
                <div class="empty-icon">📅</div>
                <h3>暂无活动记录</h3>
                <p>在选定的时间范围内没有找到活动记录</p>
                <button class="btn btn-primary" @click="goToAddActivity">
                  添加活动
                </button>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 统计信息 -->
      <div class="timeline-stats">
        <ContentCard title="时间线统计" size="sm">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">总活动数</span>
              <span class="stat-value">{{ timelineStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成</span>
              <span class="stat-value">{{ timelineStats.completed }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">完成率</span>
              <span class="stat-value"
                >{{ timelineStats.completionRate }}%</span
              >
            </div>
            <div class="stat-item">
              <span class="stat-label">平均每天</span>
              <span class="stat-value">{{ timelineStats.averagePerDay }}</span>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

const router = useRouter();

// 筛选条件
const selectedTimeRange = ref("30");
const selectedMember = ref("");
const selectedType = ref("");

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 活动数据
const activities = ref([
  {
    id: 1,
    title: "晨间血压测量",
    description: "每日早晨起床后测量血压并记录",
    type: "checkup",
    memberId: 1,
    date: new Date(),
    time: "07:00",
    status: "completed",
    priority: "high",
    notes: "血压正常，继续保持",
    tags: ["血压", "日常监测"],
  },
  {
    id: 2,
    title: "服用降压药",
    description: "按医嘱服用降压药物",
    type: "medication",
    memberId: 1,
    date: new Date(),
    time: "08:00",
    status: "completed",
    priority: "high",
    notes: "",
    tags: ["用药", "高血压"],
  },
  {
    id: 3,
    title: "晚间散步",
    description: "饭后1小时进行30分钟散步",
    type: "exercise",
    memberId: 2,
    date: new Date(),
    time: "19:30",
    status: "completed",
    priority: "medium",
    notes: "今天走了3000步，感觉很好",
    tags: ["运动", "散步"],
  },
  {
    id: 4,
    title: "血糖检测",
    description: "餐后2小时血糖检测",
    type: "checkup",
    memberId: 2,
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    time: "14:00",
    status: "completed",
    priority: "medium",
    notes: "血糖值正常",
    tags: ["血糖", "餐后检测"],
  },
  {
    id: 5,
    title: "心脏科复诊",
    description: "定期心脏科检查和药物调整",
    type: "appointment",
    memberId: 1,
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    time: "14:00",
    status: "completed",
    priority: "high",
    notes: "医生建议继续当前治疗方案",
    tags: ["复诊", "心脏科"],
  },
]);

// 筛选后的活动
const filteredActivities = computed(() => {
  const now = new Date();
  const timeRangeMs = parseInt(selectedTimeRange.value) * 24 * 60 * 60 * 1000;
  const startDate = new Date(now.getTime() - timeRangeMs);

  return activities.value.filter((activity) => {
    // 时间范围筛选
    if (activity.date < startDate) return false;

    // 成员筛选
    if (
      selectedMember.value &&
      activity.memberId !== parseInt(selectedMember.value)
    ) {
      return false;
    }

    // 类型筛选
    if (selectedType.value && activity.type !== selectedType.value) {
      return false;
    }

    return true;
  });
});

// 按日期分组的活动
const groupedActivities = computed(() => {
  const groups = {};

  filteredActivities.value.forEach((activity) => {
    const dateKey = activity.date.toDateString();
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(activity);
  });

  // 按时间排序每组内的活动
  Object.keys(groups).forEach((date) => {
    groups[date].sort((a, b) => a.time.localeCompare(b.time));
  });

  // 按日期倒序排列
  const sortedGroups = {};
  Object.keys(groups)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .forEach((date) => {
      sortedGroups[date] = groups[date];
    });

  return sortedGroups;
});

// 时间线统计
const timelineStats = computed(() => {
  const total = filteredActivities.value.length;
  const completed = filteredActivities.value.filter(
    (a) => a.status === "completed"
  ).length;
  const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
  const days = parseInt(selectedTimeRange.value);
  const averagePerDay = total > 0 ? (total / days).toFixed(1) : "0";

  return { total, completed, completionRate, averagePerDay };
});

// 获取成员名称
const getMemberName = (memberId: number) => {
  const member = familyMembers.value.find((m) => m.id === memberId);
  return member ? member.name : "未知";
};

// 获取类型图标
const getTypeIcon = (type: string) => {
  const icons = {
    medication: "💊",
    exercise: "🏃",
    checkup: "🩺",
    appointment: "🏥",
  };
  return icons[type] || "📋";
};

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labels = {
    medication: "用药管理",
    exercise: "运动锻炼",
    checkup: "健康检查",
    appointment: "医疗预约",
  };
  return labels[type] || type;
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    pending: "待完成",
    completed: "已完成",
    overdue: "已逾期",
  };
  return labels[status] || status;
};

// 格式化日期标题
const formatDateHeader = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return "今天";
  } else if (date.toDateString() === yesterday.toDateString()) {
    return "昨天";
  } else {
    return date.toLocaleDateString("zh-CN", {
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  }
};

// 标记为完成
const markAsCompleted = (activity: any) => {
  activity.status = "completed";
  console.log("标记完成:", activity.title);
};

// 编辑活动
const editActivity = (activity: any) => {
  console.log("编辑活动:", activity.title);
};

// 跳转到添加活动
const goToAddActivity = () => {
  router.push("/activities?action=add");
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.timeline-filters {
  margin-bottom: var(--spacing-6);
}

.filters-content {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
  flex-wrap: wrap;
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-right: var(--spacing-2);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

.timeline-content {
  margin-bottom: var(--spacing-6);
}

.timeline-container {
  position: relative;
}

.timeline {
  position: relative;
}

.timeline-group {
  margin-bottom: var(--spacing-8);
}

.date-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.date-line {
  flex: 1;
  height: 1px;
  background-color: var(--theme-border);
}

.date-badge {
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-full);
  margin: 0 var(--spacing-4);
}

.date-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.activities-list {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: var(--spacing-6);
  position: relative;
}

.timeline-item:last-child .marker-line {
  display: none;
}

.timeline-marker {
  position: relative;
  margin-right: var(--spacing-4);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.marker-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: white;
  flex-shrink: 0;
  z-index: 2;
}

.marker-medication {
  background-color: var(--color-info);
}

.marker-exercise {
  background-color: var(--color-success);
}

.marker-checkup {
  background-color: var(--color-warning);
}

.marker-appointment {
  background-color: var(--color-primary);
}

.marker-line {
  width: 2px;
  height: 60px;
  background-color: var(--theme-border);
  margin-top: var(--spacing-2);
}

.timeline-content-item {
  flex: 1;
}

.activity-card {
  background-color: var(--theme-bg-secondary);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  transition: all var(--transition-normal);
}

.activity-card:hover {
  box-shadow: var(--shadow-md);
}

.timeline-item.completed .activity-card {
  opacity: 0.8;
  border-color: var(--color-success);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.activity-info {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.activity-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
  margin-left: var(--spacing-4);
}

.activity-time {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.activity-member {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.activity-details {
  margin-bottom: var(--spacing-4);
}

.activity-badges {
  display: flex;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  flex-wrap: wrap;
}

.type-badge,
.status-badge,
.priority-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.type-medication {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.type-exercise {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.type-checkup {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.type-appointment {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.status-completed {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-pending {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.priority-badge {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.activity-notes {
  margin-bottom: var(--spacing-3);
}

.activity-notes h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.activity-notes p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.activity-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.activity-tag {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
}

.activity-actions {
  display: flex;
  gap: var(--spacing-2);
}

.empty-timeline {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-timeline h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-timeline p {
  font-size: var(--font-size-base);
  margin: 0 0 var(--spacing-4) 0;
}

.timeline-stats {
  margin-bottom: var(--spacing-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-4);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
}

.stat-value {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range,
  .member-filter,
  .type-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .filter-select {
    min-width: 150px;
  }

  .timeline-item {
    flex-direction: column;
  }

  .timeline-marker {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: var(--spacing-3);
  }

  .marker-line {
    width: 60px;
    height: 2px;
    margin-top: 0;
    margin-left: var(--spacing-2);
  }

  .activity-header {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .activity-meta {
    align-items: flex-start;
    margin-left: 0;
  }

  .activity-actions {
    justify-content: stretch;
  }

  .activity-actions .btn {
    flex: 1;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
