<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 成员选择器 -->
      <div class="member-selector">
        <ContentCard title="选择家庭成员" size="sm">
          <div class="member-tabs">
            <button
              v-for="member in familyMembers"
              :key="member.id"
              class="member-tab"
              :class="{ active: selectedMember?.id === member.id }"
              @click="selectMember(member)"
            >
              <div class="member-avatar">
                <span>{{ member.name.charAt(0) }}</span>
              </div>
              <span class="member-name">{{ member.name }}</span>
            </button>
          </div>
        </ContentCard>
      </div>

      <!-- 预测内容 -->
      <div v-if="selectedMember" class="prediction-content">
        <!-- 预测概览 -->
        <div class="prediction-overview">
          <ContentCard
            title="健康预测概览"
            subtitle="基于当前数据的未来健康趋势"
          >
            <div class="overview-grid">
              <div class="overview-item">
                <div class="overview-icon good">📈</div>
                <div class="overview-info">
                  <h4>整体趋势</h4>
                  <p class="trend-positive">持续改善</p>
                  <small>预计未来3个月健康状况将持续向好</small>
                </div>
              </div>

              <div class="overview-item">
                <div class="overview-icon warning">⚠️</div>
                <div class="overview-info">
                  <h4>风险等级</h4>
                  <p class="risk-medium">中等风险</p>
                  <small>需要关注血压变化趋势</small>
                </div>
              </div>

              <div class="overview-item">
                <div class="overview-icon info">🎯</div>
                <div class="overview-info">
                  <h4>目标达成</h4>
                  <p class="goal-progress">75%</p>
                  <small>预计2个月内达成健康目标</small>
                </div>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 具体预测 -->
        <div class="predictions-grid">
          <!-- 血压预测 -->
          <div class="prediction-card">
            <ContentCard title="血压趋势预测" subtitle="未来3个月血压变化预测">
              <div class="prediction-chart">
                <!-- 简化的预测图表 -->
                <div class="chart-container">
                  <div class="chart-header">
                    <span class="chart-title">收缩压/舒张压 (mmHg)</span>
                    <span class="chart-period">未来90天</span>
                  </div>
                  <div class="chart-placeholder">
                    <div class="chart-line">
                      <svg viewBox="0 0 300 120" class="prediction-svg">
                        <!-- 历史数据线 -->
                        <polyline
                          points="10,80 30,75 50,70 70,65 90,60"
                          class="history-line"
                          fill="none"
                          stroke="#3B82F6"
                          stroke-width="2"
                        />
                        <!-- 预测数据线 -->
                        <polyline
                          points="90,60 110,58 130,55 150,52 170,50 190,48 210,45 230,42 250,40 270,38 290,35"
                          class="prediction-line"
                          fill="none"
                          stroke="#10B981"
                          stroke-width="2"
                          stroke-dasharray="5,5"
                        />
                        <!-- 目标线 -->
                        <line
                          x1="10"
                          y1="45"
                          x2="290"
                          y2="45"
                          stroke="#EF4444"
                          stroke-width="1"
                          stroke-dasharray="3,3"
                        />
                      </svg>
                    </div>
                    <div class="chart-labels">
                      <span>历史</span>
                      <span>现在</span>
                      <span>预测</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="prediction-details">
                <div class="detail-item">
                  <span class="detail-label">当前值:</span>
                  <span class="detail-value">125/80 mmHg</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">预测值 (3个月后):</span>
                  <span class="detail-value prediction-good">118/75 mmHg</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">目标值:</span>
                  <span class="detail-value">&lt; 120/80 mmHg</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">达成概率:</span>
                  <span class="detail-value">85%</span>
                </div>
              </div>

              <div class="prediction-factors">
                <h5>影响因素:</h5>
                <div class="factors-list">
                  <span class="factor positive">坚持用药 (+15%)</span>
                  <span class="factor positive">规律运动 (+12%)</span>
                  <span class="factor positive">饮食控制 (+8%)</span>
                  <span class="factor negative">工作压力 (-5%)</span>
                </div>
              </div>
            </ContentCard>
          </div>

          <!-- 体重预测 -->
          <div class="prediction-card">
            <ContentCard title="体重变化预测" subtitle="基于当前减重计划的预测">
              <div class="weight-prediction">
                <div class="weight-chart">
                  <div class="weight-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 60%"></div>
                      <div class="progress-prediction" style="width: 85%"></div>
                    </div>
                    <div class="progress-labels">
                      <span class="start-weight">75kg</span>
                      <span class="current-weight">72.5kg</span>
                      <span class="target-weight">70kg</span>
                    </div>
                  </div>
                </div>

                <div class="weight-details">
                  <div class="detail-row">
                    <span>当前体重:</span>
                    <span class="current">72.5 kg</span>
                  </div>
                  <div class="detail-row">
                    <span>目标体重:</span>
                    <span class="target">70.0 kg</span>
                  </div>
                  <div class="detail-row">
                    <span>预计达成时间:</span>
                    <span class="prediction">6-8周</span>
                  </div>
                  <div class="detail-row">
                    <span>每周减重:</span>
                    <span class="rate">0.3-0.5 kg</span>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>

          <!-- 健康风险预测 -->
          <div class="prediction-card">
            <ContentCard title="健康风险预测" subtitle="未来可能面临的健康风险">
              <div class="risk-predictions">
                <div class="risk-item low-risk">
                  <div class="risk-header">
                    <div class="risk-icon">💚</div>
                    <div class="risk-info">
                      <h4>心血管疾病</h4>
                      <span class="risk-level">低风险</span>
                    </div>
                    <div class="risk-percentage">15%</div>
                  </div>
                  <p class="risk-description">
                    基于当前血压控制情况，未来5年心血管疾病风险较低
                  </p>
                </div>

                <div class="risk-item medium-risk">
                  <div class="risk-header">
                    <div class="risk-icon">🟡</div>
                    <div class="risk-info">
                      <h4>糖尿病</h4>
                      <span class="risk-level">中等风险</span>
                    </div>
                    <div class="risk-percentage">35%</div>
                  </div>
                  <p class="risk-description">
                    需要继续控制体重和饮食，定期监测血糖水平
                  </p>
                </div>

                <div class="risk-item low-risk">
                  <div class="risk-header">
                    <div class="risk-icon">💚</div>
                    <div class="risk-info">
                      <h4>骨质疏松</h4>
                      <span class="risk-level">低风险</span>
                    </div>
                    <div class="risk-percentage">20%</div>
                  </div>
                  <p class="risk-description">当前运动量充足，骨密度保持良好</p>
                </div>
              </div>
            </ContentCard>
          </div>

          <!-- 预防建议 -->
          <div class="prediction-card">
            <ContentCard title="预防性建议" subtitle="降低风险的具体措施">
              <div class="prevention-suggestions">
                <div class="suggestion-item">
                  <div class="suggestion-header">
                    <span class="suggestion-icon">🏃</span>
                    <h4>增强运动强度</h4>
                  </div>
                  <p>
                    在当前基础上增加10%的运动量，可将心血管风险降低至10%以下
                  </p>
                  <div class="suggestion-impact">
                    <span class="impact-label">风险降低:</span>
                    <span class="impact-value">-5%</span>
                  </div>
                </div>

                <div class="suggestion-item">
                  <div class="suggestion-header">
                    <span class="suggestion-icon">🥗</span>
                    <h4>优化饮食结构</h4>
                  </div>
                  <p>增加膳食纤维摄入，减少精制糖类，可有效预防糖尿病</p>
                  <div class="suggestion-impact">
                    <span class="impact-label">风险降低:</span>
                    <span class="impact-value">-8%</span>
                  </div>
                </div>

                <div class="suggestion-item">
                  <div class="suggestion-header">
                    <span class="suggestion-icon">🩺</span>
                    <h4>定期健康检查</h4>
                  </div>
                  <p>每6个月进行一次全面体检，及早发现潜在问题</p>
                  <div class="suggestion-impact">
                    <span class="impact-label">早期发现率:</span>
                    <span class="impact-value">+25%</span>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>
        </div>

        <!-- AI预测说明 -->
        <div class="prediction-disclaimer">
          <ContentCard title="预测说明" size="sm">
            <div class="disclaimer-content">
              <div class="disclaimer-icon">🤖</div>
              <div class="disclaimer-text">
                <p>
                  以上预测基于AI算法分析您的历史健康数据、生活习惯和医学研究数据得出。
                  预测结果仅供参考，不能替代专业医疗建议。如有健康问题，请及时咨询医生。
                </p>
                <div class="disclaimer-details">
                  <span>数据来源: 个人健康记录、医学文献、统计模型</span>
                  <span>预测准确率: 约75-85%</span>
                  <span>更新频率: 每周自动更新</span>
                </div>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>

      <!-- 未选择成员时的提示 -->
      <div v-else class="no-member-selected">
        <ContentCard>
          <div class="empty-state-large">
            <div class="empty-icon">🔮</div>
            <h3>请选择家庭成员</h3>
            <p>选择一个家庭成员来查看其健康预测</p>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 当前选中的成员
const selectedMember = ref(null);

// 家庭成员数据
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 选择成员
const selectMember = (member: any) => {
  selectedMember.value = member;
};

// 组件挂载时选择第一个成员
onMounted(() => {
  if (familyMembers.value.length > 0) {
    selectedMember.value = familyMembers.value[0];
  }
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.member-selector {
  margin-bottom: var(--spacing-6);
}

.member-tabs {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.member-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.member-tab:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.member-tab.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: white;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.member-tab.active .member-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.prediction-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.prediction-overview {
  margin-bottom: var(--spacing-6);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.overview-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.overview-icon.good {
  background-color: var(--color-success-light);
}

.overview-icon.warning {
  background-color: var(--color-warning-light);
}

.overview-icon.info {
  background-color: var(--color-info-light);
}

.overview-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.overview-info p {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-1) 0;
}

.trend-positive {
  color: var(--color-success);
}

.risk-medium {
  color: var(--color-warning);
}

.goal-progress {
  color: var(--color-info);
}

.overview-info small {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

.prediction-card {
  height: fit-content;
}

.chart-container {
  margin-bottom: var(--spacing-4);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.chart-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.chart-period {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.chart-placeholder {
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
}

.chart-line {
  height: 120px;
  margin-bottom: var(--spacing-2);
}

.prediction-svg {
  width: 100%;
  height: 100%;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.prediction-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.detail-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.prediction-good {
  color: var(--color-success);
}

.prediction-factors h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.factors-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.factor {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.factor.positive {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.factor.negative {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.weight-prediction {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.weight-progress {
  position: relative;
}

.progress-bar {
  height: 20px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-normal);
}

.progress-prediction {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 60%,
    var(--color-success) 100%
  );
  border-radius: var(--border-radius-full);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.weight-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
}

.current {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.target {
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
}

.prediction {
  color: var(--color-info);
  font-weight: var(--font-weight-medium);
}

.risk-predictions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.risk-item {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  border-left: 4px solid;
}

.low-risk {
  border-left-color: var(--color-success);
  background-color: var(--color-success-light);
}

.medium-risk {
  border-left-color: var(--color-warning);
  background-color: var(--color-warning-light);
}

.high-risk {
  border-left-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.risk-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.risk-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.risk-info {
  flex: 1;
}

.risk-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.risk-level {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.risk-percentage {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
}

.risk-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.prevention-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.suggestion-item {
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.suggestion-icon {
  font-size: var(--font-size-lg);
}

.suggestion-header h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.suggestion-item p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-3) 0;
  line-height: var(--line-height-normal);
}

.suggestion-impact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-sm);
}

.impact-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.impact-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-success);
}

.prediction-disclaimer {
  margin-top: var(--spacing-6);
}

.disclaimer-content {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.disclaimer-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.disclaimer-text p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-3) 0;
  line-height: var(--line-height-relaxed);
}

.disclaimer-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.disclaimer-details span {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.no-member-selected {
  margin-top: var(--spacing-8);
}

.empty-state-large {
  text-align: center;
  padding: var(--spacing-12);
}

.empty-state-large .empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state-large h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state-large p {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-tabs {
    flex-direction: column;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .predictions-grid {
    grid-template-columns: 1fr;
  }

  .overview-item {
    flex-direction: column;
    text-align: center;
  }

  .risk-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }

  .suggestion-impact {
    flex-direction: column;
    gap: var(--spacing-1);
    text-align: center;
  }

  .disclaimer-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>
