/* 设计系统变量定义 */

/* ===== 色彩系统 ===== */

/* 基础色彩 */
:root {
  --color-white: #FFFFFF;
  --color-gray-50: #F8FAFC;
  --color-gray-100: #F1F5F9;
  --color-gray-200: #E2E8F0;
  --color-gray-300: #CBD5E1;
  --color-gray-400: #94A3B8;
  --color-gray-500: #64748B;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1E293B;
  --color-gray-900: #0F172A;

  /* 健康状态色彩 */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-success-dark: #047857;

  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-warning-dark: #D97706;

  --color-danger: #EF4444;
  --color-danger-light: #FEE2E2;
  --color-danger-dark: #DC2626;

  --color-info: #3B82F6;
  --color-info-light: #DBEAFE;
  --color-info-dark: #2563EB;

  /* 品牌色彩 */
  --color-primary: #6366F1;
  --color-primary-light: #E0E7FF;
  --color-primary-dark: #4F46E5;

  --color-secondary: #8B5CF6;
  --color-accent: #06B6D4;

  /* ===== 字体系统 ===== */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 
                      'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 
                      'Helvetica Neue', sans-serif;
  --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 
                      'Source Code Pro', monospace;

  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;

  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ===== 间距系统 ===== */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;

  /* 组件间距 */
  --component-padding-sm: var(--spacing-3);
  --component-padding-md: var(--spacing-4);
  --component-padding-lg: var(--spacing-6);

  --component-margin-sm: var(--spacing-4);
  --component-margin-md: var(--spacing-6);
  --component-margin-lg: var(--spacing-8);

  /* ===== 圆角系统 ===== */
  --border-radius-none: 0;
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 9999px;

  /* ===== 阴影系统 ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
               0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
               0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
               0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* ===== 断点系统 ===== */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;

  /* ===== 过渡效果 ===== */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* ===== 主题色彩 ===== */
  --theme-bg-primary: var(--color-white);
  --theme-bg-secondary: var(--color-gray-50);
  --theme-text-primary: var(--color-gray-900);
  --theme-text-secondary: var(--color-gray-600);
  --theme-border: var(--color-gray-200);
}

/* 深色主题 */
[data-theme="dark"] {
  --theme-bg-primary: var(--color-gray-900);
  --theme-bg-secondary: var(--color-gray-800);
  --theme-text-primary: var(--color-gray-100);
  --theme-text-secondary: var(--color-gray-400);
  --theme-border: var(--color-gray-700);
}
