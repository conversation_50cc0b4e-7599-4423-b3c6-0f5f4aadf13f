<template>
  <el-dialog
    v-model="visible"
    title="邀请历史"
    width="800px"
    :before-close="handleClose"
  >
    <div class="invite-history-content">
      <!-- 筛选工具栏 -->
      <div class="toolbar">
        <div class="filters">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadInviteHistory"
          />
          
          <el-select
            v-model="selectedStatus"
            placeholder="邀请状态"
            clearable
            @change="loadInviteHistory"
          >
            <el-option label="全部状态" value="" />
            <el-option label="待接受" value="pending" />
            <el-option label="已接受" value="accepted" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已过期" value="expired" />
            <el-option label="已撤销" value="revoked" />
          </el-select>

          <el-select
            v-model="selectedMethod"
            placeholder="邀请方式"
            clearable
            @change="loadInviteHistory"
          >
            <el-option label="全部方式" value="" />
            <el-option label="邀请链接" value="link" />
            <el-option label="邀请码" value="code" />
          </el-select>
        </div>

        <div class="actions">
          <el-button type="primary" @click="showCreateInviteDialog">
            <el-icon><Plus /></el-icon>
            新建邀请
          </el-button>
          <el-button @click="batchRevokeInvites" :disabled="selectedInvites.length === 0">
            <el-icon><Close /></el-icon>
            批量撤销
          </el-button>
        </div>
      </div>

      <!-- 邀请历史列表 -->
      <div class="invite-history-list">
        <el-table
          :data="inviteHistory"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="createdAt" label="创建时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column prop="inviter" label="邀请人" width="100" />

          <el-table-column prop="method" label="邀请方式" width="100">
            <template #default="{ row }">
              <el-tag :type="row.method === 'link' ? 'primary' : 'success'" size="small">
                {{ row.method === 'link' ? '邀请链接' : '邀请码' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="inviteCode" label="邀请码/链接" width="150">
            <template #default="{ row }">
              <div class="invite-code">
                <span class="code-text">{{ getInviteDisplay(row) }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="copyInviteCode(row)"
                >
                  复制
                </el-button>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="targetRole" label="目标角色" width="100" />

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="expiresAt" label="过期时间" width="180">
            <template #default="{ row }">
              <div class="expire-time">
                <span>{{ formatDateTime(row.expiresAt) }}</span>
                <div v-if="isExpiringSoon(row.expiresAt)" class="expire-warning">
                  即将过期
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="acceptedBy" label="接受人" width="100">
            <template #default="{ row }">
              {{ row.acceptedBy || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <div class="actions">
                <el-button
                  v-if="row.status === 'pending'"
                  type="text"
                  size="small"
                  @click="revokeInvite(row)"
                >
                  撤销
                </el-button>
                <el-button
                  v-if="row.status === 'expired' || row.status === 'revoked'"
                  type="text"
                  size="small"
                  @click="resendInvite(row)"
                >
                  重新发送
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="showInviteDetail(row)"
                >
                  详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="totalInvites"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadInviteHistory"
            @current-change="loadInviteHistory"
          />
        </div>
      </div>
    </div>

    <!-- 邀请详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="邀请详情"
      width="500px"
      append-to-body
    >
      <div v-if="selectedInvite" class="invite-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="邀请ID">
            {{ selectedInvite.id }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedInvite.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="邀请人">
            {{ selectedInvite.inviter }}
          </el-descriptions-item>
          <el-descriptions-item label="邀请方式">
            {{ selectedInvite.method === 'link' ? '邀请链接' : '邀请码' }}
          </el-descriptions-item>
          <el-descriptions-item label="邀请码/链接">
            <div class="invite-code-detail">
              <span>{{ getInviteDisplay(selectedInvite) }}</span>
              <el-button
                type="text"
                size="small"
                @click="copyInviteCode(selectedInvite)"
              >
                复制
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="目标角色">
            {{ selectedInvite.targetRole }}
          </el-descriptions-item>
          <el-descriptions-item label="权限设置">
            <div class="permissions-list">
              <el-tag
                v-for="permission in selectedInvite.permissions"
                :key="permission"
                size="small"
                class="permission-tag"
              >
                {{ getPermissionName(permission) }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(selectedInvite.status)" size="small">
              {{ getStatusText(selectedInvite.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ formatDateTime(selectedInvite.expiresAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedInvite.acceptedBy" label="接受人">
            {{ selectedInvite.acceptedBy }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedInvite.acceptedAt" label="接受时间">
            {{ formatDateTime(selectedInvite.acceptedAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedInvite.revokedAt" label="撤销时间">
            {{ formatDateTime(selectedInvite.revokedAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedInvite.revokedReason" label="撤销原因">
            {{ selectedInvite.revokedReason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  familyId?: number | string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'create-invite'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const showDetailDialog = ref(false)
const selectedInvite = ref<any>(null)
const selectedInvites = ref<any[]>([])

// 筛选条件
const dateRange = ref<[Date, Date] | null>(null)
const selectedStatus = ref('')
const selectedMethod = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalInvites = ref(0)

// 邀请历史数据
const inviteHistory = ref([
  {
    id: 'INV001',
    createdAt: new Date('2024-01-20 10:30:00'),
    inviter: '张先生',
    method: 'link',
    inviteCode: 'https://health.example.com/invite/abc123',
    targetRole: '家庭成员',
    permissions: ['viewHealth', 'manageReminders'],
    status: 'accepted',
    expiresAt: new Date('2024-01-27 10:30:00'),
    acceptedBy: '王女士',
    acceptedAt: new Date('2024-01-21 15:20:00')
  },
  {
    id: 'INV002',
    createdAt: new Date('2024-01-19 14:15:00'),
    inviter: '李女士',
    method: 'code',
    inviteCode: '123456',
    targetRole: '子女',
    permissions: ['viewHealth'],
    status: 'pending',
    expiresAt: new Date('2024-01-26 14:15:00')
  },
  {
    id: 'INV003',
    createdAt: new Date('2024-01-18 09:00:00'),
    inviter: '张先生',
    method: 'link',
    inviteCode: 'https://health.example.com/invite/def456',
    targetRole: '其他亲属',
    permissions: ['viewHealth'],
    status: 'expired',
    expiresAt: new Date('2024-01-25 09:00:00')
  },
  {
    id: 'INV004',
    createdAt: new Date('2024-01-17 16:45:00'),
    inviter: '张先生',
    method: 'code',
    inviteCode: '789012',
    targetRole: '家庭成员',
    permissions: ['viewHealth', 'manageReminders'],
    status: 'revoked',
    expiresAt: new Date('2024-01-24 16:45:00'),
    revokedAt: new Date('2024-01-18 10:00:00'),
    revokedReason: '邀请对象变更'
  }
])

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadInviteHistory()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadInviteHistory = () => {
  // 模拟加载邀请历史
  totalInvites.value = inviteHistory.value.length
  console.log('加载邀请历史:', {
    dateRange: dateRange.value,
    status: selectedStatus.value,
    method: selectedMethod.value,
    page: currentPage.value,
    pageSize: pageSize.value
  })
}

const handleSelectionChange = (selection: any[]) => {
  selectedInvites.value = selection
}

const getInviteDisplay = (invite: any) => {
  if (invite.method === 'link') {
    return invite.inviteCode.substring(0, 30) + '...'
  }
  return invite.inviteCode
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'warning',
    accepted: 'success',
    rejected: 'danger',
    expired: 'info',
    revoked: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待接受',
    accepted: '已接受',
    rejected: '已拒绝',
    expired: '已过期',
    revoked: '已撤销'
  }
  return texts[status] || status
}

const getPermissionName = (permission: string) => {
  const names: Record<string, string> = {
    viewHealth: '查看健康数据',
    manageReminders: '管理提醒',
    inviteMembers: '邀请成员',
    manageFamilySettings: '管理设置',
    removeMembers: '移除成员'
  }
  return names[permission] || permission
}

const isExpiringSoon = (expiresAt: Date) => {
  const now = new Date()
  const timeDiff = expiresAt.getTime() - now.getTime()
  const hoursDiff = timeDiff / (1000 * 3600)
  return hoursDiff > 0 && hoursDiff <= 24
}

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const copyInviteCode = async (invite: any) => {
  try {
    await navigator.clipboard.writeText(invite.inviteCode)
    ElMessage.success('邀请码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const revokeInvite = async (invite: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销邀请"${invite.id}"吗？`,
      '确认撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    invite.status = 'revoked'
    invite.revokedAt = new Date()
    ElMessage.success('邀请已撤销')
  } catch (error) {
    // 用户取消
  }
}

const resendInvite = (invite: any) => {
  console.log('重新发送邀请:', invite)
  ElMessage.success('邀请已重新发送')
}

const batchRevokeInvites = async () => {
  if (selectedInvites.value.length === 0) {
    ElMessage.warning('请选择要撤销的邀请')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要撤销选中的 ${selectedInvites.value.length} 个邀请吗？`,
      '批量撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedInvites.value.forEach(invite => {
      invite.status = 'revoked'
      invite.revokedAt = new Date()
    })
    
    ElMessage.success('批量撤销成功')
  } catch (error) {
    // 用户取消
  }
}

const showCreateInviteDialog = () => {
  emit('create-invite')
  visible.value = false
}

const showInviteDetail = (invite: any) => {
  selectedInvite.value = invite
  showDetailDialog.value = true
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.invite-history-content {
  max-height: 700px;
  overflow-y: auto;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.actions {
  display: flex;
  gap: 8px;
}

.invite-code {
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-text {
  font-family: monospace;
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.expire-time {
  display: flex;
  flex-direction: column;
}

.expire-warning {
  font-size: 11px;
  color: var(--color-warning);
  margin-top: 2px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.invite-code-detail {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-tag {
  font-size: 11px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
