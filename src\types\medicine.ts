// 药品相关类型定义

export interface Medicine {
  id: string;
  name: string;
  category: 'prescription' | 'otc' | 'supplement' | 'other';
  dosage: string;
  unit: string;
  quantity: number;
  minQuantity: number;
  expiryDate: string;
  manufacturer: string;
  batchNumber?: string;
  instructions: string;
  sideEffects?: string;
  contraindications?: string;
  storage: string;
  price?: number;
  purchaseDate?: string;
  location?: string;
  notes?: string;
  userId: string;
  familyId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MedicineStats {
  total: number;
  expiringSoon: number;
  lowStock: number;
  expired: number;
  byCategory: {
    prescription: number;
    otc: number;
    supplement: number;
    other: number;
  };
}

export interface MedicineFilter {
  category?: string;
  status?: 'all' | 'normal' | 'expiring' | 'expired' | 'lowStock';
  search?: string;
}

export interface MedicineFormData {
  name: string;
  category: Medicine['category'];
  dosage: string;
  unit: string;
  quantity: number;
  minQuantity: number;
  expiryDate: string;
  manufacturer: string;
  batchNumber?: string;
  instructions: string;
  sideEffects?: string;
  contraindications?: string;
  storage: string;
  price?: number;
  purchaseDate?: string;
  location?: string;
  notes?: string;
}

export interface StockAdjustment {
  medicineId: string;
  type: 'add' | 'subtract' | 'set';
  quantity: number;
  reason: string;
  date: string;
}

export interface MedicineReminder {
  id: string;
  medicineId: string;
  userId: string;
  type: 'dosage' | 'refill' | 'expiry';
  time: string;
  frequency: 'once' | 'daily' | 'weekly' | 'monthly';
  isActive: boolean;
  message: string;
  createdAt: string;
}
