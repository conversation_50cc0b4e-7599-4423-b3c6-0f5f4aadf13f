<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 紧急警告 -->
      <div class="emergency-warning">
        <ContentCard class="warning-card">
          <div class="warning-content">
            <div class="warning-icon">🚨</div>
            <div class="warning-text">
              <h3>紧急医疗信息卡</h3>
              <p>此卡片包含重要的医疗信息，请在紧急情况下向医护人员出示</p>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 成员选择器 -->
      <div class="member-selector">
        <ContentCard title="选择家庭成员" size="sm">
          <div class="member-tabs">
            <button
              v-for="member in familyMembers"
              :key="member.id"
              class="member-tab"
              :class="{ active: selectedMember?.id === member.id }"
              @click="selectMember(member)"
            >
              <div class="member-avatar">
                <span>{{ member.name.charAt(0) }}</span>
              </div>
              <span class="member-name">{{ member.name }}</span>
            </button>
          </div>
        </ContentCard>
      </div>

      <!-- 紧急资料卡内容 -->
      <div v-if="selectedMember" class="emergency-card-content">
        <div class="emergency-card">
          <ContentCard class="emergency-info-card">
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="header-left">
                <h2 class="card-title">紧急医疗信息卡</h2>
                <p class="card-subtitle">Emergency Medical Information Card</p>
              </div>
              <div class="header-right">
                <div class="emergency-logo">🏥</div>
              </div>
            </div>

            <!-- 个人基本信息 -->
            <div class="info-section">
              <h3 class="section-title">个人基本信息 / Personal Information</h3>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">姓名 / Name:</span>
                  <span class="info-value">{{ selectedMember.name }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">年龄 / Age:</span>
                  <span class="info-value">{{ selectedMember.age }}岁</span>
                </div>
                <div class="info-item">
                  <span class="info-label">性别 / Gender:</span>
                  <span class="info-value">{{ selectedMember.gender }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">血型 / Blood Type:</span>
                  <span class="info-value">{{ selectedMember.bloodType }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">身份证号 / ID:</span>
                  <span class="info-value">{{ selectedMember.idCard }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">住址 / Address:</span>
                  <span class="info-value">{{ selectedMember.address }}</span>
                </div>
              </div>
            </div>

            <!-- 重要医疗信息 -->
            <div class="info-section">
              <h3 class="section-title">
                重要医疗信息 / Important Medical Information
              </h3>

              <!-- 既往病史 -->
              <div class="medical-subsection">
                <h4 class="subsection-title">既往病史 / Medical History:</h4>
                <div class="medical-tags">
                  <span
                    v-for="condition in selectedMember.medicalHistory"
                    :key="condition"
                    class="medical-tag condition-tag"
                  >
                    {{ condition }}
                  </span>
                  <span
                    v-if="selectedMember.medicalHistory.length === 0"
                    class="no-data"
                  >
                    无 / None
                  </span>
                </div>
              </div>

              <!-- 药物过敏 -->
              <div class="medical-subsection">
                <h4 class="subsection-title">药物过敏 / Drug Allergies:</h4>
                <div class="medical-tags">
                  <span
                    v-for="allergy in selectedMember.allergies"
                    :key="allergy"
                    class="medical-tag allergy-tag"
                  >
                    {{ allergy }}
                  </span>
                  <span
                    v-if="selectedMember.allergies.length === 0"
                    class="no-data"
                  >
                    无 / None
                  </span>
                </div>
              </div>

              <!-- 最新健康数据 -->
              <div class="medical-subsection">
                <h4 class="subsection-title">
                  最新健康数据 / Latest Health Data:
                </h4>
                <div class="health-data-grid">
                  <div class="health-data-item">
                    <span class="data-label">血压 / Blood Pressure:</span>
                    <span class="data-value">{{
                      selectedMember.latestVitals.bloodPressure
                    }}</span>
                  </div>
                  <div class="health-data-item">
                    <span class="data-label">血糖 / Blood Sugar:</span>
                    <span class="data-value">{{
                      selectedMember.latestVitals.bloodSugar
                    }}</span>
                  </div>
                  <div class="health-data-item">
                    <span class="data-label">心率 / Heart Rate:</span>
                    <span class="data-value">{{
                      selectedMember.latestVitals.heartRate
                    }}</span>
                  </div>
                  <div class="health-data-item">
                    <span class="data-label">最近体检 / Last Checkup:</span>
                    <span class="data-value">{{
                      formatDate(selectedMember.latestVitals.lastCheckup)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 当前用药 -->
            <div class="info-section">
              <h3 class="section-title">当前用药 / Current Medications</h3>
              <div class="medications-list">
                <div
                  v-for="medication in selectedMember.currentMedications"
                  :key="medication.id"
                  class="medication-item"
                >
                  <div class="medication-info">
                    <span class="medication-name">{{ medication.name }}</span>
                    <span class="medication-dosage">{{
                      medication.dosage
                    }}</span>
                    <span class="medication-frequency">{{
                      medication.frequency
                    }}</span>
                  </div>
                </div>
                <div
                  v-if="selectedMember.currentMedications.length === 0"
                  class="no-medications"
                >
                  无当前用药 / No current medications
                </div>
              </div>
            </div>

            <!-- 主治医生信息 -->
            <div class="info-section">
              <h3 class="section-title">
                主治医生信息 / Primary Doctor Information
              </h3>
              <div class="doctor-info">
                <div class="doctor-item">
                  <span class="doctor-label">医生姓名 / Doctor Name:</span>
                  <span class="doctor-value">{{
                    selectedMember.primaryDoctor.name
                  }}</span>
                </div>
                <div class="doctor-item">
                  <span class="doctor-label">科室 / Department:</span>
                  <span class="doctor-value">{{
                    selectedMember.primaryDoctor.department
                  }}</span>
                </div>
                <div class="doctor-item">
                  <span class="doctor-label">医院 / Hospital:</span>
                  <span class="doctor-value">{{
                    selectedMember.primaryDoctor.hospital
                  }}</span>
                </div>
                <div class="doctor-item">
                  <span class="doctor-label">联系电话 / Phone:</span>
                  <span class="doctor-value phone-number">{{
                    selectedMember.primaryDoctor.phone
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 紧急联系人 -->
            <div class="info-section">
              <h3 class="section-title">紧急联系人 / Emergency Contacts</h3>
              <div class="emergency-contacts">
                <div
                  v-for="(contact, index) in selectedMember.emergencyContacts"
                  :key="contact.id"
                  class="contact-item"
                  :class="`priority-${index + 1}`"
                >
                  <div class="contact-priority">{{ index + 1 }}</div>
                  <div class="contact-info">
                    <div class="contact-name">{{ contact.name }}</div>
                    <div class="contact-relationship">
                      {{ contact.relationship }}
                    </div>
                  </div>
                  <div class="contact-phone">
                    <a :href="`tel:${contact.phone}`" class="phone-link">
                      {{ contact.phone }}
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- 使用说明 -->
            <div class="info-section">
              <h3 class="section-title">使用说明 / Instructions</h3>
              <div class="instructions">
                <div class="instruction-item">
                  <span class="instruction-number">1.</span>
                  <span class="instruction-text">
                    紧急情况下，请立即拨打120急救电话
                    <br />In emergency, call 120 immediately
                  </span>
                </div>
                <div class="instruction-item">
                  <span class="instruction-number">2.</span>
                  <span class="instruction-text">
                    向医护人员出示此卡片，提供重要医疗信息
                    <br />Show this card to medical personnel for important
                    medical information
                  </span>
                </div>
                <div class="instruction-item">
                  <span class="instruction-number">3.</span>
                  <span class="instruction-text">
                    联系紧急联系人，按优先级顺序拨打
                    <br />Contact emergency contacts in priority order
                  </span>
                </div>
              </div>
            </div>

            <!-- 卡片底部 -->
            <div class="card-footer">
              <div class="footer-info">
                <span class="update-time"
                  >最后更新 / Last Updated:
                  {{ formatDateTime(new Date()) }}</span
                >
              </div>
              <div class="footer-actions">
                <button class="btn btn-outline" @click="printCard">
                  打印卡片
                </button>
                <button class="btn btn-primary" @click="editCard">
                  编辑信息
                </button>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>

      <!-- 未选择成员时的提示 -->
      <div v-else class="no-member-selected">
        <ContentCard>
          <div class="empty-state-large">
            <div class="empty-icon">🆘</div>
            <h3>请选择家庭成员</h3>
            <p>选择一个家庭成员来查看其紧急资料卡</p>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 当前选中的成员
const selectedMember = ref(null);

// 家庭成员数据（包含紧急信息）
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    age: 45,
    gender: "男",
    bloodType: "A型",
    idCard: "110101********1234",
    address: "北京市朝阳区某某街道某某小区",
    medicalHistory: ["高血压", "糖尿病前期"],
    allergies: ["青霉素", "海鲜"],
    latestVitals: {
      bloodPressure: "125/80 mmHg",
      bloodSugar: "5.8 mmol/L",
      heartRate: "72 bpm",
      lastCheckup: new Date("2024-01-15"),
    },
    currentMedications: [
      {
        id: 1,
        name: "阿司匹林肠溶片",
        dosage: "100mg",
        frequency: "每日一次",
      },
      {
        id: 2,
        name: "氨氯地平片",
        dosage: "5mg",
        frequency: "每日一次",
      },
    ],
    primaryDoctor: {
      name: "李医生",
      department: "心内科",
      hospital: "市人民医院",
      phone: "010-12345678",
    },
    emergencyContacts: [
      {
        id: 1,
        name: "李女士",
        relationship: "配偶",
        phone: "139****9999",
      },
      {
        id: 2,
        name: "张小明",
        relationship: "儿子",
        phone: "138****8888",
      },
    ],
  },
  {
    id: 2,
    name: "李女士",
    age: 42,
    gender: "女",
    bloodType: "B型",
    idCard: "110101********5678",
    address: "北京市朝阳区某某街道某某小区",
    medicalHistory: [],
    allergies: ["花粉"],
    latestVitals: {
      bloodPressure: "115/75 mmHg",
      bloodSugar: "5.2 mmol/L",
      heartRate: "68 bpm",
      lastCheckup: new Date("2024-01-10"),
    },
    currentMedications: [],
    primaryDoctor: {
      name: "王医生",
      department: "妇科",
      hospital: "市妇幼保健院",
      phone: "010-87654321",
    },
    emergencyContacts: [
      {
        id: 1,
        name: "张先生",
        relationship: "配偶",
        phone: "138****8888",
      },
    ],
  },
]);

// 选择成员
const selectMember = (member: any) => {
  selectedMember.value = member;
};

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString("zh-CN");
};

// 打印卡片
const printCard = () => {
  window.print();
};

// 编辑卡片
const editCard = () => {
  console.log("编辑紧急资料卡");
};

// 组件挂载时选择第一个成员
onMounted(() => {
  if (familyMembers.value.length > 0) {
    selectedMember.value = familyMembers.value[0];
  }
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.emergency-warning {
  margin-bottom: var(--spacing-6);
}

.warning-card {
  border-left: 4px solid var(--color-danger);
  background-color: var(--color-danger-light);
}

.warning-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.warning-icon {
  font-size: var(--font-size-3xl);
  flex-shrink: 0;
}

.warning-text h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-danger-dark);
  margin: 0 0 var(--spacing-1) 0;
}

.warning-text p {
  font-size: var(--font-size-sm);
  color: var(--color-danger-dark);
  margin: 0;
}

.member-selector {
  margin-bottom: var(--spacing-6);
}

.member-tabs {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.member-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.member-tab:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.member-tab.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: white;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.member-tab.active .member-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.emergency-card {
  max-width: 800px;
  margin: 0 auto;
}

.emergency-info-card {
  border: 2px solid var(--color-danger);
  background-color: var(--theme-bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--color-danger);
  margin-bottom: var(--spacing-6);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-danger);
  margin: 0 0 var(--spacing-1) 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  font-style: italic;
}

.emergency-logo {
  font-size: var(--font-size-3xl);
  color: var(--color-danger);
}

.info-section {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--theme-border);
}

.info-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--color-danger-light);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.info-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
}

.info-value {
  font-size: var(--font-size-base);
  color: var(--theme-text-primary);
  font-weight: var(--font-weight-medium);
}

.medical-subsection {
  margin-bottom: var(--spacing-4);
}

.subsection-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.medical-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.medical-tag {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.condition-tag {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.allergy-tag {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.no-data {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  font-style: italic;
}

.health-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.health-data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.data-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.data-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.medications-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.medication-item {
  padding: var(--spacing-3);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  border-left: 3px solid var(--color-primary);
}

.medication-info {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.medication-name {
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  flex: 1;
}

.medication-dosage,
.medication-frequency {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.no-medications {
  text-align: center;
  padding: var(--spacing-4);
  color: var(--theme-text-secondary);
  font-style: italic;
}

.doctor-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.doctor-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.doctor-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
}

.doctor-value {
  font-size: var(--font-size-base);
  color: var(--theme-text-primary);
  font-weight: var(--font-weight-medium);
}

.phone-number {
  color: var(--color-primary);
  text-decoration: underline;
}

.emergency-contacts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  border-left: 4px solid;
}

.priority-1 {
  border-left-color: var(--color-danger);
}

.priority-2 {
  border-left-color: var(--color-warning);
}

.priority-3 {
  border-left-color: var(--color-info);
}

.contact-priority {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-danger);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-1);
}

.contact-relationship {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.contact-phone {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.phone-link {
  color: var(--color-primary);
  text-decoration: none;
}

.phone-link:hover {
  text-decoration: underline;
}

.instructions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.instruction-item {
  display: flex;
  gap: var(--spacing-3);
  align-items: flex-start;
}

.instruction-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--color-danger);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.instruction-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
  line-height: var(--line-height-relaxed);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-4);
  border-top: 2px solid var(--color-danger);
  margin-top: var(--spacing-6);
}

.update-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.footer-actions {
  display: flex;
  gap: var(--spacing-2);
}

.no-member-selected {
  margin-top: var(--spacing-8);
}

.empty-state-large {
  text-align: center;
  padding: var(--spacing-12);
}

.empty-state-large .empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state-large h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state-large p {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 打印样式 */
@media print {
  .emergency-warning,
  .member-selector,
  .footer-actions {
    display: none;
  }

  .emergency-card {
    max-width: none;
    margin: 0;
  }

  .emergency-info-card {
    border: 2px solid #000;
    box-shadow: none;
  }

  .card-title {
    color: #000;
  }

  .emergency-logo {
    color: #000;
  }

  .section-title {
    color: #000;
    border-bottom-color: #ccc;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-tabs {
    flex-direction: column;
  }

  .card-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .health-data-grid {
    grid-template-columns: 1fr;
  }

  .doctor-info {
    grid-template-columns: 1fr;
  }

  .medication-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }

  .card-footer {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }

  .footer-actions {
    justify-content: stretch;
  }

  .footer-actions .btn {
    flex: 1;
  }
}
</style>
