/**
 * 家庭管理服务
 * 提供家庭相关的API调用功能
 */

export interface FamilyMember {
  id: number | string;
  name: string;
  role: string;
  roleType: string;
  age: number;
  gender: string;
  avatar?: string;
  isAdmin: boolean;
  healthStatus: "good" | "attention" | "risk";
  joinedAt: Date;
  permissions: {
    viewHealth: boolean;
    manageReminders: boolean;
    inviteMembers: boolean;
    manageFamilySettings: boolean;
    removeMembers: boolean;
  };
}

export interface FamilyInfo {
  id: number | string;
  name: string;
  description?: string;
  createdAt: Date;
  members: FamilyMember[];
  settings: {
    dataSharing: boolean;
    healthReminder: boolean;
    emergencyContact: boolean;
  };
  adminIds: (number | string)[];
}

export interface CreateFamilyData {
  name: string;
  description?: string;
  role: string;
  customRole?: string;
}

export interface InviteData {
  familyId: number | string;
  method: "link" | "code";
  defaultRole: string;
  permissions: string[];
  expiresIn?: number;
}

export interface InviteResult {
  inviteCode?: string;
  inviteLink?: string;
  expiresAt: Date;
}

export interface FamilySettings {
  basic: {
    name: string;
    description?: string;
    address?: string;
    phone?: string;
    avatar?: string;
  };
  privacy: {
    dataSharing: boolean;
    reportSharing: boolean;
    guestMode: boolean;
    locationSharing: boolean;
    dataRetention: string;
  };
  notification: {
    medicationReminder: boolean;
    checkupReminder: boolean;
    abnormalAlert: boolean;
    memberActivity: boolean;
    permissionChange: boolean;
    methods: string[];
  };
  security: {
    twoFactorAuth: boolean;
    loginVerification: boolean;
    sessionTimeout: string;
    dataEncryption: boolean;
    operationLog: boolean;
  };
}

export interface ActivityLog {
  id: number | string;
  timestamp: Date;
  member: string;
  memberAvatar?: string;
  activityType: "member" | "permission" | "settings" | "data" | "login";
  action: string;
  description: string;
  ipAddress: string;
  device: string;
  userAgent: string;
  changes?: Array<{
    field: string;
    oldValue: string;
    newValue: string;
  }>;
}

export interface InviteHistory {
  id: string;
  createdAt: Date;
  inviter: string;
  method: "link" | "code";
  inviteCode: string;
  targetRole: string;
  permissions: string[];
  status: "pending" | "accepted" | "rejected" | "expired" | "revoked";
  expiresAt: Date;
  acceptedBy?: string;
  acceptedAt?: Date;
  revokedAt?: Date;
  revokedReason?: string;
}

/**
 * 家庭服务类
 */
class FamilyService {
  private baseUrl = "/api/family";

  /**
   * 创建新家庭
   */
  async createFamily(data: CreateFamilyData): Promise<FamilyInfo> {
    // 模拟API调用
    await this.delay(1000);

    const family: FamilyInfo = {
      id: Date.now(),
      name: data.name,
      description: data.description,
      createdAt: new Date(),
      members: [
        {
          id: 1,
          name: "我",
          role:
            data.role === "other"
              ? data.customRole!
              : this.getRoleDisplayName(data.role),
          roleType: data.role,
          age: 30,
          gender: "未知",
          isAdmin: true,
          healthStatus: "good",
          joinedAt: new Date(),
          permissions: {
            viewHealth: true,
            manageReminders: true,
            inviteMembers: true,
            manageFamilySettings: true,
            removeMembers: true,
          },
        },
      ],
      settings: {
        dataSharing: true,
        healthReminder: true,
        emergencyContact: false,
      },
      adminIds: [1],
    };

    return family;
  }

  /**
   * 获取家庭详细信息
   */
  async getFamilyInfo(familyId: number | string): Promise<FamilyInfo> {
    // 模拟API调用
    await this.delay(500);

    return {
      id: familyId,
      name: "张家大院",
      description: "我们是一个温馨的大家庭，关爱彼此的健康",
      createdAt: new Date("2024-01-15"),
      members: [
        {
          id: 1,
          name: "张先生",
          role: "父亲",
          roleType: "father",
          age: 45,
          gender: "男",
          isAdmin: true,
          healthStatus: "good",
          joinedAt: new Date("2024-01-15"),
          permissions: {
            viewHealth: true,
            manageReminders: true,
            inviteMembers: true,
            manageFamilySettings: true,
            removeMembers: true,
          },
        },
        {
          id: 2,
          name: "李女士",
          role: "母亲",
          roleType: "mother",
          age: 42,
          gender: "女",
          isAdmin: true,
          healthStatus: "good",
          joinedAt: new Date("2024-01-15"),
          permissions: {
            viewHealth: true,
            manageReminders: true,
            inviteMembers: true,
            manageFamilySettings: true,
            removeMembers: true,
          },
        },
        {
          id: 3,
          name: "张小明",
          role: "儿子",
          roleType: "son",
          age: 18,
          gender: "男",
          isAdmin: false,
          healthStatus: "attention",
          joinedAt: new Date("2024-01-20"),
          permissions: {
            viewHealth: true,
            manageReminders: false,
            inviteMembers: false,
            manageFamilySettings: false,
            removeMembers: false,
          },
        },
      ],
      settings: {
        dataSharing: true,
        healthReminder: true,
        emergencyContact: true,
      },
      adminIds: [1, 2],
    };
  }

  /**
   * 生成邀请码或邀请链接
   */
  async generateInvite(data: InviteData): Promise<InviteResult> {
    // 模拟API调用
    await this.delay(1000);

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + (data.expiresIn || 24));

    if (data.method === "link") {
      return {
        inviteLink: `https://health.example.com/invite/${Date.now()}`,
        expiresAt,
      };
    } else {
      return {
        inviteCode: Math.random().toString().slice(2, 8),
        expiresAt,
      };
    }
  }

  /**
   * 更新成员角色
   */
  async updateMemberRole(
    familyId: number | string,
    memberId: number | string,
    roleData: {
      role: string;
      customRole?: string;
      isAdmin: boolean;
      permissions: Record<string, boolean>;
    }
  ): Promise<FamilyMember> {
    // 模拟API调用
    await this.delay(1000);

    return {
      id: memberId,
      name: "张小明",
      role:
        roleData.role === "custom"
          ? roleData.customRole!
          : this.getRoleDisplayName(roleData.role),
      roleType: roleData.role,
      age: 18,
      gender: "男",
      isAdmin: roleData.isAdmin,
      healthStatus: "attention",
      joinedAt: new Date("2024-01-20"),
      permissions: roleData.permissions as any,
    };
  }

  /**
   * 移除家庭成员
   */
  async removeMember(
    familyId: number | string,
    memberId: number | string,
    options: {
      reason?: string;
      dataHandling: "keep" | "transfer" | "delete";
      transferTo?: number | string;
    }
  ): Promise<void> {
    // 模拟API调用
    await this.delay(2000);

    console.log("移除成员:", { familyId, memberId, options });
  }

  /**
   * 获取角色显示名称
   */
  private getRoleDisplayName(role: string): string {
    const roleMap: Record<string, string> = {
      father: "父亲",
      mother: "母亲",
      son: "儿子",
      daughter: "女儿",
      grandfather: "爷爷",
      grandmother: "奶奶",
      relative: "其他亲属",
    };
    return roleMap[role] || role;
  }

  /**
   * 获取家庭设置
   */
  async getFamilySettings(familyId: number | string): Promise<FamilySettings> {
    await this.delay(500);

    return {
      basic: {
        name: "张家大院",
        description: "我们是一个温馨的大家庭，关爱彼此的健康",
        address: "北京市朝阳区xxx街道xxx号",
        phone: "138****8888",
        avatar: "",
      },
      privacy: {
        dataSharing: true,
        reportSharing: true,
        guestMode: false,
        locationSharing: true,
        dataRetention: "3years",
      },
      notification: {
        medicationReminder: true,
        checkupReminder: true,
        abnormalAlert: true,
        memberActivity: true,
        permissionChange: true,
        methods: ["app", "push"],
      },
      security: {
        twoFactorAuth: false,
        loginVerification: true,
        sessionTimeout: "4hours",
        dataEncryption: true,
        operationLog: true,
      },
    };
  }

  /**
   * 更新家庭设置
   */
  async updateFamilySettings(
    familyId: number | string,
    settings: Partial<FamilySettings>
  ): Promise<void> {
    await this.delay(1000);
    console.log("更新家庭设置:", { familyId, settings });
  }

  /**
   * 获取活动日志
   */
  async getActivityLogs(
    familyId: number | string,
    options?: {
      dateRange?: [Date, Date];
      member?: string;
      activityType?: string;
      keyword?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<{ logs: ActivityLog[]; total: number }> {
    await this.delay(500);

    const mockLogs: ActivityLog[] = [
      {
        id: 1,
        timestamp: new Date("2024-01-20 10:30:00"),
        member: "张先生",
        memberAvatar: "",
        activityType: "member",
        action: "邀请成员",
        description: '邀请新成员"王女士"加入家庭',
        ipAddress: "*************",
        device: "iPhone 15",
        userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)",
        changes: [{ field: "家庭成员数量", oldValue: "3", newValue: "4" }],
      },
    ];

    return {
      logs: mockLogs,
      total: mockLogs.length,
    };
  }

  /**
   * 获取邀请历史
   */
  async getInviteHistory(
    familyId: number | string,
    options?: {
      dateRange?: [Date, Date];
      status?: string;
      method?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<{ invites: InviteHistory[]; total: number }> {
    await this.delay(500);

    const mockInvites: InviteHistory[] = [
      {
        id: "INV001",
        createdAt: new Date("2024-01-20 10:30:00"),
        inviter: "张先生",
        method: "link",
        inviteCode: "https://health.example.com/invite/abc123",
        targetRole: "家庭成员",
        permissions: ["viewHealth", "manageReminders"],
        status: "accepted",
        expiresAt: new Date("2024-01-27 10:30:00"),
        acceptedBy: "王女士",
        acceptedAt: new Date("2024-01-21 15:20:00"),
      },
    ];

    return {
      invites: mockInvites,
      total: mockInvites.length,
    };
  }

  /**
   * 撤销邀请
   */
  async revokeInvite(
    familyId: number | string,
    inviteId: string,
    reason?: string
  ): Promise<void> {
    await this.delay(1000);
    console.log("撤销邀请:", { familyId, inviteId, reason });
  }

  /**
   * 批量更新成员权限
   */
  async batchUpdatePermissions(
    familyId: number | string,
    memberIds: (number | string)[],
    permissions: Record<string, boolean | string>
  ): Promise<void> {
    await this.delay(1500);
    console.log("批量更新权限:", { familyId, memberIds, permissions });
  }

  /**
   * 模拟延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// 导出单例实例
export const familyService = new FamilyService();
export default familyService;
