<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-illustration">
        <div class="error-code">403</div>
        <div class="error-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" />
          </svg>
        </div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">访问被拒绝</h1>
        <p class="error-description">
          抱歉，您没有权限访问此页面。
          请联系管理员获取相应权限，或返回首页继续浏览。
        </p>
        
        <div class="error-actions">
          <button class="btn btn-primary" @click="goHome">
            返回首页
          </button>
          <button class="btn btn-outline" @click="goBack">
            返回上页
          </button>
        </div>
        
        <div class="error-suggestions">
          <h3 class="suggestions-title">可能的原因：</h3>
          <ul class="suggestions-list">
            <li>您的账户权限不足</li>
            <li>页面需要特殊访问权限</li>
            <li>会话已过期，请重新登录</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/dashboard');
};

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-4);
}

.error-container {
  max-width: 600px;
  text-align: center;
}

.error-illustration {
  margin-bottom: var(--spacing-8);
}

.error-code {
  font-size: 120px;
  font-weight: var(--font-weight-bold);
  color: var(--color-warning);
  line-height: 1;
  margin-bottom: var(--spacing-4);
}

.error-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  color: var(--color-gray-400);
}

.error-content {
  background-color: var(--theme-bg-primary);
  padding: var(--spacing-8);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.error-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.error-description {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-6) 0;
}

.error-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  margin-bottom: var(--spacing-6);
}

.error-suggestions {
  text-align: left;
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
}

.suggestions-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-2);
  position: relative;
  padding-left: var(--spacing-4);
}

.suggestions-list li::before {
  content: '•';
  color: var(--color-warning);
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }
  
  .error-icon {
    width: 60px;
    height: 60px;
  }
  
  .error-content {
    padding: var(--spacing-6);
  }
  
  .error-actions {
    flex-direction: column;
  }
}
</style>
