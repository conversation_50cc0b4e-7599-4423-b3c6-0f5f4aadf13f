<template>
  <div class="action-card" @click="$emit('click')">
    <div class="action-icon" :style="{ backgroundColor: iconBgColor, color: iconColor }">
      <el-icon>
        <component :is="iconComponent" />
      </el-icon>
    </div>
    <div class="action-content">
      <h3 class="action-title">{{ title }}</h3>
      <p class="action-description">{{ description }}</p>
    </div>
    <div class="action-arrow">
      <el-icon><ArrowRight /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  House,
  Key,
  Phone,
  Document,
  Clock,
  FirstAidKit,
  ArrowRight
} from '@element-plus/icons-vue'

interface Props {
  title: string
  description: string
  icon: string
  iconColor: string
  iconBgColor: string
}

defineProps<Props>()
defineEmits<{
  click: []
}>()

const iconMap = {
  House,
  Key,
  Phone,
  Document,
  Clock,
  FirstAidKit
}

const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || House
})

const props = defineProps<Props>()
</script>

<style scoped>
.action-card {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--color-primary);
  transform: scaleY(0);
  transition: transform var(--transition-normal);
}

.action-card:hover::before {
  transform: scaleY(1);
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.action-arrow {
  color: var(--theme-text-tertiary);
  font-size: 16px;
  transition: all var(--transition-normal);
}

.action-card:hover .action-arrow {
  color: var(--color-primary);
  transform: translateX(4px);
}
</style>
