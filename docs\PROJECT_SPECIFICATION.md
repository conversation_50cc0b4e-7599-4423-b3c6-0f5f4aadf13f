# 家庭健康管理系统 - 项目规范文档

## 1. 项目概述

### 1.1 产品定位
"家庭健康管理系统"是一款以家庭为中心、由AI驱动的智能健康管理平台。它旨在通过整合多源健康数据、提供个性化智能分析与建议、以及在紧急情况下提供快速响应机制，帮助用户全面、系统地管理家庭成员的健康。

### 1.2 技术栈
- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier

### 1.3 设计理念
- **现代简洁**: 采用现代、简洁且专业的UI设计风格
- **色彩方案**: 以白色和浅灰色为主色调，搭配健康状态指示色彩
- **响应式设计**: 确保在桌面、平板和移动设备上的一致体验
- **用户体验**: 注重直观性和流畅性的交互设计

## 2. 核心模块架构

### 2.1 主要模块
1. **登录/注册模块** (`/login`, `/register`)
   - 微信一键登录
   - 产品价值引导
   - 家庭创建和成员邀请

2. **首页模块** (`/`)
   - 家庭健康概览
   - 紧急警报区域
   - 快速操作入口
   - 今日健康任务

3. **档案模块** (`/archive`)
   - 个人信息 (`/archive/personal-info`)
   - 健康档案中心 (`/archive/health-center`)
   - 个人药箱 (`/archive/medicine-box`)
   - 综合健康概要 (`/archive/health-overview`)
   - 紧急资料卡 (`/archive/emergency-card`)

4. **家庭模块** (`/family`)
   - 家庭成员管理
   - 家庭统计数据
   - 成员邀请
   - 家庭设置

5. **智能助手模块** (`/assistant`)
   - 智能对话 (`/assistant/smart-chat`)
   - 健康分析 (`/assistant/health-analysis`)
   - 健康报告 (`/assistant/health-report`)
   - 健康建议 (`/assistant/health-suggestions`)
   - 健康预测 (`/assistant/health-prediction`)

6. **健康日历模块** (`/calendar`)
   - 月视图日历
   - 任务管理
   - 提醒设置

7. **活动模块** (`/activities`)
   - 活动列表 (`/activities/activity-list`)
   - 活动时间线 (`/activities/activity-timeline`)
   - 数据分析 (`/activities/activity-analysis`)

8. **设置模块** (`/settings`)
   - 语言和显示设置
   - 无障碍功能
   - 通知设置
   - 隐私和安全
   - 数据管理

### 2.2 导航结构
侧边栏导航顺序：首页、档案、家庭、助手、日历、活动、设置

## 3. 目录结构规范

```
src/
├── components/              # 公共组件
│   ├── common/             # 通用组件
│   │   ├── PageContainer.vue
│   │   ├── ContentCard.vue
│   │   └── index.ts
│   └── layout/             # 布局组件
│       └── MainLayout.vue
├── views/                  # 页面组件
│   ├── auth/              # 认证相关页面
│   │   ├── Login.vue
│   │   ├── Register.vue
│   │   ├── Onboarding.vue
│   │   └── FamilySetup.vue
│   ├── dashboard/         # 首页
│   │   └── Dashboard.vue
│   ├── archive/           # 档案模块
│   │   ├── Archive.vue
│   │   ├── PersonalInfo.vue
│   │   ├── HealthCenter.vue
│   │   ├── MedicineBox.vue
│   │   ├── HealthOverview.vue
│   │   └── EmergencyCard.vue
│   ├── family/            # 家庭模块
│   │   └── Family.vue
│   ├── assistant/         # 智能助手模块
│   │   ├── Assistant.vue
│   │   ├── SmartChat.vue
│   │   ├── HealthAnalysis.vue
│   │   ├── HealthReport.vue
│   │   ├── HealthSuggestions.vue
│   │   └── HealthPrediction.vue
│   ├── calendar/          # 日历模块
│   │   └── Calendar.vue
│   ├── activities/        # 活动模块
│   │   ├── Activities.vue
│   │   ├── ActivityList.vue
│   │   ├── ActivityTimeline.vue
│   │   └── ActivityAnalysis.vue
│   └── settings/          # 设置模块
│       └── Settings.vue
├── stores/                # 状态管理
│   ├── auth.ts
│   ├── user.ts
│   ├── family.ts
│   └── index.ts
├── types/                 # 类型定义
│   ├── auth.ts
│   ├── user.ts
│   ├── family.ts
│   └── index.ts
├── utils/                 # 工具函数
│   ├── request.ts
│   ├── storage.ts
│   ├── validation.ts
│   └── index.ts
├── styles/                # 样式文件
│   ├── variables.css
│   ├── common.css
│   └── components.css
├── router/                # 路由配置
│   └── index.ts
└── main.ts               # 入口文件
```

## 4. 开发规范

### 4.1 命名规范
- **文件命名**: 使用 PascalCase (如: `PersonalInfo.vue`)
- **组件命名**: 使用 PascalCase
- **变量命名**: 使用 camelCase
- **常量命名**: 使用 UPPER_SNAKE_CASE
- **CSS类名**: 使用 kebab-case

### 4.2 代码规范
- 使用 TypeScript 严格模式
- 使用 Vue 3 Composition API
- 统一使用 `<script setup>` 语法
- 组件必须有明确的类型定义
- 使用 ESLint 和 Prettier 进行代码格式化

### 4.3 组件规范
- 每个组件都应该有清晰的职责
- 使用 Element Plus 组件库
- 自定义组件应该有完整的 props 类型定义
- 组件应该支持响应式设计

### 4.4 样式规范
- 使用 CSS 变量定义主题色彩
- 统一的间距和字体规范
- 组件样式使用 scoped
- 全局样式放在 styles 目录

## 5. 设计系统

### 5.1 色彩规范
- **主色调**: 白色 (#FFFFFF) 和浅灰色 (#F8FAFC)
- **健康状态色彩**:
  - 正常: 绿色 (#10B981)
  - 警告: 橙色 (#F59E0B)
  - 危险: 红色 (#EF4444)
  - 信息: 蓝色 (#3B82F6)

### 5.2 字体规范
- **主字体**: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto
- **字体大小**:
  - 标题: 24px, 20px, 18px
  - 正文: 16px, 14px
  - 辅助文字: 12px

### 5.3 间距规范
- **基础间距单位**: 4px
- **常用间距**: 8px, 12px, 16px, 20px, 24px, 32px

### 5.4 组件规范
- **卡片**: 统一的圆角 (8px) 和阴影
- **按钮**: 统一的高度 (32px, 40px) 和圆角 (6px)
- **表单**: 统一的输入框样式和验证提示

## 6. 数据流规范

### 6.1 状态管理
- 使用 Pinia 进行状态管理
- 按模块划分 store
- 异步操作使用 actions

### 6.2 API 调用
- 统一的请求封装
- 错误处理机制
- 加载状态管理

### 6.3 数据类型
- 所有接口数据都有对应的 TypeScript 类型
- 使用枚举定义常量值
- 表单验证使用统一的验证规则

## 7. 测试规范

### 7.1 单元测试
- 组件测试覆盖率 > 80%
- 工具函数测试覆盖率 > 90%
- 使用 Vitest 进行单元测试

### 7.2 集成测试
- 关键业务流程的端到端测试
- 路由跳转测试
- API 集成测试

## 8. 部署规范

### 8.1 构建配置
- 生产环境代码压缩
- 静态资源优化
- 环境变量配置

### 8.2 性能优化
- 路由懒加载
- 组件按需加载
- 图片资源优化

## 9. 维护规范

### 9.1 版本管理
- 使用语义化版本号
- 详细的 CHANGELOG
- Git 提交信息规范

### 9.2 文档维护
- 及时更新技术文档
- 组件使用说明
- API 接口文档

### 9.3 代码审查
- 所有代码变更都需要 Code Review
- 遵循既定的代码规范
- 性能和安全性检查
