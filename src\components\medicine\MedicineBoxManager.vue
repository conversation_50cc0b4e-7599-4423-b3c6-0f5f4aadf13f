<template>
  <div class="medicine-box-manager">
    <!-- 快速操作面板 -->
    <div class="quick-actions">
      <div class="action-card" @click="$emit('add-medicine')">
        <div class="action-icon">➕</div>
        <div class="action-text">添加药品</div>
      </div>
      
      <div class="action-card" @click="$emit('scan-medicine')">
        <div class="action-icon">📷</div>
        <div class="action-text">扫码添加</div>
      </div>
      
      <div class="action-card" @click="$emit('check-expiry')">
        <div class="action-icon">⏰</div>
        <div class="action-text">过期检查</div>
      </div>
      
      <div class="action-card" @click="$emit('stock-alert')">
        <div class="action-icon">📊</div>
        <div class="action-text">库存预警</div>
      </div>
    </div>

    <!-- 药品分类快速筛选 -->
    <div class="category-filters">
      <div class="filter-title">药品分类</div>
      <div class="filter-buttons">
        <el-button
          v-for="category in categories"
          :key="category.value"
          :type="selectedCategory === category.value ? 'primary' : ''"
          size="small"
          @click="selectCategory(category.value)"
        >
          {{ category.icon }} {{ category.label }}
        </el-button>
      </div>
    </div>

    <!-- 药品状态筛选 -->
    <div class="status-filters">
      <div class="filter-title">药品状态</div>
      <div class="filter-buttons">
        <el-button
          v-for="status in statuses"
          :key="status.value"
          :type="selectedStatus === status.value ? 'primary' : ''"
          size="small"
          @click="selectStatus(status.value)"
        >
          {{ status.icon }} {{ status.label }}
        </el-button>
      </div>
    </div>

    <!-- 药品提醒 -->
    <div class="medicine-alerts">
      <div class="alert-title">药品提醒</div>
      
      <div v-if="expiringMedicines.length > 0" class="alert-item expiring">
        <div class="alert-icon">⚠️</div>
        <div class="alert-content">
          <div class="alert-text">{{ expiringMedicines.length }} 种药品即将过期</div>
          <div class="alert-detail">请及时使用或更换</div>
        </div>
        <el-button size="small" @click="$emit('view-expiring')">查看</el-button>
      </div>

      <div v-if="lowStockMedicines.length > 0" class="alert-item low-stock">
        <div class="alert-icon">📉</div>
        <div class="alert-content">
          <div class="alert-text">{{ lowStockMedicines.length }} 种药品库存不足</div>
          <div class="alert-detail">建议及时补充</div>
        </div>
        <el-button size="small" @click="$emit('view-low-stock')">查看</el-button>
      </div>

      <div v-if="expiredMedicines.length > 0" class="alert-item expired">
        <div class="alert-icon">❌</div>
        <div class="alert-content">
          <div class="alert-text">{{ expiredMedicines.length }} 种药品已过期</div>
          <div class="alert-detail">请立即处理</div>
        </div>
        <el-button size="small" type="danger" @click="$emit('view-expired')">处理</el-button>
      </div>

      <div v-if="!hasAlerts" class="no-alerts">
        <div class="no-alerts-icon">✅</div>
        <div class="no-alerts-text">药箱状态良好</div>
      </div>
    </div>

    <!-- 最近操作 -->
    <div class="recent-operations">
      <div class="operation-title">最近操作</div>
      <div class="operation-list">
        <div
          v-for="operation in recentOperations"
          :key="operation.id"
          class="operation-item"
        >
          <div class="operation-icon">{{ operation.icon }}</div>
          <div class="operation-content">
            <div class="operation-text">{{ operation.text }}</div>
            <div class="operation-time">{{ formatTime(operation.time) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 药箱统计图表 -->
    <div class="medicine-charts">
      <div class="chart-title">药箱统计</div>
      
      <!-- 分类统计 -->
      <div class="chart-section">
        <h4>药品分类分布</h4>
        <div class="category-stats">
          <div
            v-for="(count, category) in categoryStats"
            :key="category"
            class="category-stat"
          >
            <div class="stat-label">{{ getCategoryLabel(category) }}</div>
            <div class="stat-bar">
              <div
                class="stat-fill"
                :style="{ width: `${(count / totalMedicines) * 100}%` }"
              ></div>
            </div>
            <div class="stat-count">{{ count }}</div>
          </div>
        </div>
      </div>

      <!-- 状态统计 -->
      <div class="chart-section">
        <h4>药品状态分布</h4>
        <div class="status-stats">
          <div
            v-for="(count, status) in statusStats"
            :key="status"
            class="status-stat"
            :class="`status-${status}`"
          >
            <div class="stat-label">{{ getStatusLabel(status) }}</div>
            <div class="stat-count">{{ count }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Medicine } from '@/services/medicineBoxService'

interface Props {
  medicines: Medicine[]
  type: 'personal' | 'family'
}

interface Emits {
  (e: 'add-medicine'): void
  (e: 'scan-medicine'): void
  (e: 'check-expiry'): void
  (e: 'stock-alert'): void
  (e: 'view-expiring'): void
  (e: 'view-low-stock'): void
  (e: 'view-expired'): void
  (e: 'category-changed', category: string): void
  (e: 'status-changed', status: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedCategory = ref('')
const selectedStatus = ref('')

// 药品分类
const categories = [
  { value: '', label: '全部', icon: '📋' },
  { value: 'prescription', label: '处方药', icon: '💊' },
  { value: 'otc', label: '非处方药', icon: '🏥' },
  { value: 'test', label: '检验试剂', icon: '🧪' },
  { value: 'supplement', label: '保健品', icon: '🌿' }
]

// 药品状态
const statuses = [
  { value: '', label: '全部', icon: '📊' },
  { value: 'normal', label: '正常', icon: '✅' },
  { value: 'low', label: '库存不足', icon: '📉' },
  { value: 'expiring', label: '即将过期', icon: '⚠️' },
  { value: 'expired', label: '已过期', icon: '❌' }
]

// 最近操作
const recentOperations = ref([
  { id: 1, icon: '➕', text: '添加了阿司匹林肠溶片', time: new Date(Date.now() - 1000 * 60 * 30) },
  { id: 2, icon: '📝', text: '更新了维生素D3库存', time: new Date(Date.now() - 1000 * 60 * 60 * 2) },
  { id: 3, icon: '🗑️', text: '移除了过期感冒药', time: new Date(Date.now() - 1000 * 60 * 60 * 24) }
])

// 计算属性
const totalMedicines = computed(() => props.medicines.length)

const expiringMedicines = computed(() => 
  props.medicines.filter(m => m.status === 'expiring')
)

const lowStockMedicines = computed(() => 
  props.medicines.filter(m => m.status === 'low')
)

const expiredMedicines = computed(() => 
  props.medicines.filter(m => m.status === 'expired')
)

const hasAlerts = computed(() => 
  expiringMedicines.value.length > 0 || 
  lowStockMedicines.value.length > 0 || 
  expiredMedicines.value.length > 0
)

const categoryStats = computed(() => {
  return props.medicines.reduce((acc, medicine) => {
    acc[medicine.category] = (acc[medicine.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)
})

const statusStats = computed(() => {
  return props.medicines.reduce((acc, medicine) => {
    acc[medicine.status] = (acc[medicine.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)
})

// 方法
const selectCategory = (category: string) => {
  selectedCategory.value = category
  emit('category-changed', category)
}

const selectStatus = (status: string) => {
  selectedStatus.value = status
  emit('status-changed', status)
}

const getCategoryLabel = (category: string) => {
  const categoryMap: Record<string, string> = {
    prescription: '处方药',
    otc: '非处方药',
    test: '检验试剂',
    supplement: '保健品'
  }
  return categoryMap[category] || category
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: '正常',
    low: '库存不足',
    expiring: '即将过期',
    expired: '已过期'
  }
  return statusMap[status] || status
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}
</script>

<style scoped>
.medicine-box-manager {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--theme-border-light);
  cursor: pointer;
  transition: all 0.3s;
}

.action-card:hover {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.action-text {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.category-filters,
.status-filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.medicine-alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--theme-border-light);
}

.alert-item.expiring {
  background: var(--color-warning-light);
  border-color: var(--color-warning);
}

.alert-item.low-stock {
  background: var(--color-info-light);
  border-color: var(--color-info);
}

.alert-item.expired {
  background: var(--color-danger-light);
  border-color: var(--color-danger);
}

.alert-icon {
  font-size: 20px;
}

.alert-content {
  flex: 1;
}

.alert-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-primary);
}

.alert-detail {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  text-align: center;
}

.no-alerts-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.no-alerts-text {
  font-size: 14px;
  color: var(--theme-text-secondary);
}

.recent-operations {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.operation-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.operation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-sm);
}

.operation-icon {
  font-size: 16px;
}

.operation-content {
  flex: 1;
}

.operation-text {
  font-size: 14px;
  color: var(--theme-text-primary);
}

.operation-time {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.medicine-charts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.chart-section h4 {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin: 0 0 12px 0;
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-stat {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-label {
  font-size: 12px;
  color: var(--theme-text-secondary);
  min-width: 60px;
}

.stat-bar {
  flex: 1;
  height: 8px;
  background: var(--theme-border-light);
  border-radius: 4px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s;
}

.stat-count {
  font-size: 12px;
  font-weight: 500;
  color: var(--theme-text-primary);
  min-width: 20px;
  text-align: right;
}

.status-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.status-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--theme-border-light);
}

.status-stat.status-normal {
  background: var(--color-success-light);
}

.status-stat.status-low {
  background: var(--color-warning-light);
}

.status-stat.status-expiring {
  background: var(--color-danger-light);
}

.status-stat.status-expired {
  background: var(--color-danger-light);
}
</style>
