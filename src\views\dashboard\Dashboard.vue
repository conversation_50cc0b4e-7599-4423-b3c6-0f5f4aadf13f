<template>
  <PageContainer
    title="首页"
    description="健康管理 - 守护您和家人的健康每一天"
    :show-header="false"
  >
    <!-- 自定义页面头部 -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1 class="welcome-title">欢迎回来，{{ userInfo.username }}！</h1>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，让我们一起关注您的健康状况
        </p>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary">
          <span>快速操作</span>
        </button>
        <button class="btn btn-secondary">
          <span>通知</span>
        </button>
      </div>
    </div>

    <!-- 紧急警报区域 -->
    <ContentCard
      v-if="emergencyAlerts.length > 0"
      title="紧急警报"
      variant="bordered"
      class="emergency-section"
    >
      <div class="alerts-list">
        <div
          v-for="alert in emergencyAlerts"
          :key="alert.id"
          class="alert-item"
          :class="`alert-${alert.type}`"
        >
          <div class="alert-icon">
            <component :is="getAlertIcon(alert.type)" />
          </div>
          <div class="alert-content">
            <h4 class="alert-title">{{ alert.title }}</h4>
            <p class="alert-message">{{ alert.message }}</p>
            <span class="alert-time">{{ formatTime(alert.time) }}</span>
          </div>
          <button class="alert-action btn btn-sm btn-primary">查看详情</button>
        </div>
      </div>
    </ContentCard>

    <!-- 快速操作卡片 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="quick-actions-grid">
        <ContentCard
          v-for="action in quickActions"
          :key="action.id"
          :title="action.title"
          :subtitle="action.subtitle"
          :icon="action.icon"
          :icon-color="action.iconColor"
          :icon-bg-color="action.iconBgColor"
          hoverable
          size="sm"
          class="quick-action-card"
          @click="handleQuickAction(action)"
        >
          <p class="action-description">{{ action.description }}</p>
        </ContentCard>
      </div>
    </div>

    <!-- 今日健康任务 -->
    <div class="tasks-section">
      <h2 class="section-title">今日健康任务</h2>
      <ContentCard
        title="待办任务"
        :subtitle="`共 ${todayTasks.length} 项任务`"
      >
        <div v-if="todayTasks.length === 0" class="empty-state">
          <p>今天没有待办任务，您可以休息一下 😊</p>
        </div>
        <div v-else class="tasks-list">
          <div
            v-for="task in todayTasks"
            :key="task.id"
            class="task-item"
            :class="{ completed: task.completed }"
          >
            <div class="task-checkbox">
              <input
                type="checkbox"
                :checked="task.completed"
                @change="toggleTask(task)"
              />
            </div>
            <div class="task-icon">
              <component :is="getTaskIcon(task.type)" />
            </div>
            <div class="task-content">
              <h4 class="task-title">{{ task.title }}</h4>
              <p class="task-time">{{ task.time }}</p>
            </div>
            <span class="task-priority" :class="`priority-${task.priority}`">
              {{ getPriorityText(task.priority) }}
            </span>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 家庭健康状态概览 -->
    <div class="family-overview-section">
      <h2 class="section-title">家庭健康状态</h2>
      <div class="family-stats-grid">
        <StatCard
          v-for="member in familyMembers"
          :key="member.id"
          :title="member.name"
          :value="member.healthScore"
          unit="分"
          :status="member.healthStatus"
          :trend="member.trend"
          :icon="member.avatar"
          size="md"
        />
      </div>
    </div>

    <!-- 健康趋势图表 -->
    <div class="trends-section">
      <h2 class="section-title">本周健康趋势</h2>
      <div class="charts-grid">
        <ContentCard title="血压趋势" subtitle="过去7天的血压变化">
          <LineChart
            :data="bloodPressureData"
            title=""
            x-axis-label="日期"
            y-axis-label="血压 (mmHg)"
            height="300px"
            :smooth="true"
            :show-area="true"
            color="#EF4444"
          />
        </ContentCard>

        <ContentCard title="活动分布" subtitle="本周健康活动类型分布">
          <PieChart
            :data="activityDistribution"
            title=""
            height="300px"
            :show-legend="true"
          />
        </ContentCard>
      </div>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

const router = useRouter();

// 用户信息
const userInfo = ref({
  username: "张先生",
  avatar: "/api/placeholder/40/40",
});

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
});

// 紧急警报
const emergencyAlerts = ref([
  {
    id: 1,
    type: "warning",
    title: "血压异常提醒",
    message: "张奶奶的血压读数偏高，建议及时就医",
    time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
  },
]);

// 快速操作
const quickActions = ref([
  {
    id: 1,
    title: "健康档案",
    subtitle: "管理健康信息",
    description: "查看和管理家庭成员的健康档案",
    icon: "User",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
    path: "/archive",
  },
  {
    id: 2,
    title: "家庭管理",
    subtitle: "成员管理",
    description: "添加家庭成员，管理家庭信息",
    icon: "UserFilled",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
    path: "/family",
  },
  {
    id: 3,
    title: "AI助手",
    subtitle: "智能咨询",
    description: "获取个性化健康建议和分析",
    icon: "ChatDotRound",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
    path: "/assistant",
  },
  {
    id: 4,
    title: "健康日历",
    subtitle: "任务管理",
    description: "查看和管理健康任务提醒",
    icon: "Calendar",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
    path: "/calendar",
  },
]);

// 今日任务
const todayTasks = ref([
  {
    id: 1,
    title: "服用降压药",
    time: "08:00",
    type: "medication",
    priority: "high",
    completed: true,
  },
  {
    id: 2,
    title: "血糖检测",
    time: "12:00",
    type: "checkup",
    priority: "medium",
    completed: false,
  },
  {
    id: 3,
    title: "晚间散步",
    time: "19:00",
    type: "exercise",
    priority: "low",
    completed: false,
  },
]);

// 家庭成员健康状态
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    healthScore: 85,
    healthStatus: "normal",
    trend: { direction: "up", value: 5, period: "本周" },
    avatar: "Male",
  },
  {
    id: 2,
    name: "李女士",
    healthScore: 92,
    healthStatus: "success",
    trend: { direction: "stable", value: 0, period: "本周" },
    avatar: "Female",
  },
  {
    id: 3,
    name: "张奶奶",
    healthScore: 68,
    healthStatus: "warning",
    trend: { direction: "down", value: 8, period: "本周" },
    avatar: "Female",
  },
]);

// 获取警报图标
const getAlertIcon = (type: string) => {
  switch (type) {
    case "warning":
      return "Warning";
    case "danger":
      return "CircleClose";
    case "info":
      return "InfoFilled";
    default:
      return "Warning";
  }
};

// 获取任务图标
const getTaskIcon = (type: string) => {
  switch (type) {
    case "medication":
      return "Medicine";
    case "exercise":
      return "Trophy";
    case "checkup":
      return "Monitor";
    case "appointment":
      return "Calendar";
    default:
      return "Clock";
  }
};

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const minutes = Math.floor(diff / (1000 * 60));

  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (minutes < 1440) {
    return `${Math.floor(minutes / 60)}小时前`;
  } else {
    return time.toLocaleDateString();
  }
};

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case "high":
      return "高";
    case "medium":
      return "中";
    case "low":
      return "低";
    default:
      return "中";
  }
};

// 处理快速操作点击
const handleQuickAction = (action: any) => {
  router.push(action.path);
};

// 切换任务状态
const toggleTask = (task: any) => {
  task.completed = !task.completed;
};

// 血压趋势数据
const bloodPressureData = ref([
  { name: "周一", value: 125, date: "2024-01-15" },
  { name: "周二", value: 128, date: "2024-01-16" },
  { name: "周三", value: 122, date: "2024-01-17" },
  { name: "周四", value: 120, date: "2024-01-18" },
  { name: "周五", value: 118, date: "2024-01-19" },
  { name: "周六", value: 115, date: "2024-01-20" },
  { name: "周日", value: 117, date: "2024-01-21" },
]);

// 活动分布数据
const activityDistribution = ref([
  { name: "用药管理", value: 35, color: "#3B82F6" },
  { name: "运动锻炼", value: 25, color: "#10B981" },
  { name: "健康检查", value: 20, color: "#F59E0B" },
  { name: "医疗预约", value: 15, color: "#EF4444" },
  { name: "其他", value: 5, color: "#8B5CF6" },
]);
</script>

<style scoped>
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-8);
  margin: 0 var(--spacing-6) var(--spacing-6) var(--spacing-6);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.welcome-section {
  flex: 1;
}

.welcome-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.welcome-subtitle {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
}

/* 主要内容区域的统一边距 */
.emergency-section,
.quick-actions-section,
.tasks-section,
.family-section,
.trends-section {
  margin: 0 var(--spacing-6) var(--spacing-6) var(--spacing-6);
}

.emergency-section {
  margin-bottom: var(--spacing-6);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  border-left: 4px solid;
}

.alert-warning {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
}

.alert-danger {
  background-color: var(--color-danger-light);
  border-color: var(--color-danger);
}

.alert-icon {
  width: 24px;
  height: 24px;
  color: var(--color-warning);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.alert-message {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-1) 0;
}

.alert-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
}

.quick-action-card {
  cursor: pointer;
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.task-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.task-item:hover {
  background-color: var(--theme-bg-secondary);
}

.task-item.completed {
  opacity: 0.6;
}

.task-checkbox input {
  width: 18px;
  height: 18px;
}

.task-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.task-time {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.task-priority {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.priority-high {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.priority-medium {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.priority-low {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.family-overview-section {
  margin-bottom: var(--spacing-8);
}

.family-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
}

.chart-placeholder {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    margin: 0 var(--spacing-4) var(--spacing-6) var(--spacing-4);
  }

  .emergency-section,
  .quick-actions-section,
  .tasks-section,
  .family-section,
  .trends-section {
    margin: 0 var(--spacing-4) var(--spacing-6) var(--spacing-4);
  }

  .header-actions {
    justify-content: center;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .family-stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
