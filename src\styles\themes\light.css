/* 浅色主题样式 */

:root {
  /* 主题背景色 */
  --theme-bg-primary: var(--color-white);
  --theme-bg-secondary: var(--color-gray-50);
  --theme-bg-tertiary: var(--color-gray-100);
  
  /* 主题文字色 */
  --theme-text-primary: var(--color-gray-900);
  --theme-text-secondary: var(--color-gray-600);
  --theme-text-tertiary: var(--color-gray-500);
  --theme-text-inverse: var(--color-white);
  
  /* 主题边框色 */
  --theme-border: var(--color-gray-200);
  --theme-border-light: var(--color-gray-100);
  --theme-border-dark: var(--color-gray-300);
  
  /* 主题阴影 */
  --theme-shadow-sm: var(--shadow-sm);
  --theme-shadow-md: var(--shadow-md);
  --theme-shadow-lg: var(--shadow-lg);
  
  /* 侧边栏主题 */
  --sidebar-bg: var(--color-gray-50);
  --sidebar-text: var(--color-gray-700);
  --sidebar-text-active: var(--color-primary);
  --sidebar-border: var(--color-gray-200);
  
  /* 头部主题 */
  --header-bg: var(--color-white);
  --header-text: var(--color-gray-900);
  --header-border: var(--color-gray-200);
  
  /* 卡片主题 */
  --card-bg: var(--color-white);
  --card-border: var(--color-gray-200);
  --card-shadow: var(--shadow-md);
  
  /* 表单主题 */
  --input-bg: var(--color-white);
  --input-border: var(--color-gray-300);
  --input-border-focus: var(--color-primary);
  --input-text: var(--color-gray-900);
  --input-placeholder: var(--color-gray-500);
  
  /* 按钮主题 */
  --btn-primary-bg: var(--color-primary);
  --btn-primary-text: var(--color-white);
  --btn-primary-border: var(--color-primary);
  --btn-primary-hover-bg: var(--color-primary-dark);
  
  --btn-secondary-bg: var(--color-gray-200);
  --btn-secondary-text: var(--color-gray-700);
  --btn-secondary-border: var(--color-gray-200);
  --btn-secondary-hover-bg: var(--color-gray-300);
  
  /* 状态色主题 */
  --status-success-bg: var(--color-success-light);
  --status-success-text: var(--color-success-dark);
  --status-success-border: var(--color-success);
  
  --status-warning-bg: var(--color-warning-light);
  --status-warning-text: var(--color-warning-dark);
  --status-warning-border: var(--color-warning);
  
  --status-danger-bg: var(--color-danger-light);
  --status-danger-text: var(--color-danger-dark);
  --status-danger-border: var(--color-danger);
  
  --status-info-bg: var(--color-info-light);
  --status-info-text: var(--color-info-dark);
  --status-info-border: var(--color-info);
}

/* 浅色主题特定样式 */
.light-theme {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

/* 滚动条浅色主题 */
.light-theme ::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

.light-theme ::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
}

.light-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* 选择文本浅色主题 */
.light-theme ::selection {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

/* 焦点样式浅色主题 */
.light-theme .focus-visible {
  outline-color: var(--color-primary);
}

/* 链接样式浅色主题 */
.light-theme a {
  color: var(--color-primary);
}

.light-theme a:hover {
  color: var(--color-primary-dark);
}

/* 代码块样式浅色主题 */
.light-theme code {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.875em;
}

.light-theme pre {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  overflow-x: auto;
}

.light-theme pre code {
  background: none;
  padding: 0;
}

/* 表格样式浅色主题 */
.light-theme table {
  border-collapse: collapse;
  width: 100%;
}

.light-theme th,
.light-theme td {
  border: 1px solid var(--theme-border);
  padding: var(--spacing-3);
  text-align: left;
}

.light-theme th {
  background-color: var(--theme-bg-secondary);
  font-weight: var(--font-weight-semibold);
}

.light-theme tr:nth-child(even) {
  background-color: var(--theme-bg-secondary);
}

/* 引用样式浅色主题 */
.light-theme blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--spacing-4);
  margin: var(--spacing-4) 0;
  color: var(--theme-text-secondary);
  font-style: italic;
}

/* 分割线样式浅色主题 */
.light-theme hr {
  border: none;
  height: 1px;
  background-color: var(--theme-border);
  margin: var(--spacing-6) 0;
}

/* 列表样式浅色主题 */
.light-theme ul,
.light-theme ol {
  padding-left: var(--spacing-6);
  margin: var(--spacing-4) 0;
}

.light-theme li {
  margin-bottom: var(--spacing-2);
}

/* 标题样式浅色主题 */
.light-theme h1,
.light-theme h2,
.light-theme h3,
.light-theme h4,
.light-theme h5,
.light-theme h6 {
  color: var(--theme-text-primary);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
}

.light-theme h1 { font-size: var(--font-size-3xl); }
.light-theme h2 { font-size: var(--font-size-2xl); }
.light-theme h3 { font-size: var(--font-size-xl); }
.light-theme h4 { font-size: var(--font-size-lg); }
.light-theme h5 { font-size: var(--font-size-base); }
.light-theme h6 { font-size: var(--font-size-sm); }

/* 段落样式浅色主题 */
.light-theme p {
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
}

/* 图片样式浅色主题 */
.light-theme img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
}

/* 表单元素浅色主题增强 */
.light-theme .form-input:focus {
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.light-theme .form-input::placeholder {
  color: var(--input-placeholder);
}

/* 按钮浅色主题增强 */
.light-theme .btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  border-color: var(--btn-primary-border);
}

.light-theme .btn-primary:hover {
  background-color: var(--btn-primary-hover-bg);
}

.light-theme .btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border-color: var(--btn-secondary-border);
}

.light-theme .btn-secondary:hover {
  background-color: var(--btn-secondary-hover-bg);
}
