<template>
  <el-dialog
    v-model="visible"
    title="上传健康报告"
    width="600px"
    :before-close="handleClose"
    center
    :lock-scroll="true"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="upload-content">
      <!-- 上传区域 -->
      <div class="upload-section">
        <h4 class="section-title">选择报告文件</h4>
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          accept=".pdf,.jpg,.jpeg,.png"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、JPG、PNG 格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>

        <!-- 文件预览 -->
        <div v-if="selectedFile" class="file-preview">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <div class="file-details">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-size">
                {{ formatFileSize(selectedFile.size) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 报告信息 -->
      <div class="report-info-section">
        <h4 class="section-title">报告信息</h4>
        <el-form
          ref="formRef"
          :model="reportForm"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="报告类型" prop="type">
            <el-select v-model="reportForm.type" placeholder="选择报告类型">
              <el-option label="体检报告" value="checkup" />
              <el-option label="化验报告" value="lab" />
              <el-option label="影像报告" value="imaging" />
              <el-option label="处方单" value="prescription" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item label="报告名称" prop="name">
            <el-input
              v-model="reportForm.name"
              placeholder="请输入报告名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="报告日期" prop="reportDate">
            <el-date-picker
              v-model="reportForm.reportDate"
              type="date"
              placeholder="选择报告日期"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="所属成员" prop="memberId">
            <el-select v-model="reportForm.memberId" placeholder="选择家庭成员">
              <el-option
                v-for="member in familyMembers"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="医院名称">
            <el-input
              v-model="reportForm.hospitalName"
              placeholder="请输入医院名称（可选）"
            />
          </el-form-item>

          <el-form-item label="医生姓名">
            <el-input
              v-model="reportForm.doctorName"
              placeholder="请输入医生姓名（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- OCR设置 -->
      <div class="ocr-settings-section">
        <h4 class="section-title">OCR设置</h4>
        <div class="ocr-options">
          <el-checkbox v-model="ocrSettings.autoExtract">
            自动提取数据
          </el-checkbox>
          <el-checkbox v-model="ocrSettings.enableReview">
            启用人工审核
          </el-checkbox>
          <el-checkbox v-model="ocrSettings.saveOriginal">
            保存原始文件
          </el-checkbox>
        </div>
        <div class="ocr-note">
          <el-icon><InfoFilled /></el-icon>
          <span>OCR识别可能需要几分钟时间，请耐心等待</span>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <h4 class="section-title">上传进度</h4>
        <div class="progress-info">
          <div class="progress-text">
            {{
              uploadStatus === "uploading"
                ? "正在上传文件..."
                : uploadStatus === "processing"
                ? "正在进行OCR识别..."
                : uploadStatus === "completed"
                ? "处理完成"
                : "处理失败"
            }}
          </div>
          <el-progress
            :percentage="uploadProgress"
            :status="
              uploadStatus === 'failed'
                ? 'exception'
                : uploadStatus === 'completed'
                ? 'success'
                : undefined
            "
          />
        </div>

        <!-- OCR进度详情 -->
        <div v-if="uploadStatus === 'processing'" class="ocr-progress">
          <div class="ocr-steps">
            <div
              class="step"
              :class="{ active: ocrStep >= 1, completed: ocrStep > 1 }"
            >
              <el-icon><Document /></el-icon>
              <span>文件解析</span>
            </div>
            <div
              class="step"
              :class="{ active: ocrStep >= 2, completed: ocrStep > 2 }"
            >
              <el-icon><View /></el-icon>
              <span>图像识别</span>
            </div>
            <div
              class="step"
              :class="{ active: ocrStep >= 3, completed: ocrStep > 3 }"
            >
              <el-icon><EditPen /></el-icon>
              <span>数据提取</span>
            </div>
            <div
              class="step"
              :class="{ active: ocrStep >= 4, completed: ocrStep > 4 }"
            >
              <el-icon><Check /></el-icon>
              <span>完成处理</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理结果 -->
      <div v-if="uploadResult" class="upload-result">
        <el-result
          :icon="uploadResult.success ? 'success' : 'error'"
          :title="uploadResult.success ? '上传成功' : '上传失败'"
          :sub-title="uploadResult.message"
        >
          <template #extra>
            <el-button
              v-if="uploadResult.success"
              type="primary"
              @click="viewReport"
            >
              查看报告
            </el-button>
            <el-button
              v-if="!uploadResult.success"
              type="primary"
              @click="retryUpload"
            >
              重新上传
            </el-button>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="uploading">
          {{ uploading ? "处理中..." : "取消" }}
        </el-button>
        <el-button
          type="primary"
          @click="handleUpload"
          :loading="uploading"
          :disabled="!selectedFile || uploading"
        >
          {{ uploading ? "处理中..." : "开始上传" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  UploadFilled,
  Document,
  InfoFilled,
  View,
  EditPen,
  Check,
} from "@element-plus/icons-vue";
import type {
  FormInstance,
  FormRules,
  UploadFile,
  UploadInstance,
} from "element-plus";
import { healthArchiveService } from "@/services/healthArchiveService";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success", reportId: string): void;
  (e: "viewReport", reportId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref<"uploading" | "processing" | "completed" | "failed">(
  "uploading"
);
const ocrStep = ref(0);
const selectedFile = ref<File | null>(null);
const uploadResult = ref<{
  success: boolean;
  message: string;
  reportId?: string;
} | null>(null);

const uploadRef = ref<UploadInstance>();
const formRef = ref<FormInstance>();

// 表单数据
const reportForm = reactive({
  type: "",
  name: "",
  reportDate: null as Date | null,
  memberId: "",
  hospitalName: "",
  doctorName: "",
});

// OCR设置
const ocrSettings = reactive({
  autoExtract: true,
  enableReview: true,
  saveOriginal: true,
});

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 表单验证规则
const formRules: FormRules = {
  type: [{ required: true, message: "请选择报告类型", trigger: "change" }],
  name: [{ required: true, message: "请输入报告名称", trigger: "blur" }],
  reportDate: [
    { required: true, message: "请选择报告日期", trigger: "change" },
  ],
  memberId: [{ required: true, message: "请选择所属成员", trigger: "change" }],
};

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
  if (!val) {
    resetForm();
  }
});

const resetForm = () => {
  selectedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  uploadStatus.value = "uploading";
  ocrStep.value = 0;
  uploadResult.value = null;

  Object.assign(reportForm, {
    type: "",
    name: "",
    reportDate: null,
    memberId: "",
    hospitalName: "",
    doctorName: "",
  });

  uploadRef.value?.clearFiles();
};

const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null;

  // 自动填充报告名称
  if (file.name && !reportForm.name) {
    reportForm.name = file.name.replace(/\.[^/.]+$/, "");
  }
};

const handleFileRemove = () => {
  selectedFile.value = null;
};

const beforeUpload = (file: File) => {
  const isValidType = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/jpg",
  ].includes(file.type);
  const isValidSize = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    ElMessage.error("只支持 PDF、JPG、PNG 格式的文件");
    return false;
  }
  if (!isValidSize) {
    ElMessage.error("文件大小不能超过 10MB");
    return false;
  }
  return false; // 阻止自动上传
};

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / 1024 / 1024).toFixed(1)} MB`;
};

const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning("请选择要上传的文件");
    return;
  }

  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    uploading.value = true;
    uploadProgress.value = 0;
    uploadStatus.value = "uploading";
    uploadResult.value = null;

    // 模拟上传进度
    const uploadInterval = setInterval(() => {
      if (uploadProgress.value < 30) {
        uploadProgress.value += 5;
      }
    }, 200);

    // 调用上传API
    const result = await healthArchiveService.createHealthReport(
      selectedFile.value,
      reportForm.memberId,
      reportForm.type
    );

    clearInterval(uploadInterval);
    uploadProgress.value = 30;
    uploadStatus.value = "processing";

    // 模拟OCR处理进度
    await simulateOCRProgress();

    uploadResult.value = {
      success: true,
      message: "报告上传成功，OCR识别已完成",
      reportId: result.reportId,
    };

    ElMessage.success("健康报告上传成功！");
    emit("success", result.reportId);
  } catch (error) {
    console.error("上传失败:", error);
    uploadResult.value = {
      success: false,
      message: "上传失败，请重试",
    };
    uploadStatus.value = "failed";
    ElMessage.error("上传失败，请重试");
  } finally {
    uploading.value = false;
  }
};

const simulateOCRProgress = async () => {
  const steps = [
    { step: 1, progress: 50, delay: 1000 },
    { step: 2, progress: 70, delay: 1500 },
    { step: 3, progress: 90, delay: 1000 },
    { step: 4, progress: 100, delay: 500 },
  ];

  for (const { step, progress, delay } of steps) {
    await new Promise((resolve) => setTimeout(resolve, delay));
    ocrStep.value = step;
    uploadProgress.value = progress;
  }

  uploadStatus.value = "completed";
};

const viewReport = () => {
  if (uploadResult.value?.reportId) {
    // 触发查看报告事件，由父组件处理
    emit("viewReport", uploadResult.value.reportId);
    visible.value = false;
  }
};

const retryUpload = () => {
  uploadResult.value = null;
  uploadProgress.value = 0;
  uploadStatus.value = "uploading";
  ocrStep.value = 0;
};

const handleClose = () => {
  if (uploading.value) {
    ElMessage.warning("正在处理中，请稍候...");
    return;
  }
  visible.value = false;
};
</script>

<style scoped>
.upload-content {
  max-height: 600px;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.upload-section {
  margin-bottom: 24px;
}

.upload-dragger {
  width: 100%;
}

.file-preview {
  margin-top: 16px;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.file-size {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.report-info-section,
.ocr-settings-section {
  margin-bottom: 24px;
}

.ocr-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.ocr-note {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.upload-progress {
  margin-bottom: 24px;
}

.progress-info {
  margin-bottom: 16px;
}

.progress-text {
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--theme-text-primary);
}

.ocr-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: var(--border-radius-md);
  transition: all 0.3s;
  color: var(--theme-text-secondary);
}

.step.active {
  color: var(--color-primary);
  background: var(--color-primary-light);
}

.step.completed {
  color: var(--color-success);
  background: var(--color-success-light);
}

.step span {
  font-size: 12px;
  text-align: center;
}

.upload-result {
  margin-bottom: 24px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
