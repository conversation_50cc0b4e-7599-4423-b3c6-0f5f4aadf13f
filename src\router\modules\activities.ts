import type { RouteRecordRaw } from 'vue-router';

// 活动模块路由
export const activitiesRoutes: RouteRecordRaw[] = [
  {
    path: '/activities',
    name: 'Activities',
    component: () => import('@/views/activities/Activities.vue'),
    meta: {
      title: '活动 - 家庭健康管理系统',
      icon: 'Trophy',
      order: 6
    }
  },
  {
    path: '/activities/activity-list',
    name: 'ActivityList',
    component: () => import('@/views/activities/ActivityList.vue'),
    meta: {
      title: '活动列表 - 家庭健康管理系统',
      parent: 'Activities',
      breadcrumb: [
        { title: '活动', path: '/activities' },
        { title: '活动列表' }
      ]
    }
  },
  {
    path: '/activities/activity-timeline',
    name: 'ActivityTimeline',
    component: () => import('@/views/activities/ActivityTimeline.vue'),
    meta: {
      title: '活动时间线 - 家庭健康管理系统',
      parent: 'Activities',
      breadcrumb: [
        { title: '活动', path: '/activities' },
        { title: '活动时间线' }
      ]
    }
  },
  {
    path: '/activities/activity-analysis',
    name: 'ActivityAnalysis',
    component: () => import('@/views/activities/ActivityAnalysis.vue'),
    meta: {
      title: '活动数据分析 - 家庭健康管理系统',
      parent: 'Activities',
      breadcrumb: [
        { title: '活动', path: '/activities' },
        { title: '活动数据分析' }
      ]
    }
  }
];
