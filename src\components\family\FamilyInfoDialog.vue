<template>
  <el-dialog
    v-model="visible"
    title="家庭详细信息"
    width="600px"
    :before-close="handleClose"
  >
    <div v-if="familyInfo" class="family-info-content">
      <!-- 家庭基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>家庭名称：</label>
            <span>{{ familyInfo.name }}</span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(familyInfo.createdAt) }}</span>
          </div>
          <div class="info-item">
            <label>成员数量：</label>
            <span>{{ familyInfo.members.length }} 人</span>
          </div>
          <div class="info-item">
            <label>家庭地址：</label>
            <span>{{ familyInfo.address || "未设置" }}</span>
          </div>
          <div class="info-item">
            <label>联系电话：</label>
            <span>{{ familyInfo.phone || "未设置" }}</span>
          </div>
          <div class="info-item">
            <label>邮政编码：</label>
            <span>{{ familyInfo.zipCode || "未设置" }}</span>
          </div>
          <div class="info-item full-width">
            <label>家庭描述：</label>
            <span>{{ familyInfo.description || "暂无描述" }}</span>
          </div>
        </div>
      </div>

      <!-- 成员列表 -->
      <div class="info-section">
        <h3 class="section-title">成员列表</h3>
        <div class="members-list">
          <div
            v-for="member in familyInfo.members"
            :key="member.id"
            class="member-item"
          >
            <div class="member-avatar">
              <el-avatar :size="40" :src="member.avatar">
                {{ member.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="member-info">
              <div class="member-name">
                {{ member.name }}
                <el-tag v-if="member.isAdmin" type="warning" size="small"
                  >管理员</el-tag
                >
              </div>
              <div class="member-details">
                <span class="member-role">{{ member.role }}</span>
                <span class="member-join-time"
                  >{{ formatDate(member.joinedAt) }} 加入</span
                >
              </div>
            </div>
            <div class="member-status">
              <el-tag
                :type="getHealthStatusType(member.healthStatus)"
                size="small"
              >
                {{ getHealthStatusText(member.healthStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 家庭设置 -->
      <div class="info-section">
        <h3 class="section-title">家庭设置</h3>
        <div class="settings-list">
          <div class="setting-item">
            <label>数据共享：</label>
            <span>{{
              familyInfo.settings?.dataSharing ? "已开启" : "已关闭"
            }}</span>
          </div>
          <div class="setting-item">
            <label>健康提醒：</label>
            <span>{{
              familyInfo.settings?.healthReminder ? "已开启" : "已关闭"
            }}</span>
          </div>
          <div class="setting-item">
            <label>紧急联系：</label>
            <span>{{
              familyInfo.settings?.emergencyContact ? "已设置" : "未设置"
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑家庭信息</el-button>
      </div>
    </template>

    <!-- 编辑对话框 -->
    <FamilyEditDialog
      v-model="showEditDialog"
      :family-id="familyId"
      @success="handleEditSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import FamilyEditDialog from "./FamilyEditDialog.vue";

interface Props {
  modelValue: boolean;
  familyId?: number | string;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "edit", familyId: number | string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const familyInfo = ref<any>(null);
const showEditDialog = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val && props.familyId) {
      loadFamilyInfo();
    }
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const loadFamilyInfo = async () => {
  // 模拟API调用获取家庭详细信息
  await new Promise((resolve) => setTimeout(resolve, 500));

  familyInfo.value = {
    id: props.familyId,
    name: "张家大院",
    description: "我们是一个温馨的大家庭，关爱彼此的健康",
    address: "北京市朝阳区某某街道123号",
    phone: "138-0000-0000",
    zipCode: "100000",
    createdAt: new Date("2024-01-15"),
    members: [
      {
        id: 1,
        name: "张先生",
        role: "父亲",
        isAdmin: true,
        joinedAt: new Date("2024-01-15"),
        healthStatus: "good",
        avatar: "",
      },
      {
        id: 2,
        name: "李女士",
        role: "母亲",
        isAdmin: true,
        joinedAt: new Date("2024-01-15"),
        healthStatus: "good",
        avatar: "",
      },
      {
        id: 3,
        name: "张小明",
        role: "儿子",
        isAdmin: false,
        joinedAt: new Date("2024-01-20"),
        healthStatus: "attention",
        avatar: "",
      },
    ],
    settings: {
      dataSharing: true,
      healthReminder: true,
      emergencyContact: true,
    },
  };
};

const handleClose = () => {
  visible.value = false;
};

const handleEdit = () => {
  showEditDialog.value = true;
};

const handleEditSuccess = () => {
  // 重新加载家庭信息
  loadFamilyInfo();
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  }).format(new Date(date));
};

const getHealthStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    good: "success",
    attention: "warning",
    risk: "danger",
  };
  return statusMap[status] || "info";
};

const getHealthStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    good: "健康",
    attention: "需关注",
    risk: "有风险",
  };
  return statusMap[status] || "未知";
};
</script>

<style scoped>
.family-info-content {
  max-height: 500px;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: var(--theme-text-secondary);
  margin-right: 8px;
  min-width: 80px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  gap: 12px;
}

.member-info {
  flex: 1;
}

.member-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.member-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item {
  display: flex;
  align-items: center;
}

.setting-item label {
  font-weight: 500;
  color: var(--theme-text-secondary);
  margin-right: 8px;
  min-width: 80px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
