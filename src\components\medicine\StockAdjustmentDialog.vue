<template>
  <el-dialog
    v-model="dialogVisible"
    title="调整库存"
    width="500px"
    :before-close="handleClose"
  >
    <div v-if="medicine" class="stock-adjustment">
      <!-- 药品信息 -->
      <div class="medicine-info">
        <h4>{{ medicine.name }}</h4>
        <p class="medicine-spec">{{ medicine.dosage }} | {{ medicine.manufacturer }}</p>
        <div class="current-stock">
          <span class="label">当前库存：</span>
          <span class="value" :class="{ 'low-stock': medicine.quantity <= medicine.minQuantity }">
            {{ medicine.quantity }}{{ medicine.unit }}
          </span>
        </div>
      </div>

      <!-- 调整表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent
      >
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio value="add">增加</el-radio>
            <el-radio value="subtract">减少</el-radio>
            <el-radio value="set">设置为</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="form.quantity"
            :min="form.type === 'set' ? 0 : 1"
            :max="form.type === 'subtract' ? medicine.quantity : undefined"
            :precision="0"
            style="width: 100%"
          />
          <div class="quantity-hint">
            <span v-if="form.type === 'add'">
              调整后库存：{{ medicine.quantity + (form.quantity || 0) }}{{ medicine.unit }}
            </span>
            <span v-else-if="form.type === 'subtract'">
              调整后库存：{{ Math.max(0, medicine.quantity - (form.quantity || 0)) }}{{ medicine.unit }}
            </span>
            <span v-else-if="form.type === 'set'">
              调整后库存：{{ form.quantity || 0 }}{{ medicine.unit }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="调整原因" prop="reason">
          <el-select v-model="form.reason" placeholder="请选择调整原因" style="width: 100%">
            <el-option label="购买补充" value="purchase" />
            <el-option label="使用消耗" value="consumption" />
            <el-option label="过期处理" value="expired" />
            <el-option label="损坏丢失" value="damaged" />
            <el-option label="盘点调整" value="inventory" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" v-if="form.reason === 'other'">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>

        <el-form-item label="调整日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择调整日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>

      <!-- 警告提示 -->
      <el-alert
        v-if="showLowStockWarning"
        title="库存不足警告"
        type="warning"
        :description="`调整后库存将低于最小库存（${medicine.minQuantity}${medicine.unit}），请及时补充。`"
        show-icon
        :closable="false"
        style="margin-top: 16px"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定调整
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { Medicine, StockAdjustment } from '@/types/medicine';
import type { FamilyMedicine } from '@/services/familyMedicineService';

interface Props {
  modelValue: boolean;
  medicine?: Medicine | FamilyMedicine | null;
  type?: 'personal' | 'family';
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success', adjustment: StockAdjustment): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'personal',
  medicine: null
});

const emit = defineEmits<Emits>();

const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

const form = reactive({
  type: 'add' as 'add' | 'subtract' | 'set',
  quantity: 1,
  reason: '',
  notes: '',
  date: new Date().toISOString().split('T')[0]
});

const rules: FormRules = {
  type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请选择调整原因', trigger: 'change' }
  ],
  date: [
    { required: true, message: '请选择调整日期', trigger: 'change' }
  ]
};

// 计算调整后是否会出现库存不足
const showLowStockWarning = computed(() => {
  if (!props.medicine) return false;
  
  let newQuantity = 0;
  switch (form.type) {
    case 'add':
      newQuantity = props.medicine.quantity + (form.quantity || 0);
      break;
    case 'subtract':
      newQuantity = Math.max(0, props.medicine.quantity - (form.quantity || 0));
      break;
    case 'set':
      newQuantity = form.quantity || 0;
      break;
  }
  
  return newQuantity < props.medicine.minQuantity;
});

const resetForm = () => {
  Object.assign(form, {
    type: 'add',
    quantity: 1,
    reason: '',
    notes: '',
    date: new Date().toISOString().split('T')[0]
  });
  formRef.value?.clearValidate();
};

const handleClose = () => {
  emit('update:modelValue', false);
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value || !props.medicine) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    // 构建调整数据
    const adjustment: StockAdjustment = {
      medicineId: props.medicine.id,
      type: form.type,
      quantity: form.quantity,
      reason: form.reason === 'other' ? form.notes : getReasonText(form.reason),
      date: form.date
    };
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    emit('success', adjustment);
    ElMessage.success('库存调整成功');
    handleClose();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

const getReasonText = (reason: string) => {
  const reasonMap: Record<string, string> = {
    purchase: '购买补充',
    consumption: '使用消耗',
    expired: '过期处理',
    damaged: '损坏丢失',
    inventory: '盘点调整'
  };
  return reasonMap[reason] || reason;
};

// 监听调整类型变化，重置数量验证规则
watch(() => form.type, (newType) => {
  if (newType === 'set') {
    rules.quantity = [
      { required: true, message: '请输入数量', trigger: 'blur' },
      { type: 'number', min: 0, message: '数量不能小于0', trigger: 'blur' }
    ];
  } else {
    rules.quantity = [
      { required: true, message: '请输入数量', trigger: 'blur' },
      { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
    ];
  }
  
  // 重新验证数量字段
  formRef.value?.validateField('quantity');
});

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
  if (val) {
    resetForm();
  }
});

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
});
</script>

<style scoped>
.stock-adjustment {
  padding: 8px 0;
}

.medicine-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.medicine-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.medicine-spec {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
}

.current-stock {
  display: flex;
  align-items: center;
}

.current-stock .label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.current-stock .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.low-stock {
  color: #f56c6c !important;
}

.quantity-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.dialog-footer {
  text-align: right;
}
</style>
