# 设计系统文档

## 1. 设计理念

### 1.1 核心原则
- **现代简洁**: 采用现代、简洁且专业的UI设计风格
- **健康导向**: 以健康为中心的色彩和视觉语言
- **用户友好**: 直观、易用的交互设计
- **一致性**: 统一的视觉规范和交互模式
- **可访问性**: 支持无障碍访问和多设备适配

### 1.2 设计目标
- 营造健康、安心、高效的视觉体验
- 提供清晰的信息层级和导航结构
- 确保在不同设备上的一致体验
- 支持用户的健康管理需求

## 2. 色彩系统

### 2.1 主色调
```css
/* 基础色彩 */
--color-white: #FFFFFF;
--color-gray-50: #F8FAFC;
--color-gray-100: #F1F5F9;
--color-gray-200: #E2E8F0;
--color-gray-300: #CBD5E1;
--color-gray-400: #94A3B8;
--color-gray-500: #64748B;
--color-gray-600: #475569;
--color-gray-700: #334155;
--color-gray-800: #1E293B;
--color-gray-900: #0F172A;
```

### 2.2 健康状态色彩
```css
/* 健康状态指示色 */
--color-success: #10B981;      /* 正常/健康 */
--color-success-light: #D1FAE5;
--color-success-dark: #047857;

--color-warning: #F59E0B;      /* 警告/注意 */
--color-warning-light: #FEF3C7;
--color-warning-dark: #D97706;

--color-danger: #EF4444;       /* 危险/异常 */
--color-danger-light: #FEE2E2;
--color-danger-dark: #DC2626;

--color-info: #3B82F6;         /* 信息/提示 */
--color-info-light: #DBEAFE;
--color-info-dark: #2563EB;
```

### 2.3 品牌色彩
```css
/* 品牌主色 */
--color-primary: #6366F1;      /* 主品牌色 */
--color-primary-light: #E0E7FF;
--color-primary-dark: #4F46E5;

/* 辅助色彩 */
--color-secondary: #8B5CF6;    /* 辅助色 */
--color-accent: #06B6D4;       /* 强调色 */
```

### 2.4 色彩使用规范
- **背景色**: 主要使用白色和浅灰色
- **文字色**: 使用灰色系，确保足够的对比度
- **状态色**: 用于健康数据、警告信息等状态指示
- **品牌色**: 用于按钮、链接、重要操作等

## 3. 字体系统

### 3.1 字体族
```css
--font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 
                    'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 
                    'Helvetica Neue', sans-serif;
--font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 
                    'Source Code Pro', monospace;
```

### 3.2 字体大小
```css
/* 字体大小规范 */
--font-size-xs: 12px;          /* 辅助文字 */
--font-size-sm: 14px;          /* 小号文字 */
--font-size-base: 16px;        /* 基础文字 */
--font-size-lg: 18px;          /* 大号文字 */
--font-size-xl: 20px;          /* 小标题 */
--font-size-2xl: 24px;         /* 中标题 */
--font-size-3xl: 30px;         /* 大标题 */
--font-size-4xl: 36px;         /* 特大标题 */
```

### 3.3 字重
```css
--font-weight-light: 300;      /* 细体 */
--font-weight-normal: 400;     /* 常规 */
--font-weight-medium: 500;     /* 中等 */
--font-weight-semibold: 600;   /* 半粗 */
--font-weight-bold: 700;       /* 粗体 */
```

### 3.4 行高
```css
--line-height-tight: 1.25;     /* 紧密行高 */
--line-height-normal: 1.5;     /* 标准行高 */
--line-height-relaxed: 1.75;   /* 宽松行高 */
```

## 4. 间距系统

### 4.1 基础间距
```css
/* 基于 4px 的间距系统 */
--spacing-1: 4px;              /* 0.25rem */
--spacing-2: 8px;              /* 0.5rem */
--spacing-3: 12px;             /* 0.75rem */
--spacing-4: 16px;             /* 1rem */
--spacing-5: 20px;             /* 1.25rem */
--spacing-6: 24px;             /* 1.5rem */
--spacing-8: 32px;             /* 2rem */
--spacing-10: 40px;            /* 2.5rem */
--spacing-12: 48px;            /* 3rem */
--spacing-16: 64px;            /* 4rem */
--spacing-20: 80px;            /* 5rem */
--spacing-24: 96px;            /* 6rem */
```

### 4.2 组件间距
```css
/* 组件内部间距 */
--component-padding-sm: var(--spacing-3);
--component-padding-md: var(--spacing-4);
--component-padding-lg: var(--spacing-6);

/* 组件外部间距 */
--component-margin-sm: var(--spacing-4);
--component-margin-md: var(--spacing-6);
--component-margin-lg: var(--spacing-8);
```

## 5. 圆角系统

```css
--border-radius-none: 0;
--border-radius-sm: 4px;       /* 小圆角 */
--border-radius-md: 6px;       /* 中等圆角 */
--border-radius-lg: 8px;       /* 大圆角 */
--border-radius-xl: 12px;      /* 特大圆角 */
--border-radius-full: 9999px;  /* 完全圆角 */
```

## 6. 阴影系统

```css
/* 阴影效果 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
             0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
             0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
             0 10px 10px -5px rgba(0, 0, 0, 0.04);
```

## 7. 组件规范

### 7.1 按钮组件
```css
/* 按钮基础样式 */
.btn {
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

/* 按钮尺寸 */
.btn-sm { padding: var(--spacing-2) var(--spacing-3); }
.btn-md { padding: var(--spacing-3) var(--spacing-4); }
.btn-lg { padding: var(--spacing-4) var(--spacing-6); }

/* 按钮类型 */
.btn-primary { background: var(--color-primary); color: white; }
.btn-secondary { background: var(--color-gray-200); color: var(--color-gray-700); }
.btn-success { background: var(--color-success); color: white; }
.btn-warning { background: var(--color-warning); color: white; }
.btn-danger { background: var(--color-danger); color: white; }
```

### 7.2 卡片组件
```css
.card {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);
}

.card-header {
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--color-gray-200);
}

.card-body {
  margin-bottom: var(--spacing-4);
}

.card-footer {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}
```

### 7.3 表单组件
```css
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.form-input {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}
```

## 8. 布局规范

### 8.1 容器规范
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-4);
}
```

### 8.2 网格系统
```css
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
```

### 8.3 弹性布局
```css
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
```

## 9. 响应式设计

### 9.1 断点系统
```css
/* 断点定义 */
--breakpoint-sm: 640px;        /* 小屏设备 */
--breakpoint-md: 768px;        /* 平板设备 */
--breakpoint-lg: 1024px;       /* 桌面设备 */
--breakpoint-xl: 1280px;       /* 大屏设备 */
```

### 9.2 媒体查询
```css
/* 移动优先的响应式设计 */
@media (min-width: 640px) {
  /* 小屏及以上 */
}

@media (min-width: 768px) {
  /* 平板及以上 */
}

@media (min-width: 1024px) {
  /* 桌面及以上 */
}
```

## 10. 动画系统

### 10.1 过渡效果
```css
--transition-fast: 0.15s ease;
--transition-normal: 0.2s ease;
--transition-slow: 0.3s ease;
```

### 10.2 常用动画
```css
/* 淡入淡出 */
.fade-enter-active, .fade-leave-active {
  transition: opacity var(--transition-normal);
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 滑动效果 */
.slide-enter-active, .slide-leave-active {
  transition: transform var(--transition-normal);
}
.slide-enter-from {
  transform: translateX(-100%);
}
.slide-leave-to {
  transform: translateX(100%);
}
```

## 11. 图标系统

### 11.1 图标规范
- 使用 Element Plus 内置图标
- 图标大小：16px, 20px, 24px, 32px
- 图标颜色与文字颜色保持一致
- 重要操作使用彩色图标

### 11.2 图标使用
```vue
<!-- Element Plus 图标 -->
<el-icon :size="20" color="#6366F1">
  <User />
</el-icon>

<!-- 自定义图标 -->
<i class="icon icon-health" style="font-size: 24px; color: #10B981;"></i>
```

## 12. 主题系统

### 12.1 浅色主题（默认）
```css
:root {
  --theme-bg-primary: var(--color-white);
  --theme-bg-secondary: var(--color-gray-50);
  --theme-text-primary: var(--color-gray-900);
  --theme-text-secondary: var(--color-gray-600);
  --theme-border: var(--color-gray-200);
}
```

### 12.2 深色主题
```css
[data-theme="dark"] {
  --theme-bg-primary: var(--color-gray-900);
  --theme-bg-secondary: var(--color-gray-800);
  --theme-text-primary: var(--color-gray-100);
  --theme-text-secondary: var(--color-gray-400);
  --theme-border: var(--color-gray-700);
}
```

## 13. 可访问性规范

### 13.1 颜色对比度
- 正文文字对比度至少 4.5:1
- 大号文字对比度至少 3:1
- 非文字元素对比度至少 3:1

### 13.2 焦点指示
```css
.focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
```

### 13.3 屏幕阅读器支持
- 使用语义化 HTML 标签
- 提供 alt 属性和 aria-label
- 确保键盘导航的可用性

## 14. 使用指南

### 14.1 CSS 变量使用
```css
/* 推荐：使用 CSS 变量 */
.my-component {
  color: var(--color-primary);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
}

/* 避免：硬编码值 */
.my-component {
  color: #6366F1;
  padding: 16px;
  border-radius: 6px;
}
```

### 14.2 组件样式组织
```vue
<style scoped>
/* 1. 组件根元素样式 */
.component-root {
  /* 样式定义 */
}

/* 2. 子元素样式 */
.component-header {
  /* 样式定义 */
}

/* 3. 状态样式 */
.component-root.is-active {
  /* 样式定义 */
}

/* 4. 响应式样式 */
@media (min-width: 768px) {
  .component-root {
    /* 样式定义 */
  }
}
</style>
```

### 14.3 最佳实践
1. 优先使用设计系统中定义的变量和类
2. 保持样式的一致性和可维护性
3. 遵循移动优先的响应式设计原则
4. 注重可访问性和用户体验
5. 定期审查和更新设计系统
