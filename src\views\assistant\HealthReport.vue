<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 报告筛选 -->
      <div class="report-filters">
        <ContentCard size="sm">
          <div class="filters-content">
            <div class="member-filter">
              <label class="filter-label">家庭成员:</label>
              <select v-model="selectedMember" class="filter-select">
                <option value="">全部成员</option>
                <option
                  v-for="member in familyMembers"
                  :key="member.id"
                  :value="member.id"
                >
                  {{ member.name }}
                </option>
              </select>
            </div>

            <div class="period-filter">
              <label class="filter-label">报告周期:</label>
              <select v-model="selectedPeriod" class="filter-select">
                <option value="weekly">周报告</option>
                <option value="monthly">月报告</option>
                <option value="quarterly">季度报告</option>
                <option value="yearly">年度报告</option>
              </select>
            </div>

            <div class="actions">
              <button class="btn btn-primary" @click="generateReport">
                生成新报告
              </button>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 报告列表 -->
      <div class="reports-list">
        <div
          v-for="report in filteredReports"
          :key="report.id"
          class="report-item"
        >
          <ContentCard>
            <div class="report-header">
              <div class="report-info">
                <h3 class="report-title">{{ report.title }}</h3>
                <div class="report-meta">
                  <span class="meta-item">
                    <span class="meta-icon">👤</span>
                    {{ getMemberName(report.memberId) }}
                  </span>
                  <span class="meta-item">
                    <span class="meta-icon">📅</span>
                    {{ formatDate(report.date) }}
                  </span>
                  <span class="meta-item">
                    <span class="meta-icon">📊</span>
                    {{ getPeriodLabel(report.period) }}
                  </span>
                  <span class="meta-item">
                    <span class="meta-icon">⭐</span>
                    健康评分: {{ report.healthScore }}
                  </span>
                </div>
              </div>

              <div class="report-actions">
                <button
                  class="btn btn-sm btn-outline"
                  @click="viewReport(report)"
                >
                  查看详情
                </button>
                <button
                  class="btn btn-sm btn-outline"
                  @click="downloadReport(report)"
                >
                  下载PDF
                </button>
                <button
                  class="btn btn-sm btn-outline"
                  @click="shareReport(report)"
                >
                  分享
                </button>
              </div>
            </div>

            <div class="report-summary">
              <h4>健康状况总结</h4>
              <p>{{ report.summary }}</p>
            </div>

            <div class="report-highlights">
              <h4>关键亮点</h4>
              <div class="highlights-grid">
                <div
                  v-for="highlight in report.highlights"
                  :key="highlight.id"
                  class="highlight-item"
                  :class="`highlight-${highlight.type}`"
                >
                  <div class="highlight-icon">{{ highlight.icon }}</div>
                  <div class="highlight-content">
                    <h5 class="highlight-title">{{ highlight.title }}</h5>
                    <p class="highlight-description">
                      {{ highlight.description }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="report-recommendations">
              <h4>改善建议</h4>
              <div class="recommendations-list">
                <div
                  v-for="(recommendation, index) in report.recommendations"
                  :key="index"
                  class="recommendation-item"
                >
                  <div class="recommendation-priority">{{ index + 1 }}</div>
                  <div class="recommendation-content">
                    <h5 class="recommendation-title">
                      {{ recommendation.title }}
                    </h5>
                    <p class="recommendation-description">
                      {{ recommendation.description }}
                    </p>
                    <div class="recommendation-tags">
                      <span
                        v-for="tag in recommendation.tags"
                        :key="tag"
                        class="recommendation-tag"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ContentCard>
        </div>

        <div v-if="filteredReports.length === 0" class="empty-reports">
          <ContentCard>
            <div class="empty-state">
              <div class="empty-icon">📋</div>
              <h3>暂无健康报告</h3>
              <p>还没有生成任何健康报告</p>
              <button class="btn btn-primary" @click="generateReport">
                生成第一份报告
              </button>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 筛选条件
const selectedMember = ref("");
const selectedPeriod = ref("monthly");

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 健康报告数据
const reports = ref([
  {
    id: 1,
    title: "2024年1月健康分析报告",
    memberId: 1,
    date: new Date("2024-01-31"),
    period: "monthly",
    healthScore: 85,
    summary:
      "本月整体健康状况良好，血压控制效果显著，体重管理有所改善。建议继续保持当前的生活方式和用药习惯，适当增加运动量。",
    highlights: [
      {
        id: 1,
        type: "positive",
        icon: "📈",
        title: "血压控制良好",
        description: "平均血压125/80mmHg，较上月改善15%",
      },
      {
        id: 2,
        type: "positive",
        icon: "⚖️",
        title: "体重下降趋势",
        description: "本月体重下降2.5kg，已接近目标值",
      },
      {
        id: 3,
        type: "warning",
        icon: "🏃",
        title: "运动量不足",
        description: "本月运动频率偏低，建议增加有氧运动",
      },
    ],
    recommendations: [
      {
        title: "增加有氧运动",
        description:
          "建议每周进行3-4次中等强度有氧运动，每次30-45分钟，如快走、游泳等",
        tags: ["运动", "心血管健康"],
      },
      {
        title: "控制钠盐摄入",
        description: "继续保持低钠饮食，每日钠摄入量控制在2000mg以下",
        tags: ["饮食", "血压管理"],
      },
      {
        title: "定期血压监测",
        description: "保持每日早晚血压监测，记录数据变化趋势",
        tags: ["监测", "血压管理"],
      },
    ],
  },
  {
    id: 2,
    title: "2024年第一季度健康总结",
    memberId: 1,
    date: new Date("2024-03-31"),
    period: "quarterly",
    healthScore: 88,
    summary:
      "第一季度健康状况持续改善，各项指标稳定向好。血压控制达到理想水平，体重管理效果显著，用药依从性良好。",
    highlights: [
      {
        id: 1,
        type: "positive",
        icon: "🎯",
        title: "达成健康目标",
        description: "血压、体重等关键指标均达到目标范围",
      },
      {
        id: 2,
        type: "positive",
        icon: "💊",
        title: "用药依从性优秀",
        description: "本季度用药依从性达到98%，无漏服记录",
      },
    ],
    recommendations: [
      {
        title: "保持当前治疗方案",
        description: "继续按医嘱服药，保持当前的生活方式",
        tags: ["用药", "生活方式"],
      },
      {
        title: "增加力量训练",
        description: "在有氧运动基础上，增加适量力量训练",
        tags: ["运动", "肌肉健康"],
      },
    ],
  },
]);

// 筛选后的报告
const filteredReports = computed(() => {
  return reports.value.filter((report) => {
    if (
      selectedMember.value &&
      report.memberId !== parseInt(selectedMember.value)
    ) {
      return false;
    }
    if (selectedPeriod.value && report.period !== selectedPeriod.value) {
      return false;
    }
    return true;
  });
});

// 获取成员名称
const getMemberName = (memberId: number) => {
  const member = familyMembers.value.find((m) => m.id === memberId);
  return member ? member.name : "未知";
};

// 获取周期标签
const getPeriodLabel = (period: string) => {
  const labels = {
    weekly: "周报告",
    monthly: "月报告",
    quarterly: "季度报告",
    yearly: "年度报告",
  };
  return labels[period] || period;
};

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 生成新报告
const generateReport = () => {
  console.log("生成新报告");
};

// 查看报告
const viewReport = (report: any) => {
  console.log("查看报告:", report.title);
};

// 下载报告
const downloadReport = (report: any) => {
  console.log("下载报告:", report.title);
};

// 分享报告
const shareReport = (report: any) => {
  console.log("分享报告:", report.title);
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.report-filters {
  margin-bottom: var(--spacing-6);
}

.filters-content {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
  flex-wrap: wrap;
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-right: var(--spacing-2);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

.actions {
  margin-left: auto;
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.report-item {
  transition: all var(--transition-normal);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.report-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.report-meta {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.meta-icon {
  font-size: var(--font-size-base);
}

.report-actions {
  display: flex;
  gap: var(--spacing-2);
}

.report-summary {
  margin-bottom: var(--spacing-6);
}

.report-summary h4,
.report-highlights h4,
.report-recommendations h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.report-summary p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.report-highlights {
  margin-bottom: var(--spacing-6);
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  border-left: 4px solid;
}

.highlight-positive {
  border-left-color: var(--color-success);
  background-color: var(--color-success-light);
}

.highlight-warning {
  border-left-color: var(--color-warning);
  background-color: var(--color-warning-light);
}

.highlight-negative {
  border-left-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.highlight-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.highlight-content {
  flex: 1;
}

.highlight-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.highlight-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.recommendation-item {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.recommendation-priority {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.recommendation-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-3) 0;
  line-height: var(--line-height-normal);
}

.recommendation-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.recommendation-tag {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.empty-reports {
  margin-top: var(--spacing-8);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state p {
  font-size: var(--font-size-base);
  margin: 0 0 var(--spacing-4) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .member-filter,
  .period-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .filter-select {
    min-width: 150px;
  }

  .actions {
    margin-left: 0;
  }

  .report-header {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .report-meta {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .report-actions {
    justify-content: stretch;
  }

  .report-actions .btn {
    flex: 1;
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }

  .recommendation-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }
}
</style>
