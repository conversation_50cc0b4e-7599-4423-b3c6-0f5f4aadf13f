import type { RouteRecordRaw } from "vue-router";

// 家庭模块路由
export const familyRoutes: RouteRecordRaw[] = [
  {
    path: "/family",
    name: "Family",
    component: () => import("@/views/family/Family.vue"),
    meta: {
      title: "家庭 - 家庭健康管理系统",
      icon: "UserFilled",
      order: 3,
    },
  },
  {
    path: "/family/medicine-box",
    name: "FamilyMedicineBox",
    component: () => import("@/views/family/FamilyMedicineBox.vue"),
    meta: {
      title: "家庭药箱 - 家庭健康管理系统",
      icon: "Medicine",
      order: 3.1,
    },
  },
];
