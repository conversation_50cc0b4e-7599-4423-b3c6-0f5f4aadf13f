<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 成员选择器 -->
      <div class="member-selector">
        <ContentCard title="选择家庭成员" size="sm">
          <div class="member-tabs">
            <button
              v-for="member in familyMembers"
              :key="member.id"
              class="member-tab"
              :class="{ active: selectedMember?.id === member.id }"
              @click="selectMember(member)"
            >
              <div class="member-avatar">
                <span>{{ member.name.charAt(0) }}</span>
              </div>
              <span class="member-name">{{ member.name }}</span>
            </button>
          </div>
        </ContentCard>
      </div>

      <!-- 建议内容 -->
      <div v-if="selectedMember" class="suggestions-content">
        <!-- 建议分类 -->
        <div class="suggestions-categories">
          <div
            v-for="category in suggestionCategories"
            :key="category.key"
            class="category-section"
          >
            <ContentCard :title="category.title" :subtitle="category.subtitle">
              <template #actions>
                <button
                  class="btn btn-sm btn-outline"
                  @click="refreshCategory(category.key)"
                >
                  刷新建议
                </button>
              </template>

              <div class="suggestions-list">
                <div
                  v-for="suggestion in getCategorySuggestions(category.key)"
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="`priority-${suggestion.priority}`"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-icon">{{ suggestion.icon }}</div>
                    <div class="suggestion-info">
                      <h4 class="suggestion-title">{{ suggestion.title }}</h4>
                      <p class="suggestion-description">
                        {{ suggestion.description }}
                      </p>
                    </div>
                    <div class="suggestion-priority">
                      <span
                        class="priority-badge"
                        :class="`priority-${suggestion.priority}`"
                      >
                        {{ getPriorityLabel(suggestion.priority) }}
                      </span>
                    </div>
                  </div>

                  <div class="suggestion-details">
                    <div class="suggestion-reason">
                      <h5>建议原因：</h5>
                      <p>{{ suggestion.reason }}</p>
                    </div>

                    <div class="suggestion-steps">
                      <h5>具体步骤：</h5>
                      <ol>
                        <li v-for="step in suggestion.steps" :key="step">
                          {{ step }}
                        </li>
                      </ol>
                    </div>

                    <div class="suggestion-benefits">
                      <h5>预期效果：</h5>
                      <ul>
                        <li
                          v-for="benefit in suggestion.benefits"
                          :key="benefit"
                        >
                          {{ benefit }}
                        </li>
                      </ul>
                    </div>

                    <div class="suggestion-tags">
                      <span
                        v-for="tag in suggestion.tags"
                        :key="tag"
                        class="suggestion-tag"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>

                  <div class="suggestion-actions">
                    <button
                      class="btn btn-sm btn-success"
                      @click="acceptSuggestion(suggestion)"
                    >
                      采纳建议
                    </button>
                    <button
                      class="btn btn-sm btn-outline"
                      @click="saveSuggestion(suggestion)"
                    >
                      保存备用
                    </button>
                    <button
                      class="btn btn-sm btn-outline"
                      @click="dismissSuggestion(suggestion)"
                    >
                      暂不采纳
                    </button>
                  </div>
                </div>

                <div
                  v-if="getCategorySuggestions(category.key).length === 0"
                  class="empty-suggestions"
                >
                  <div class="empty-icon">{{ category.emptyIcon }}</div>
                  <h3>暂无{{ category.title }}</h3>
                  <p>{{ category.emptyText }}</p>
                </div>
              </div>
            </ContentCard>
          </div>
        </div>

        <!-- AI建议生成器 -->
        <div class="ai-generator">
          <ContentCard
            title="AI建议生成器"
            subtitle="基于特定问题获取个性化建议"
          >
            <div class="generator-form">
              <div class="form-group">
                <label for="question" class="form-label"
                  >您的健康问题或目标：</label
                >
                <textarea
                  id="question"
                  v-model="customQuestion"
                  class="form-textarea"
                  placeholder="例如：我想改善睡眠质量，最近总是失眠..."
                  rows="3"
                ></textarea>
              </div>

              <div class="form-actions">
                <button
                  class="btn btn-primary"
                  @click="generateCustomSuggestion"
                  :disabled="!customQuestion.trim() || generating"
                >
                  <span v-if="generating">生成中...</span>
                  <span v-else>获取AI建议</span>
                </button>
              </div>
            </div>

            <div v-if="customSuggestionResult" class="custom-result">
              <div class="result-header">
                <h4>AI个性化建议</h4>
                <span class="result-time">{{
                  formatDateTime(new Date())
                }}</span>
              </div>
              <div class="result-content">
                <p>{{ customSuggestionResult }}</p>
              </div>
              <div class="result-actions">
                <button
                  class="btn btn-sm btn-success"
                  @click="saveCustomSuggestion"
                >
                  保存建议
                </button>
                <button
                  class="btn btn-sm btn-outline"
                  @click="shareCustomSuggestion"
                >
                  分享建议
                </button>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>

      <!-- 未选择成员时的提示 -->
      <div v-else class="no-member-selected">
        <ContentCard>
          <div class="empty-state-large">
            <div class="empty-icon">💡</div>
            <h3>请选择家庭成员</h3>
            <p>选择一个家庭成员来查看其个性化健康建议</p>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 当前选中的成员
const selectedMember = ref(null);

// 自定义问题和结果
const customQuestion = ref("");
const customSuggestionResult = ref("");
const generating = ref(false);

// 建议分类
const suggestionCategories = ref([
  {
    key: "diet",
    title: "饮食建议",
    subtitle: "营养搭配和饮食习惯优化",
    emptyIcon: "🥗",
    emptyText: "当前饮食习惯良好，暂无特别建议",
  },
  {
    key: "exercise",
    title: "运动建议",
    subtitle: "运动计划和体能提升",
    emptyIcon: "🏃",
    emptyText: "运动量充足，继续保持当前运动习惯",
  },
  {
    key: "lifestyle",
    title: "生活方式",
    subtitle: "作息调整和生活习惯改善",
    emptyIcon: "🌙",
    emptyText: "生活方式健康，继续保持良好习惯",
  },
  {
    key: "medical",
    title: "医疗建议",
    subtitle: "用药指导和就医提醒",
    emptyIcon: "💊",
    emptyText: "医疗管理规范，按时复查即可",
  },
]);

// 家庭成员数据（包含建议信息）
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    suggestions: {
      diet: [
        {
          id: 1,
          title: "减少钠盐摄入",
          description: "控制每日钠摄入量，有助于血压管理",
          icon: "🧂",
          priority: "high",
          reason: "您的血压偏高，减少钠盐摄入可以有效降低血压",
          steps: [
            "每日钠摄入量控制在2000mg以下",
            "选择低钠或无盐调料",
            "多使用天然香料调味",
            "避免加工食品和腌制食品",
          ],
          benefits: ["降低血压5-10mmHg", "减少心血管疾病风险", "改善肾脏功能"],
          tags: ["血压管理", "心血管健康", "饮食控制"],
        },
        {
          id: 2,
          title: "增加富钾食物",
          description: "多食用香蕉、橙子等富含钾的食物",
          icon: "🍌",
          priority: "medium",
          reason: "钾元素有助于平衡钠的作用，对血压控制有益",
          steps: [
            "每日摄入3-4份富钾水果",
            "选择香蕉、橙子、猕猴桃等",
            "增加绿叶蔬菜摄入",
            "适量食用坚果类",
          ],
          benefits: ["平衡体内电解质", "辅助降血压", "改善心脏功能"],
          tags: ["营养补充", "血压管理", "水果蔬菜"],
        },
      ],
      exercise: [
        {
          id: 3,
          title: "增加有氧运动",
          description: "每周进行3-4次中等强度有氧运动",
          icon: "🏃",
          priority: "high",
          reason: "有氧运动可以有效改善心血管功能，降低血压",
          steps: [
            "选择快走、游泳、骑车等运动",
            "每次运动30-45分钟",
            "保持中等强度（微微出汗）",
            "运动前后做好热身和拉伸",
          ],
          benefits: [
            "改善心肺功能",
            "降低血压和血脂",
            "控制体重",
            "提升整体健康水平",
          ],
          tags: ["有氧运动", "心血管健康", "体重管理"],
        },
      ],
      lifestyle: [
        {
          id: 4,
          title: "改善睡眠质量",
          description: "保持规律作息，确保充足睡眠",
          icon: "😴",
          priority: "medium",
          reason: "良好的睡眠有助于血压稳定和整体健康",
          steps: [
            "每晚保证7-8小时睡眠",
            "固定睡眠和起床时间",
            "睡前1小时避免电子设备",
            "创造舒适的睡眠环境",
          ],
          benefits: ["稳定血压", "提高免疫力", "改善精神状态", "促进身体恢复"],
          tags: ["睡眠管理", "作息规律", "压力缓解"],
        },
      ],
      medical: [
        {
          id: 5,
          title: "定期血压监测",
          description: "每日早晚测量血压并记录",
          icon: "🩺",
          priority: "high",
          reason: "定期监测有助于及时发现血压变化，调整治疗方案",
          steps: [
            "每日早晚固定时间测量",
            "测量前休息5分钟",
            "记录血压数值和时间",
            "定期与医生分享数据",
          ],
          benefits: [
            "及时发现血压异常",
            "评估治疗效果",
            "指导用药调整",
            "预防并发症",
          ],
          tags: ["血压监测", "数据记录", "医疗管理"],
        },
      ],
    },
  },
]);

// 选择成员
const selectMember = (member: any) => {
  selectedMember.value = member;
};

// 获取分类建议
const getCategorySuggestions = (category: string) => {
  if (!selectedMember.value) return [];
  return selectedMember.value.suggestions[category] || [];
};

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels = {
    high: "高",
    medium: "中",
    low: "低",
  };
  return labels[priority] || priority;
};

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString("zh-CN");
};

// 刷新分类建议
const refreshCategory = (category: string) => {
  console.log("刷新建议分类:", category);
};

// 采纳建议
const acceptSuggestion = (suggestion: any) => {
  console.log("采纳建议:", suggestion.title);
};

// 保存建议
const saveSuggestion = (suggestion: any) => {
  console.log("保存建议:", suggestion.title);
};

// 忽略建议
const dismissSuggestion = (suggestion: any) => {
  console.log("忽略建议:", suggestion.title);
};

// 生成自定义建议
const generateCustomSuggestion = async () => {
  if (!customQuestion.value.trim()) return;

  generating.value = true;

  try {
    // 模拟AI生成建议
    await new Promise((resolve) => setTimeout(resolve, 2000));

    customSuggestionResult.value = `基于您的问题"${customQuestion.value}"，AI建议：建议您采用渐进式改善方法，首先调整睡前习惯，避免睡前2小时内进食和使用电子设备。其次，建立固定的睡眠时间，每晚10:30上床，早上6:30起床。此外，可以尝试睡前冥想或深呼吸练习，有助于放松身心。如果问题持续，建议咨询专业医生。`;
  } catch (error) {
    console.error("生成建议失败:", error);
  } finally {
    generating.value = false;
  }
};

// 保存自定义建议
const saveCustomSuggestion = () => {
  console.log("保存自定义建议");
};

// 分享自定义建议
const shareCustomSuggestion = () => {
  console.log("分享自定义建议");
};

// 组件挂载时选择第一个成员
onMounted(() => {
  if (familyMembers.value.length > 0) {
    selectedMember.value = familyMembers.value[0];
  }
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.member-selector {
  margin-bottom: var(--spacing-6);
}

.member-tabs {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.member-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.member-tab:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.member-tab.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: white;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.member-tab.active .member-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.suggestions-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.suggestions-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.suggestion-item {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid;
  background-color: var(--theme-bg-secondary);
}

.priority-high {
  border-left-color: var(--color-danger);
}

.priority-medium {
  border-left-color: var(--color-warning);
}

.priority-low {
  border-left-color: var(--color-info);
}

.suggestion-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.suggestion-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.suggestion-info {
  flex: 1;
}

.suggestion-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.suggestion-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.priority-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: white;
}

.priority-badge.priority-high {
  background-color: var(--color-danger);
}

.priority-badge.priority-medium {
  background-color: var(--color-warning);
}

.priority-badge.priority-low {
  background-color: var(--color-info);
}

.suggestion-details {
  margin-bottom: var(--spacing-4);
}

.suggestion-reason,
.suggestion-steps,
.suggestion-benefits {
  margin-bottom: var(--spacing-3);
}

.suggestion-reason h5,
.suggestion-steps h5,
.suggestion-benefits h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.suggestion-reason p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.suggestion-steps ol,
.suggestion-benefits ul {
  margin: 0;
  padding-left: var(--spacing-4);
}

.suggestion-steps li,
.suggestion-benefits li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
}

.suggestion-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-3);
}

.suggestion-tag {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.suggestion-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.empty-suggestions {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

.empty-suggestions .empty-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-3);
}

.empty-suggestions h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-suggestions p {
  font-size: var(--font-size-sm);
  margin: 0;
}

.ai-generator {
  margin-top: var(--spacing-6);
}

.generator-form {
  margin-bottom: var(--spacing-6);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-family: inherit;
  resize: vertical;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.custom-result {
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--color-primary);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.result-header h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.result-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.result-content p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-4) 0;
}

.result-actions {
  display: flex;
  gap: var(--spacing-2);
}

.no-member-selected {
  margin-top: var(--spacing-8);
}

.empty-state-large {
  text-align: center;
  padding: var(--spacing-12);
}

.empty-state-large .empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state-large h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state-large p {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-tabs {
    flex-direction: column;
  }

  .suggestions-categories {
    grid-template-columns: 1fr;
  }

  .suggestion-header {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .suggestion-actions {
    justify-content: stretch;
  }

  .suggestion-actions .btn {
    flex: 1;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }

  .result-actions {
    justify-content: stretch;
  }

  .result-actions .btn {
    flex: 1;
  }
}
</style>
