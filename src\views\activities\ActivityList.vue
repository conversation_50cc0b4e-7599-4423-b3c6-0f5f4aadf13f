<template>
  <div class="page-container">
    <!-- 活动统计 -->
    <div class="activity-stats">
      <StatCard
        title="总活动数"
        :value="activityStats.total"
        unit="个"
        status="normal"
        icon="Calendar"
        icon-color="#3B82F6"
        icon-bg-color="#DBEAFE"
        size="sm"
      />
      <StatCard
        title="已完成"
        :value="activityStats.completed"
        unit="个"
        status="success"
        icon="CheckCircle"
        icon-color="#10B981"
        icon-bg-color="#D1FAE5"
        size="sm"
      />
      <StatCard
        title="待完成"
        :value="activityStats.pending"
        unit="个"
        status="warning"
        icon="Clock"
        icon-color="#F59E0B"
        icon-bg-color="#FEF3C7"
        size="sm"
      />
      <StatCard
        title="已逾期"
        :value="activityStats.overdue"
        unit="个"
        status="danger"
        icon="AlertTriangle"
        icon-color="#EF4444"
        icon-bg-color="#FEE2E2"
        size="sm"
      />
      <StatCard
        title="即将到期"
        :value="activityStats.upcoming"
        unit="个"
        status="info"
        icon="Bell"
        icon-color="#8B5CF6"
        icon-bg-color="#EDE9FE"
        size="sm"
      />
    </div>

    <!-- 筛选和操作栏 -->
    <div class="activity-controls">
      <ContentCard size="sm">
        <div class="controls-content">
          <div class="filters">
            <select v-model="filters.member" class="filter-select">
              <option value="">全部成员</option>
              <option
                v-for="member in familyMembers"
                :key="member.id"
                :value="member.id"
              >
                {{ member.name }}
              </option>
            </select>

            <select v-model="filters.type" class="filter-select">
              <option value="">全部类型</option>
              <option value="medication">用药管理</option>
              <option value="exercise">运动锻炼</option>
              <option value="checkup">健康检查</option>
              <option value="appointment">医疗预约</option>
            </select>

            <select v-model="filters.status" class="filter-select">
              <option value="">全部状态</option>
              <option value="pending">待完成</option>
              <option value="completed">已完成</option>
              <option value="overdue">已逾期</option>
            </select>

            <select v-model="filters.priority" class="filter-select">
              <option value="">全部优先级</option>
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>

          <div class="actions">
            <button
              class="btn btn-outline"
              @click="showAdvancedFilters = !showAdvancedFilters"
            >
              高级筛选
            </button>
            <button
              class="btn btn-primary"
              @click="showAddActivityDialog = true"
            >
              添加活动
            </button>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 活动列表 -->
    <div class="activity-list">
      <ContentCard title="活动列表">
        <div class="activities">
          <div
            v-for="activity in filteredActivities"
            :key="activity.id"
            class="activity-item"
            :class="{
              completed: activity.status === 'completed',
              overdue: activity.status === 'overdue',
            }"
          >
            <div class="activity-header">
              <div class="activity-info">
                <div class="activity-title-row">
                  <div class="activity-type-icon">
                    <component :is="getActivityIcon(activity.type)" />
                  </div>
                  <h3 class="activity-title">{{ activity.title }}</h3>
                  <div class="activity-badges">
                    <span class="type-badge" :class="`type-${activity.type}`">
                      {{ getTypeLabel(activity.type) }}
                    </span>
                    <span
                      class="priority-badge"
                      :class="`priority-${activity.priority}`"
                    >
                      {{ getPriorityLabel(activity.priority) }}
                    </span>
                    <span v-if="activity.recurring" class="recurring-badge">
                      重复
                    </span>
                  </div>
                </div>
                <p class="activity-description">{{ activity.description }}</p>
                <div class="activity-meta">
                  <span class="meta-item">
                    <span class="meta-icon">👤</span>
                    {{ getMemberName(activity.memberId) }}
                  </span>
                  <span class="meta-item">
                    <span class="meta-icon">📅</span>
                    {{ formatDate(activity.date) }}
                  </span>
                  <span class="meta-item">
                    <span class="meta-icon">⏰</span>
                    {{ activity.time }}
                  </span>
                  <span v-if="activity.reminder" class="meta-item">
                    <span class="meta-icon">🔔</span>
                    {{ activity.reminder }}
                  </span>
                </div>
                <div v-if="activity.tags.length > 0" class="activity-tags">
                  <span
                    v-for="tag in activity.tags"
                    :key="tag"
                    class="activity-tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>

              <div class="activity-status">
                <div
                  class="status-indicator"
                  :class="`status-${activity.status}`"
                >
                  <span v-if="activity.status === 'completed'">✓</span>
                  <span v-else-if="activity.status === 'overdue'">!</span>
                  <span v-else>○</span>
                </div>
                <span class="status-text">{{
                  getStatusLabel(activity.status)
                }}</span>
              </div>
            </div>

            <div class="activity-actions">
              <button
                v-if="activity.status !== 'completed'"
                class="btn btn-sm btn-success"
                @click="markAsCompleted(activity)"
              >
                标记完成
              </button>
              <button
                class="btn btn-sm btn-outline"
                @click="editActivity(activity)"
              >
                编辑
              </button>
              <button
                class="btn btn-sm btn-outline"
                @click="duplicateActivity(activity)"
              >
                复制
              </button>
              <button
                class="btn btn-sm btn-danger"
                @click="deleteActivity(activity)"
              >
                删除
              </button>
            </div>
          </div>

          <div v-if="filteredActivities.length === 0" class="empty-state">
            <div class="empty-icon">📋</div>
            <h3>暂无活动</h3>
            <p>还没有符合条件的活动记录</p>
            <button
              class="btn btn-primary"
              @click="showAddActivityDialog = true"
            >
              添加第一个活动
            </button>
          </div>
        </div>
      </ContentCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 筛选条件
const filters = ref({
  member: "",
  type: "",
  status: "",
  priority: "",
});

// 显示高级筛选
const showAdvancedFilters = ref(false);

// 显示添加活动对话框
const showAddActivityDialog = ref(false);

// 家庭成员数据
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 活动数据
const activities = ref([
  {
    id: 1,
    title: "晨间血压测量",
    description: "每日早晨起床后测量血压并记录",
    type: "checkup",
    memberId: 1,
    date: new Date(),
    time: "07:00",
    status: "pending",
    priority: "high",
    recurring: true,
    reminder: "提前15分钟",
    tags: ["血压", "日常监测"],
  },
  {
    id: 2,
    title: "服用降压药",
    description: "按医嘱服用降压药物",
    type: "medication",
    memberId: 1,
    date: new Date(),
    time: "08:00",
    status: "completed",
    priority: "high",
    recurring: true,
    reminder: "准时提醒",
    tags: ["用药", "高血压"],
  },
  {
    id: 3,
    title: "晚间散步",
    description: "饭后1小时进行30分钟散步",
    type: "exercise",
    memberId: 2,
    date: new Date(),
    time: "19:30",
    status: "pending",
    priority: "medium",
    recurring: true,
    reminder: "提前10分钟",
    tags: ["运动", "散步"],
  },
  {
    id: 4,
    title: "心脏科复诊",
    description: "定期心脏科检查和药物调整",
    type: "appointment",
    memberId: 1,
    date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    time: "14:00",
    status: "pending",
    priority: "high",
    recurring: false,
    reminder: "提前1天",
    tags: ["复诊", "心脏科"],
  },
  {
    id: 5,
    title: "血糖检测",
    description: "餐后2小时血糖检测",
    type: "checkup",
    memberId: 2,
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    time: "14:00",
    status: "overdue",
    priority: "medium",
    recurring: true,
    reminder: "准时提醒",
    tags: ["血糖", "餐后检测"],
  },
]);

// 计算活动统计
const activityStats = computed(() => {
  const total = activities.value.length;
  const completed = activities.value.filter(
    (a) => a.status === "completed"
  ).length;
  const pending = activities.value.filter((a) => a.status === "pending").length;
  const overdue = activities.value.filter((a) => a.status === "overdue").length;
  const upcoming = activities.value.filter((a) => {
    const activityDate = new Date(a.date);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return a.status === "pending" && activityDate <= tomorrow;
  }).length;

  return { total, completed, pending, overdue, upcoming };
});

// 筛选后的活动
const filteredActivities = computed(() => {
  return activities.value.filter((activity) => {
    if (
      filters.value.member &&
      activity.memberId !== parseInt(filters.value.member)
    ) {
      return false;
    }
    if (filters.value.type && activity.type !== filters.value.type) {
      return false;
    }
    if (filters.value.status && activity.status !== filters.value.status) {
      return false;
    }
    if (
      filters.value.priority &&
      activity.priority !== filters.value.priority
    ) {
      return false;
    }
    return true;
  });
});

// 获取成员名称
const getMemberName = (memberId: number) => {
  const member = familyMembers.value.find((m) => m.id === memberId);
  return member ? member.name : "未知";
};

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labels = {
    medication: "用药管理",
    exercise: "运动锻炼",
    checkup: "健康检查",
    appointment: "医疗预约",
  };
  return labels[type] || type;
};

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels = {
    high: "高",
    medium: "中",
    low: "低",
  };
  return labels[priority] || priority;
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    pending: "待完成",
    completed: "已完成",
    overdue: "已逾期",
  };
  return labels[status] || status;
};

// 获取活动图标
const getActivityIcon = (type: string) => {
  switch (type) {
    case "medication":
      return "Medicine";
    case "exercise":
      return "Trophy";
    case "checkup":
      return "Monitor";
    case "appointment":
      return "Calendar";
    default:
      return "Clock";
  }
};

// 格式化日期
const formatDate = (date: Date) => {
  const today = new Date();
  const activityDate = new Date(date);

  if (activityDate.toDateString() === today.toDateString()) {
    return "今天";
  }

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  if (activityDate.toDateString() === tomorrow.toDateString()) {
    return "明天";
  }

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  if (activityDate.toDateString() === yesterday.toDateString()) {
    return "昨天";
  }

  return activityDate.toLocaleDateString("zh-CN");
};

// 标记为完成
const markAsCompleted = (activity: any) => {
  activity.status = "completed";
  console.log("标记完成:", activity.title);
};

// 编辑活动
const editActivity = (activity: any) => {
  console.log("编辑活动:", activity.title);
};

// 复制活动
const duplicateActivity = (activity: any) => {
  console.log("复制活动:", activity.title);
};

// 删除活动
const deleteActivity = (activity: any) => {
  if (confirm(`确定要删除活动"${activity.title}"吗？`)) {
    const index = activities.value.findIndex((a) => a.id === activity.id);
    if (index > -1) {
      activities.value.splice(index, 1);
    }
  }
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}

.activity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.activity-controls {
  margin-bottom: var(--spacing-6);
}

.controls-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-4);
}

.filters {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.actions {
  display: flex;
  gap: var(--spacing-2);
}

.activity-list {
  margin-bottom: var(--spacing-6);
}

.activities {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  padding: var(--spacing-6);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  background-color: var(--theme-bg-secondary);
  transition: all var(--transition-normal);
}

.activity-item:hover {
  box-shadow: var(--shadow-md);
}

.activity-item.completed {
  opacity: 0.7;
  border-color: var(--color-success);
}

.activity-item.overdue {
  border-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.activity-info {
  flex: 1;
}

.activity-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2);
}

.activity-type-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
  margin-right: var(--spacing-3);
}

.activity-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
  flex: 1;
}

.activity-badges {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.type-badge,
.priority-badge,
.recurring-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.type-medication {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.type-exercise {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.type-checkup {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.type-appointment {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.priority-high {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.priority-medium {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.priority-low {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.recurring-badge {
  background-color: var(--color-secondary-light);
  color: var(--color-secondary-dark);
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-3) 0;
  line-height: var(--line-height-normal);
}

.activity-meta {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-3);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.meta-icon {
  font-size: var(--font-size-base);
}

.activity-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.activity-tag {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
}

.activity-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  margin-left: var(--spacing-4);
}

.status-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  color: white;
}

.status-pending {
  background-color: var(--color-gray-400);
}

.status-completed {
  background-color: var(--color-success);
}

.status-overdue {
  background-color: var(--color-danger);
}

.status-text {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  text-align: center;
}

.activity-actions {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state p {
  font-size: var(--font-size-base);
  margin: 0 0 var(--spacing-4) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activity-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .controls-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filters {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
  }

  .activity-header {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .activity-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .activity-status {
    flex-direction: row;
    align-items: center;
    margin-left: 0;
  }

  .activity-meta {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .activity-actions {
    justify-content: stretch;
  }

  .activity-actions .btn {
    flex: 1;
  }
}
</style>
