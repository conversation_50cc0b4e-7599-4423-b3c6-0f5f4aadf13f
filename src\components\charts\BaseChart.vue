<template>
  <div class="chart-container">
    <div
      ref="chartRef"
      class="chart"
      :style="{ width: width, height: height }"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';

interface Props {
  option: EChartsOption;
  width?: string;
  height?: string;
  theme?: string;
  loading?: boolean;
  loadingOption?: object;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  theme: 'default',
  loading: false,
  loadingOption: () => ({
    text: '加载中...',
    color: '#3B82F6',
    textColor: '#666',
    maskColor: 'rgba(255, 255, 255, 0.8)',
    zlevel: 0
  })
});

const emit = defineEmits<{
  chartReady: [chart: echarts.ECharts];
  chartClick: [params: any];
  chartMouseover: [params: any];
  chartMouseout: [params: any];
}>();

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  await nextTick();
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value, props.theme);
  
  // 设置配置项
  chartInstance.setOption(props.option);
  
  // 绑定事件
  chartInstance.on('click', (params) => {
    emit('chartClick', params);
  });
  
  chartInstance.on('mouseover', (params) => {
    emit('chartMouseover', params);
  });
  
  chartInstance.on('mouseout', (params) => {
    emit('chartMouseout', params);
  });
  
  // 发出图表就绪事件
  emit('chartReady', chartInstance);
};

// 更新图表配置
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(props.option, true);
  }
};

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 显示/隐藏加载状态
const toggleLoading = (loading: boolean) => {
  if (!chartInstance) return;
  
  if (loading) {
    chartInstance.showLoading('default', props.loadingOption);
  } else {
    chartInstance.hideLoading();
  }
};

// 监听配置变化
watch(
  () => props.option,
  () => {
    updateChart();
  },
  { deep: true }
);

// 监听加载状态变化
watch(
  () => props.loading,
  (newVal) => {
    toggleLoading(newVal);
  }
);

// 监听窗口大小变化
const handleResize = () => {
  resizeChart();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});

// 暴露方法给父组件
defineExpose({
  getChart: () => chartInstance,
  resize: resizeChart,
  showLoading: () => toggleLoading(true),
  hideLoading: () => toggleLoading(false)
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
