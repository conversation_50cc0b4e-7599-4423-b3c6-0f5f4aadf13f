<template>
  <div class="page-container">
    <!-- 成员选择器 -->
    <div class="member-selector">
      <ContentCard title="选择家庭成员" size="sm">
        <div class="member-tabs">
          <button
            v-for="member in familyMembers"
            :key="member.id"
            class="member-tab"
            :class="{ active: selectedMember?.id === member.id }"
            @click="selectMember(member)"
          >
            <div class="member-avatar">
              <span>{{ member.name.charAt(0) }}</span>
            </div>
            <span class="member-name">{{ member.name }}</span>
          </button>
        </div>
      </ContentCard>
    </div>

    <!-- 健康概要内容 -->
    <div v-if="selectedMember" class="health-overview-content">
      <!-- 标签页导航 -->
      <div class="tabs-navigation">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-button"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-text">{{ tab.title }}</span>
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 健康概览 -->
        <div v-if="activeTab === 'overview'" class="tab-panel">
          <div class="overview-grid">
            <!-- 核心健康指标 -->
            <ContentCard title="核心健康指标" subtitle="当前健康状况一览">
              <div class="health-metrics">
                <div
                  v-for="metric in selectedMember.healthMetrics"
                  :key="metric.id"
                  class="metric-item"
                >
                  <div class="metric-header">
                    <div
                      class="metric-icon"
                      :style="{
                        backgroundColor: metric.iconBgColor,
                        color: metric.iconColor,
                      }"
                    >
                      <span>{{ metric.icon }}</span>
                    </div>
                    <div class="metric-info">
                      <h4 class="metric-name">{{ metric.name }}</h4>
                      <div class="metric-value">
                        {{ metric.value }}
                        <span class="metric-unit">{{ metric.unit }}</span>
                      </div>
                    </div>
                    <div class="metric-status">
                      <span
                        class="status-indicator"
                        :class="`status-${metric.status}`"
                      >
                        {{ getStatusLabel(metric.status) }}
                      </span>
                    </div>
                  </div>

                  <div class="metric-details">
                    <div class="metric-trend">
                      <span
                        class="trend-icon"
                        :class="`trend-${metric.trend.direction}`"
                      >
                        {{ getTrendIcon(metric.trend.direction) }}
                      </span>
                      <span class="trend-text">
                        {{
                          metric.trend.direction === "stable"
                            ? "稳定"
                            : `${
                                metric.trend.direction === "up"
                                  ? "上升"
                                  : "下降"
                              } ${metric.trend.value}%`
                        }}
                        ({{ metric.trend.period }})
                      </span>
                    </div>
                    <div class="metric-description">
                      {{ metric.description }}
                    </div>
                  </div>
                </div>
              </div>
            </ContentCard>

            <!-- 健康建议 -->
            <ContentCard title="个性化健康建议" subtitle="基于最新数据的AI建议">
              <div class="health-suggestions">
                <div
                  v-for="suggestion in selectedMember.healthSuggestions"
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="`priority-${suggestion.priority}`"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-icon">{{ suggestion.icon }}</div>
                    <div class="suggestion-content">
                      <h5 class="suggestion-title">{{ suggestion.title }}</h5>
                      <p class="suggestion-description">
                        {{ suggestion.description }}
                      </p>
                    </div>
                    <div class="suggestion-priority">
                      <span
                        class="priority-badge"
                        :class="`priority-${suggestion.priority}`"
                      >
                        {{ getPriorityLabel(suggestion.priority) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </ContentCard>

            <!-- 整体健康评分 -->
            <ContentCard title="整体健康评分" subtitle="综合健康状况评估">
              <div class="health-score">
                <div class="score-circle">
                  <div class="score-value">
                    {{ selectedMember.healthScore.overall }}
                  </div>
                  <div class="score-label">总分</div>
                </div>
                <div class="score-breakdown">
                  <div
                    v-for="category in selectedMember.healthScore.categories"
                    :key="category.name"
                    class="score-category"
                  >
                    <div class="category-header">
                      <span class="category-name">{{ category.name }}</span>
                      <span class="category-score"
                        >{{ category.score }}/100</span
                      >
                    </div>
                    <div class="category-progress">
                      <div
                        class="progress-bar"
                        :style="{ width: `${category.score}%` }"
                        :class="`progress-${getScoreLevel(category.score)}`"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>
        </div>

        <!-- 趋势分析 -->
        <div v-if="activeTab === 'trends'" class="tab-panel">
          <ContentCard title="健康趋势分析" subtitle="过去30天健康数据变化趋势">
            <div class="trends-charts">
              <div class="chart-container">
                <h4>血压趋势</h4>
                <LineChart
                  :data="bloodPressureTrend"
                  title=""
                  x-axis-label="日期"
                  y-axis-label="血压 (mmHg)"
                  height="250px"
                  :smooth="true"
                  :show-area="true"
                  color="#EF4444"
                />
              </div>
              <div class="chart-container">
                <h4>血糖趋势</h4>
                <LineChart
                  :data="bloodSugarTrend"
                  title=""
                  x-axis-label="日期"
                  y-axis-label="血糖 (mmol/L)"
                  height="250px"
                  :smooth="true"
                  :show-area="true"
                  color="#F59E0B"
                />
              </div>
              <div class="chart-container">
                <h4>体重趋势</h4>
                <LineChart
                  :data="weightTrend"
                  title=""
                  x-axis-label="日期"
                  y-axis-label="体重 (kg)"
                  height="250px"
                  :smooth="true"
                  :show-area="true"
                  color="#10B981"
                />
              </div>
            </div>

            <!-- 趋势总结 -->
            <div class="trends-summary">
              <h4>趋势总结</h4>
              <div class="summary-items">
                <div class="summary-item improving">
                  <span class="summary-icon">📈</span>
                  <span class="summary-text"
                    >血压控制效果显著，较上月改善15%</span
                  >
                </div>
                <div class="summary-item stable">
                  <span class="summary-icon">📊</span>
                  <span class="summary-text"
                    >血糖水平保持稳定，波动范围正常</span
                  >
                </div>
                <div class="summary-item improving">
                  <span class="summary-icon">⚖️</span>
                  <span class="summary-text"
                    >体重下降趋势良好，已接近目标值</span
                  >
                </div>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 健康报告 -->
        <div v-if="activeTab === 'reports'" class="tab-panel">
          <ContentCard
            title="AI健康报告"
            subtitle="智能生成的个性化健康分析报告"
          >
            <div class="reports-list">
              <div
                v-for="report in selectedMember.healthReports"
                :key="report.id"
                class="report-item"
              >
                <div class="report-header">
                  <div class="report-info">
                    <h4 class="report-title">{{ report.title }}</h4>
                    <p class="report-meta">
                      {{ formatDate(report.date) }} · {{ report.period }} ·
                      {{ report.status }}
                    </p>
                  </div>
                  <div class="report-actions">
                    <button
                      class="btn btn-sm btn-outline"
                      @click="viewReport(report)"
                    >
                      查看详情
                    </button>
                    <button
                      class="btn btn-sm btn-outline"
                      @click="downloadReport(report)"
                    >
                      下载PDF
                    </button>
                    <button
                      class="btn btn-sm btn-outline"
                      @click="shareReport(report)"
                    >
                      分享
                    </button>
                  </div>
                </div>

                <div class="report-summary">
                  <h5>健康状况总结</h5>
                  <p>{{ report.summary }}</p>
                </div>

                <div class="report-findings">
                  <h5>关键发现</h5>
                  <ul>
                    <li v-for="finding in report.keyFindings" :key="finding">
                      {{ finding }}
                    </li>
                  </ul>
                </div>

                <div class="report-recommendations">
                  <h5>改善建议</h5>
                  <ul>
                    <li
                      v-for="recommendation in report.recommendations"
                      :key="recommendation"
                    >
                      {{ recommendation }}
                    </li>
                  </ul>
                </div>
              </div>

              <div
                v-if="selectedMember.healthReports.length === 0"
                class="empty-reports"
              >
                <div class="empty-icon">📋</div>
                <h3>暂无健康报告</h3>
                <p>AI正在分析您的健康数据，报告将自动生成</p>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>

    <!-- 未选择成员时的提示 -->
    <div v-else class="no-member-selected">
      <ContentCard>
        <div class="empty-state-large">
          <div class="empty-icon">👤</div>
          <h3>请选择家庭成员</h3>
          <p>选择一个家庭成员来查看其综合健康概要</p>
        </div>
      </ContentCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 当前选中的成员
const selectedMember = ref(null);

// 当前激活的标签页
const activeTab = ref("overview");

// 标签页配置
const tabs = ref([
  { key: "overview", title: "健康概览", icon: "📊" },
  { key: "trends", title: "趋势分析", icon: "📈" },
  { key: "reports", title: "健康报告", icon: "📋" },
]);

// 家庭成员数据（包含健康概要信息）
const familyMembers = ref([
  {
    id: 1,
    name: "张先生",
    healthMetrics: [
      {
        id: 1,
        name: "血压",
        value: "125/80",
        unit: "mmHg",
        status: "normal",
        icon: "❤️",
        iconColor: "#EF4444",
        iconBgColor: "#FEE2E2",
        trend: { direction: "down", value: 5, period: "本周" },
        description: "血压控制良好，继续保持当前治疗方案",
      },
      {
        id: 2,
        name: "血糖",
        value: "5.8",
        unit: "mmol/L",
        status: "normal",
        icon: "🩸",
        iconColor: "#3B82F6",
        iconBgColor: "#DBEAFE",
        trend: { direction: "stable", value: 0, period: "本周" },
        description: "血糖水平稳定，继续保持饮食控制",
      },
      {
        id: 3,
        name: "体重",
        value: "72.5",
        unit: "kg",
        status: "improving",
        icon: "⚖️",
        iconColor: "#10B981",
        iconBgColor: "#D1FAE5",
        trend: { direction: "down", value: 3, period: "本月" },
        description: "体重下降趋势良好，已接近目标值",
      },
    ],
    healthSuggestions: [
      {
        id: 1,
        title: "增加有氧运动",
        description: "建议每周进行3-4次中等强度有氧运动，每次30-45分钟",
        icon: "🏃",
        priority: "high",
      },
      {
        id: 2,
        title: "控制钠盐摄入",
        description: "减少钠盐摄入，多食用富含钾的食物如香蕉、橙子",
        icon: "🧂",
        priority: "medium",
      },
      {
        id: 3,
        title: "保持规律作息",
        description: "确保充足睡眠，保持规律的作息时间",
        icon: "😴",
        priority: "medium",
      },
    ],
    healthScore: {
      overall: 85,
      categories: [
        { name: "血压控制", score: 88 },
        { name: "血糖管理", score: 92 },
        { name: "体重控制", score: 78 },
        { name: "用药依从性", score: 95 },
      ],
    },
    healthReports: [
      {
        id: 1,
        title: "月度健康分析报告",
        date: new Date("2024-01-15"),
        period: "2024年1月",
        status: "已完成",
        summary:
          "整体健康状况良好，血压控制效果显著，建议继续保持当前的生活方式和用药习惯。",
        keyFindings: [
          "血压较上月改善15%，平均值在正常范围内",
          "体重下降3kg，已接近目标值",
          "用药依从性良好，无漏服记录",
        ],
        recommendations: [
          "继续保持低钠饮食",
          "增加有氧运动频率",
          "定期监测血压变化",
          "保持规律作息时间",
        ],
      },
    ],
  },
]);

// 选择成员
const selectMember = (member: any) => {
  selectedMember.value = member;
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    normal: "正常",
    warning: "需关注",
    danger: "需就医",
    improving: "改善中",
  };
  return labels[status] || status;
};

// 获取趋势图标
const getTrendIcon = (direction: string) => {
  const icons = {
    up: "↗",
    down: "↘",
    stable: "→",
  };
  return icons[direction] || "→";
};

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels = {
    high: "高",
    medium: "中",
    low: "低",
  };
  return labels[priority] || priority;
};

// 获取评分等级
const getScoreLevel = (score: number) => {
  if (score >= 90) return "excellent";
  if (score >= 80) return "good";
  if (score >= 70) return "fair";
  return "poor";
};

// 趋势图表数据
const bloodPressureTrend = ref([
  { name: "1周前", value: 135, date: "2024-01-14" },
  { name: "6天前", value: 132, date: "2024-01-15" },
  { name: "5天前", value: 128, date: "2024-01-16" },
  { name: "4天前", value: 125, date: "2024-01-17" },
  { name: "3天前", value: 122, date: "2024-01-18" },
  { name: "2天前", value: 120, date: "2024-01-19" },
  { name: "昨天", value: 118, date: "2024-01-20" },
]);

const bloodSugarTrend = ref([
  { name: "1周前", value: 7.2, date: "2024-01-14" },
  { name: "6天前", value: 6.8, date: "2024-01-15" },
  { name: "5天前", value: 6.5, date: "2024-01-16" },
  { name: "4天前", value: 6.3, date: "2024-01-17" },
  { name: "3天前", value: 6.1, date: "2024-01-18" },
  { name: "2天前", value: 5.9, date: "2024-01-19" },
  { name: "昨天", value: 5.8, date: "2024-01-20" },
]);

const weightTrend = ref([
  { name: "1周前", value: 75.2, date: "2024-01-14" },
  { name: "6天前", value: 75.0, date: "2024-01-15" },
  { name: "5天前", value: 74.8, date: "2024-01-16" },
  { name: "4天前", value: 74.5, date: "2024-01-17" },
  { name: "3天前", value: 74.3, date: "2024-01-18" },
  { name: "2天前", value: 74.0, date: "2024-01-19" },
  { name: "昨天", value: 73.8, date: "2024-01-20" },
]);

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 报告操作
const viewReport = (report: any) => {
  console.log("查看报告:", report.title);
};

const downloadReport = (report: any) => {
  console.log("下载报告:", report.title);
};

const shareReport = (report: any) => {
  console.log("分享报告:", report.title);
};

// 组件挂载时选择第一个成员
onMounted(() => {
  if (familyMembers.value.length > 0) {
    selectedMember.value = familyMembers.value[0];
  }
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.health-overview-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

/* 复用个人信息页面的样式 */
.member-selector {
  margin-bottom: var(--spacing-6);
}

.member-tabs {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.member-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.member-tab:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.member-tab.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: white;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.member-tab.active .member-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.tabs-navigation {
  display: flex;
  gap: var(--spacing-2);
  border-bottom: 1px solid var(--theme-border);
  padding-bottom: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: none;
  color: var(--theme-text-secondary);
  cursor: pointer;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.tab-button:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.tab-button.active {
  background-color: var(--color-primary);
  color: white;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.health-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.metric-item {
  padding: var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-secondary);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.metric-info {
  flex: 1;
}

.metric-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.metric-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.metric-unit {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--theme-text-secondary);
}

.status-indicator {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-normal {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.status-improving {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.metric-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.trend-up {
  color: var(--color-success);
}

.trend-down {
  color: var(--color-danger);
}

.trend-stable {
  color: var(--color-gray-500);
}

.health-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.suggestion-item {
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  border-left: 4px solid;
}

.priority-high {
  border-left-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.priority-medium {
  border-left-color: var(--color-warning);
  background-color: var(--color-warning-light);
}

.priority-low {
  border-left-color: var(--color-info);
  background-color: var(--color-info-light);
}

.suggestion-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.suggestion-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.suggestion-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.priority-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: white;
}

.priority-badge.priority-high {
  background-color: var(--color-danger);
}

.priority-badge.priority-medium {
  background-color: var(--color-warning);
}

.priority-badge.priority-low {
  background-color: var(--color-info);
}

.health-score {
  display: flex;
  gap: var(--spacing-6);
  align-items: center;
}

.score-circle {
  text-align: center;
  flex-shrink: 0;
}

.score-value {
  font-size: 48px;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
}

.score-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-top: var(--spacing-1);
}

.score-breakdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.score-category {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
}

.category-score {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
}

.category-progress {
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width var(--transition-normal);
}

.progress-excellent {
  background-color: var(--color-success);
}

.progress-good {
  background-color: var(--color-info);
}

.progress-fair {
  background-color: var(--color-warning);
}

.progress-poor {
  background-color: var(--color-danger);
}

.trends-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.chart-container h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.chart-placeholder {
  text-align: center;
  padding: var(--spacing-8);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  color: var(--theme-text-secondary);
}

.trends-summary h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
}

.summary-item.improving {
  background-color: var(--color-success-light);
}

.summary-item.stable {
  background-color: var(--color-info-light);
}

.summary-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.summary-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.report-item {
  padding: var(--spacing-6);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  background-color: var(--theme-bg-secondary);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.report-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.report-meta {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.report-actions {
  display: flex;
  gap: var(--spacing-2);
}

.report-summary,
.report-findings,
.report-recommendations {
  margin-bottom: var(--spacing-4);
}

.report-summary h5,
.report-findings h5,
.report-recommendations h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.report-summary p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.report-findings ul,
.report-recommendations ul {
  margin: 0;
  padding-left: var(--spacing-4);
}

.report-findings li,
.report-recommendations li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
}

.empty-reports {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-reports h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-reports p {
  font-size: var(--font-size-base);
  margin: 0;
}

.no-member-selected {
  margin-top: var(--spacing-8);
}

.empty-state-large {
  text-align: center;
  padding: var(--spacing-12);
}

.empty-state-large .empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-state-large h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state-large p {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .health-score {
    flex-direction: column;
    text-align: center;
  }

  .trends-charts {
    grid-template-columns: 1fr;
  }

  .report-header {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .report-actions {
    justify-content: stretch;
  }

  .report-actions .btn {
    flex: 1;
  }
}
</style>
