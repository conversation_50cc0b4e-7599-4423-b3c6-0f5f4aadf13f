// 通用类型定义

// 基础实体类型
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: string;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应类型
export interface PaginationResponse {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 分页数据类型
export interface PaginatedData<T = any> {
  list: T[];
  pagination: PaginationResponse;
}

// 选项类型
export interface Option<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
  children?: Option<T>[];
}

// 键值对类型
export interface KeyValue<T = any> {
  key: string;
  value: T;
}

// 坐标类型
export interface Coordinate {
  x: number;
  y: number;
}

// 尺寸类型
export interface Size {
  width: number;
  height: number;
}

// 矩形区域类型
export interface Rectangle extends Coordinate, Size {}

// 颜色类型
export interface Color {
  hex: string;
  rgb: {
    r: number;
    g: number;
    b: number;
  };
  hsl: {
    h: number;
    s: number;
    l: number;
  };
}

// 主题类型
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
    info: string;
    light: string;
    dark: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// 语言类型
export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag?: string;
}

// 时区类型
export interface Timezone {
  name: string;
  offset: number;
  abbreviation: string;
}

// 货币类型
export interface Currency {
  code: string;
  name: string;
  symbol: string;
  decimals: number;
}

// 国家/地区类型
export interface Country {
  code: string;
  name: string;
  flag?: string;
  currency?: Currency;
  timezone?: Timezone;
  languages?: Language[];
}

// 地址类型
export interface Address {
  id?: string;
  country: string;
  province: string;
  city: string;
  district?: string;
  street: string;
  detail?: string;
  postalCode?: string;
  coordinates?: Coordinate;
  isDefault?: boolean;
}

// 联系方式类型
export interface ContactMethod {
  type: 'phone' | 'email' | 'wechat' | 'qq' | 'other';
  value: string;
  label?: string;
  isPrimary?: boolean;
  isVerified?: boolean;
}

// 文件类型
export interface FileInfo {
  id: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
  extension: string;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: string;
  uploadedBy?: string;
}

// 上传进度类型
export interface UploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error' | 'cancelled';
  error?: string;
  result?: FileInfo;
}

// 搜索参数类型
export interface SearchParams {
  keyword?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    start: string;
    end: string;
  };
}

// 导出参数类型
export interface ExportParams {
  format: 'excel' | 'csv' | 'pdf' | 'json';
  fields?: string[];
  filters?: Record<string, any>;
  fileName?: string;
}

// 导入结果类型
export interface ImportResult {
  total: number;
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// 操作日志类型
export interface OperationLog {
  id: string;
  action: string;
  resource: string;
  resourceId: string;
  userId: string;
  userName: string;
  details?: Record<string, any>;
  ip?: string;
  userAgent?: string;
  createdAt: string;
}

// 系统配置类型
export interface SystemConfig {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  category?: string;
  isPublic?: boolean;
  updatedAt: string;
  updatedBy: string;
}

// 通知类型
export interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category?: string;
  userId?: string;
  isRead: boolean;
  isGlobal: boolean;
  data?: Record<string, any>;
  createdAt: string;
  readAt?: string;
}

// 权限类型
export interface Permission {
  id: string;
  name: string;
  code: string;
  description?: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

// 角色类型
export interface Role {
  id: string;
  name: string;
  code: string;
  description?: string;
  permissions: Permission[];
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

// 菜单类型
export interface Menu {
  id: string;
  title: string;
  path?: string;
  icon?: string;
  component?: string;
  parentId?: string;
  children?: Menu[];
  order: number;
  isVisible: boolean;
  permissions?: string[];
  meta?: Record<string, any>;
}

// 路由元信息类型
export interface RouteMeta {
  title?: string;
  icon?: string;
  requiresAuth?: boolean;
  roles?: string[];
  permissions?: string[];
  hidden?: boolean;
  keepAlive?: boolean;
  breadcrumb?: boolean;
}

// 面包屑项类型
export interface BreadcrumbItem {
  title: string;
  path?: string;
  icon?: string;
  disabled?: boolean;
}

// 标签页类型
export interface TabItem {
  key: string;
  title: string;
  path: string;
  icon?: string;
  closable?: boolean;
  meta?: RouteMeta;
}

// 工具函数类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Nullable<T> = T | null;

export type Optional<T> = T | undefined;

export type ValueOf<T> = T[keyof T];

export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

// 事件类型
export interface CustomEvent<T = any> {
  type: string;
  data?: T;
  timestamp: number;
  source?: string;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
  timestamp: string;
}

// 加载状态类型
export interface LoadingState {
  loading: boolean;
  error?: AppError;
  lastUpdated?: string;
}

// 异步状态类型
export interface AsyncState<T = any> extends LoadingState {
  data?: T;
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
  message?: string;
}

// 表单字段类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'date' | 'number' | 'checkbox' | 'radio' | 'upload';
  placeholder?: string;
  defaultValue?: any;
  options?: Option[];
  rules?: ValidationRule[];
  disabled?: boolean;
  hidden?: boolean;
  props?: Record<string, any>;
}

// 表单配置类型
export interface FormConfig {
  fields: FormField[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelWidth?: string | number;
  size?: 'small' | 'default' | 'large';
  showResetButton?: boolean;
  showSubmitButton?: boolean;
  submitText?: string;
  resetText?: string;
}
