<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 分析概览 -->
      <div class="analysis-overview">
        <StatCard
          title="健康评分"
          :value="healthScore.overall"
          unit="分"
          :status="healthScore.status"
          icon="Heart"
          icon-color="#EF4444"
          icon-bg-color="#FEE2E2"
          size="lg"
        />
        <StatCard
          title="风险等级"
          :value="riskLevel.level"
          unit=""
          :status="riskLevel.status"
          icon="Shield"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          size="lg"
        />
        <StatCard
          title="改善趋势"
          :value="improvementTrend.value"
          unit="%"
          :status="improvementTrend.status"
          :trend="improvementTrend.trend"
          icon="TrendingUp"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          size="lg"
        />
      </div>

      <!-- 主要分析内容 -->
      <div class="analysis-content">
        <!-- 健康趋势分析 -->
        <div class="trends-section">
          <ContentCard title="健康趋势分析" subtitle="各项健康指标的变化趋势">
            <div class="trends-grid">
              <div
                v-for="trend in healthTrends"
                :key="trend.id"
                class="trend-item"
              >
                <div class="trend-header">
                  <div class="trend-info">
                    <h4 class="trend-title">{{ trend.name }}</h4>
                    <p class="trend-description">{{ trend.description }}</p>
                  </div>
                  <div class="trend-status">
                    <span
                      class="status-indicator"
                      :class="`status-${trend.status}`"
                    >
                      {{ getStatusLabel(trend.status) }}
                    </span>
                  </div>
                </div>

                <div class="trend-chart">
                  <!-- 简化的趋势图表 -->
                  <div class="chart-container">
                    <div class="chart-line">
                      <svg viewBox="0 0 200 60" class="trend-svg">
                        <polyline
                          :points="trend.chartData"
                          :class="`trend-line trend-${trend.status}`"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </div>
                    <div class="chart-labels">
                      <span>30天前</span>
                      <span>今天</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metrics">
                  <div class="metric">
                    <span class="metric-label">当前值</span>
                    <span class="metric-value">{{ trend.currentValue }}</span>
                  </div>
                  <div class="metric">
                    <span class="metric-label">变化</span>
                    <span
                      class="metric-value"
                      :class="`trend-${trend.changeDirection}`"
                    >
                      {{
                        trend.changeDirection === "up"
                          ? "↗"
                          : trend.changeDirection === "down"
                          ? "↘"
                          : "→"
                      }}
                      {{ trend.changeValue }}
                    </span>
                  </div>
                  <div class="metric">
                    <span class="metric-label">目标</span>
                    <span class="metric-value">{{ trend.targetValue }}</span>
                  </div>
                </div>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 风险预警 -->
        <div class="risks-section">
          <ContentCard title="风险预警" subtitle="基于数据分析的健康风险评估">
            <div class="risks-list">
              <div
                v-for="risk in healthRisks"
                :key="risk.id"
                class="risk-item"
                :class="`risk-${risk.level}`"
              >
                <div class="risk-header">
                  <div class="risk-icon">
                    <span>{{ risk.icon }}</span>
                  </div>
                  <div class="risk-info">
                    <h4 class="risk-title">{{ risk.title }}</h4>
                    <p class="risk-description">{{ risk.description }}</p>
                  </div>
                  <div class="risk-level">
                    <span class="level-badge" :class="`level-${risk.level}`">
                      {{ getRiskLevelLabel(risk.level) }}
                    </span>
                  </div>
                </div>

                <div class="risk-details">
                  <div class="risk-factors">
                    <h5>风险因素：</h5>
                    <ul>
                      <li v-for="factor in risk.factors" :key="factor">
                        {{ factor }}
                      </li>
                    </ul>
                  </div>
                  <div class="risk-recommendations">
                    <h5>建议措施：</h5>
                    <ul>
                      <li
                        v-for="recommendation in risk.recommendations"
                        :key="recommendation"
                      >
                        {{ recommendation }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div v-if="healthRisks.length === 0" class="no-risks">
                <div class="no-risks-icon">✅</div>
                <h3>暂无风险预警</h3>
                <p>您的健康状况良好，请继续保持</p>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- AI分析报告 -->
        <div class="ai-report-section">
          <ContentCard
            title="AI智能分析"
            subtitle="基于机器学习的个性化健康分析"
          >
            <div class="ai-report">
              <div class="report-header">
                <div class="ai-avatar">
                  <span>🤖</span>
                </div>
                <div class="report-info">
                  <h4>智能健康分析报告</h4>
                  <p>分析时间：{{ formatDateTime(new Date()) }}</p>
                  <p>数据来源：最近30天健康记录</p>
                </div>
              </div>

              <div class="report-content">
                <div class="analysis-summary">
                  <h5>综合分析：</h5>
                  <p>
                    基于您最近30天的健康数据分析，整体健康状况呈现稳定向好的趋势。
                    血压控制良好，平均值在正常范围内；血糖水平稳定，无明显波动；
                    体重管理效果显著，已接近目标值。建议继续保持当前的生活方式和用药习惯。
                  </p>
                </div>

                <div class="key-insights">
                  <h5>关键发现：</h5>
                  <div class="insights-list">
                    <div class="insight-item">
                      <span class="insight-icon">📈</span>
                      <span class="insight-text"
                        >血压控制效果良好，较上月改善15%</span
                      >
                    </div>
                    <div class="insight-item">
                      <span class="insight-icon">⚖️</span>
                      <span class="insight-text"
                        >体重下降趋势稳定，已达到阶段性目标</span
                      >
                    </div>
                    <div class="insight-item">
                      <span class="insight-icon">💊</span>
                      <span class="insight-text"
                        >用药依从性良好，建议继续保持</span
                      >
                    </div>
                    <div class="insight-item">
                      <span class="insight-icon">🏃</span>
                      <span class="insight-text"
                        >运动频率有所提升，心率变异性改善</span
                      >
                    </div>
                  </div>
                </div>

                <div class="personalized-advice">
                  <h5>个性化建议：</h5>
                  <div class="advice-categories">
                    <div class="advice-category">
                      <h6>🍎 饮食建议</h6>
                      <ul>
                        <li>继续保持低钠饮食，每日钠摄入量控制在2000mg以下</li>
                        <li>增加富含钾的食物摄入，如香蕉、橙子等</li>
                        <li>适量增加优质蛋白质，如鱼类、豆制品</li>
                      </ul>
                    </div>
                    <div class="advice-category">
                      <h6>🏃 运动建议</h6>
                      <ul>
                        <li>保持每周3-4次中等强度有氧运动</li>
                        <li>增加力量训练，每周2次，每次30分钟</li>
                        <li>注意运动前后的血压监测</li>
                      </ul>
                    </div>
                    <div class="advice-category">
                      <h6>💊 用药建议</h6>
                      <ul>
                        <li>继续按时服用降压药，不可自行停药</li>
                        <li>定期监测血压，记录用药效果</li>
                        <li>如有不适及时联系医生调整用药</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 健康评分
const healthScore = ref({
  overall: 85,
  status: "success",
});

// 风险等级
const riskLevel = ref({
  level: "低风险",
  status: "success",
});

// 改善趋势
const improvementTrend = ref({
  value: 12,
  status: "success",
  trend: { direction: "up", value: 12, period: "本月" },
});

// 健康趋势数据
const healthTrends = ref([
  {
    id: 1,
    name: "血压控制",
    description: "收缩压/舒张压趋势",
    status: "good",
    currentValue: "125/80 mmHg",
    changeDirection: "down",
    changeValue: "5%",
    targetValue: "< 130/85",
    chartData:
      "10,45 30,40 50,35 70,30 90,25 110,20 130,18 150,15 170,12 190,10",
  },
  {
    id: 2,
    name: "血糖管理",
    description: "空腹血糖变化趋势",
    status: "normal",
    currentValue: "5.8 mmol/L",
    changeDirection: "stable",
    changeValue: "0%",
    targetValue: "< 6.1",
    chartData:
      "10,30 30,32 50,28 70,30 90,29 110,31 130,28 150,30 170,29 190,30",
  },
  {
    id: 3,
    name: "体重控制",
    description: "体重变化趋势",
    status: "improving",
    currentValue: "72.5 kg",
    changeDirection: "down",
    changeValue: "3%",
    targetValue: "70 kg",
    chartData:
      "10,50 30,48 50,45 70,42 90,40 110,38 130,36 150,34 170,32 190,30",
  },
  {
    id: 4,
    name: "心率变异",
    description: "静息心率趋势",
    status: "good",
    currentValue: "68 bpm",
    changeDirection: "down",
    changeValue: "8%",
    targetValue: "60-80",
    chartData:
      "10,40 30,38 50,36 70,34 90,32 110,30 130,28 150,26 170,24 190,22",
  },
]);

// 健康风险数据
const healthRisks = ref([
  {
    id: 1,
    title: "轻度高血压风险",
    description: "血压偶有升高，需要持续监测",
    level: "medium",
    icon: "⚠️",
    factors: [
      "年龄因素（45岁以上）",
      "家族遗传史",
      "工作压力较大",
      "运动量不足",
    ],
    recommendations: [
      "增加有氧运动频率",
      "减少钠盐摄入",
      "保持规律作息",
      "定期血压监测",
    ],
  },
]);

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    good: "良好",
    normal: "正常",
    improving: "改善中",
    warning: "需关注",
    danger: "需就医",
  };
  return labels[status] || status;
};

// 获取风险等级标签
const getRiskLevelLabel = (level: string) => {
  const labels = {
    low: "低风险",
    medium: "中风险",
    high: "高风险",
  };
  return labels[level] || level;
};

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString("zh-CN");
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
.analysis-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.trend-item {
  padding: var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  background-color: var(--theme-bg-secondary);
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.trend-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.trend-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.status-indicator {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-good {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-normal {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.status-improving {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.status-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.chart-container {
  margin-bottom: var(--spacing-4);
}

.chart-line {
  height: 60px;
  margin-bottom: var(--spacing-2);
}

.trend-svg {
  width: 100%;
  height: 100%;
}

.trend-line {
  stroke-width: 2;
}

.trend-good {
  stroke: var(--color-success);
}

.trend-normal {
  stroke: var(--color-info);
}

.trend-improving {
  stroke: var(--color-primary);
}

.trend-warning {
  stroke: var(--color-warning);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.trend-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-3);
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
}

.metric-value {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.trend-up {
  color: var(--color-success);
}

.trend-down {
  color: var(--color-danger);
}

.trend-stable {
  color: var(--color-gray-500);
}

.risks-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.risk-item {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid;
}

.risk-low {
  border-left-color: var(--color-success);
  background-color: var(--color-success-light);
}

.risk-medium {
  border-left-color: var(--color-warning);
  background-color: var(--color-warning-light);
}

.risk-high {
  border-left-color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.risk-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.risk-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.risk-info {
  flex: 1;
}

.risk-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.risk-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.level-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.level-low {
  background-color: var(--color-success);
  color: white;
}

.level-medium {
  background-color: var(--color-warning);
  color: white;
}

.level-high {
  background-color: var(--color-danger);
  color: white;
}

.risk-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.risk-factors h5,
.risk-recommendations h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.risk-factors ul,
.risk-recommendations ul {
  margin: 0;
  padding-left: var(--spacing-4);
}

.risk-factors li,
.risk-recommendations li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
}

.no-risks {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.no-risks-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.no-risks h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.no-risks p {
  font-size: var(--font-size-base);
  margin: 0;
}

.ai-report {
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
}

.report-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.ai-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.report-info h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.report-info p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.analysis-summary h5,
.key-insights h5,
.personalized-advice h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.analysis-summary p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.insight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-md);
}

.insight-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.insight-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
  line-height: var(--line-height-normal);
}

.advice-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.advice-category {
  padding: var(--spacing-4);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-md);
}

.advice-category h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.advice-category ul {
  margin: 0;
  padding-left: var(--spacing-4);
}

.advice-category li {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-normal);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-overview {
    grid-template-columns: 1fr;
  }

  .trends-grid {
    grid-template-columns: 1fr;
  }

  .trend-metrics {
    grid-template-columns: 1fr;
  }

  .risk-details {
    grid-template-columns: 1fr;
  }

  .advice-categories {
    grid-template-columns: 1fr;
  }

  .report-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
