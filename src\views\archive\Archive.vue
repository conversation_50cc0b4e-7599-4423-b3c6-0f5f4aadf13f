<template>
  <div class="page-container">
    <!-- 功能导航卡片 -->
    <div class="archive-navigation">
      <div class="nav-grid">
        <ContentCard
          v-for="item in navigationItems"
          :key="item.id"
          :title="item.title"
          :subtitle="item.subtitle"
          :icon="item.icon"
          :icon-color="item.iconColor"
          :icon-bg-color="item.iconBgColor"
          hoverable
          size="md"
          class="nav-card"
          @click="navigateTo(item.path)"
        >
          <p class="nav-description">{{ item.description }}</p>
          <div class="nav-stats">
            <span class="stat-item">
              <span class="stat-label">{{ item.stats.label }}</span>
              <span class="stat-value">{{ item.stats.value }}</span>
            </span>
          </div>
        </ContentCard>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity-section">
      <h2 class="section-title">最近活动</h2>
      <ContentCard title="档案更新记录" subtitle="最近7天的档案变更">
        <div v-if="recentActivities.length === 0" class="empty-state">
          <p>暂无最近活动记录</p>
        </div>
        <div v-else class="activity-list">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="`activity-${activity.type}`">
              <component :is="getActivityIcon(activity.type)" />
            </div>
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-time">{{ formatTime(activity.time) }}</span>
            </div>
            <button class="activity-action btn btn-sm btn-outline">查看</button>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 健康概览 -->
    <div class="health-overview-section">
      <h2 class="section-title">健康概览</h2>
      <div class="overview-grid">
        <StatCard
          v-for="metric in healthMetrics"
          :key="metric.id"
          :title="metric.title"
          :value="metric.value"
          :unit="metric.unit"
          :status="metric.status"
          :trend="metric.trend"
          :icon="metric.icon"
          :icon-color="metric.iconColor"
          :icon-bg-color="metric.iconBgColor"
          size="md"
        />
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="actions-grid">
        <ContentCard
          v-for="action in quickActions"
          :key="action.id"
          :title="action.title"
          :icon="action.icon"
          :icon-color="action.iconColor"
          :icon-bg-color="action.iconBgColor"
          hoverable
          size="sm"
          class="action-card"
          @click="handleQuickAction(action)"
        >
          <p class="action-description">{{ action.description }}</p>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

const router = useRouter();

// 导航项目
const navigationItems = ref([
  {
    id: 1,
    title: "个人信息",
    subtitle: "基本资料管理",
    description: "管理个人基本信息、紧急联系人和医疗信息",
    icon: "User",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
    path: "/archive/personal-info",
    stats: { label: "完整度", value: "85%" },
  },
  {
    id: 2,
    title: "健康档案中心",
    subtitle: "档案文件管理",
    description: "集中管理所有健康档案文件和智能设备数据",
    icon: "Folder",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
    path: "/archive/health-center",
    stats: { label: "档案数", value: "12" },
  },
  {
    id: 3,
    title: "个人药箱",
    subtitle: "药品管理",
    description: "管理个人药品信息和用药提醒",
    icon: "Medicine",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
    path: "/archive/medicine-box",
    stats: { label: "药品数", value: "8" },
  },
  {
    id: 4,
    title: "综合健康概要",
    subtitle: "健康分析报告",
    description: "查看健康趋势分析和AI生成的健康报告",
    icon: "TrendCharts",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
    path: "/archive/health-overview",
    stats: { label: "报告数", value: "5" },
  },
  {
    id: 5,
    title: "紧急资料卡",
    subtitle: "应急信息",
    description: "紧急情况下的关键医疗和联系信息",
    icon: "Warning",
    iconColor: "#EF4444",
    iconBgColor: "#FEE2E2",
    path: "/archive/emergency-card",
    stats: { label: "状态", value: "已完善" },
  },
]);

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: "update",
    title: "更新了个人信息",
    description: "修改了紧急联系人信息",
    time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
  },
  {
    id: 2,
    type: "upload",
    title: "上传了体检报告",
    description: "添加了2024年度体检报告",
    time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前
  },
  {
    id: 3,
    type: "sync",
    title: "同步了设备数据",
    description: "血压计数据自动同步",
    time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前
  },
]);

// 健康指标
const healthMetrics = ref([
  {
    id: 1,
    title: "血压",
    value: "120/80",
    unit: "mmHg",
    status: "normal",
    trend: { direction: "stable", value: 0, period: "本周" },
    icon: "Heart",
    iconColor: "#EF4444",
    iconBgColor: "#FEE2E2",
  },
  {
    id: 2,
    title: "血糖",
    value: "5.6",
    unit: "mmol/L",
    status: "normal",
    trend: { direction: "down", value: 3, period: "本周" },
    icon: "Drop",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
  },
  {
    id: 3,
    title: "体重",
    value: "68.5",
    unit: "kg",
    status: "normal",
    trend: { direction: "up", value: 1, period: "本月" },
    icon: "Scale",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
  },
  {
    id: 4,
    title: "心率",
    value: "72",
    unit: "bpm",
    status: "normal",
    trend: { direction: "stable", value: 0, period: "本周" },
    icon: "Activity",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
  },
]);

// 快速操作
const quickActions = ref([
  {
    id: 1,
    title: "添加档案",
    description: "上传新的健康档案文件",
    icon: "Plus",
    iconColor: "#10B981",
    iconBgColor: "#D1FAE5",
    action: "add-archive",
  },
  {
    id: 2,
    title: "连接设备",
    description: "连接新的智能健康设备",
    icon: "Connection",
    iconColor: "#3B82F6",
    iconBgColor: "#DBEAFE",
    action: "connect-device",
  },
  {
    id: 3,
    title: "生成报告",
    description: "生成最新的健康分析报告",
    icon: "Document",
    iconColor: "#8B5CF6",
    iconBgColor: "#EDE9FE",
    action: "generate-report",
  },
  {
    id: 4,
    title: "导出数据",
    description: "导出健康数据备份",
    icon: "Download",
    iconColor: "#F59E0B",
    iconBgColor: "#FEF3C7",
    action: "export-data",
  },
]);

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path);
};

// 获取活动图标
const getActivityIcon = (type: string) => {
  switch (type) {
    case "update":
      return "Edit";
    case "upload":
      return "Upload";
    case "sync":
      return "Refresh";
    default:
      return "Document";
  }
};

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(hours / 24);

  if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return `${days}天前`;
  }
};

// 处理快速操作
const handleQuickAction = (action: any) => {
  console.log("执行操作:", action.action);
  // 这里可以添加具体的操作逻辑
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}
/* 页面容器样式已在 common.css 中统一定义 */

.archive-navigation {
  margin-bottom: var(--spacing-8);
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-6);
}

.nav-card {
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.nav-card:hover {
  transform: translateY(-2px);
}

.nav-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

.nav-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.recent-activity-section {
  margin-bottom: var(--spacing-8);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--theme-text-secondary);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.activity-item:hover {
  background-color: var(--theme-bg-secondary);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-update {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

.activity-upload {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.activity-sync {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-1) 0;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-1) 0;
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.health-overview-section {
  margin-bottom: var(--spacing-8);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.quick-actions-section {
  margin-bottom: var(--spacing-8);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.action-card {
  cursor: pointer;
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .activity-action {
    align-self: flex-end;
  }
}
</style>
