<template>
  <el-dialog
    v-model="dialogVisible"
    title="药品详情"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="medicine" class="medicine-details">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>药品名称：</label>
              <span>{{ medicine.name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>药品类别：</label>
              <el-tag :type="getCategoryTagType(medicine.category)">
                {{ getCategoryLabel(medicine.category) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>规格：</label>
              <span>{{ medicine.dosage }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>单位：</label>
              <span>{{ medicine.unit }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>生产厂家：</label>
              <span>{{ medicine.manufacturer }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>批号：</label>
              <span>{{ medicine.batchNumber || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 库存信息 -->
      <div class="detail-section">
        <h3 class="section-title">库存信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>当前库存：</label>
              <span :class="{ 'low-stock': medicine.quantity <= medicine.minQuantity }">
                {{ medicine.quantity }}{{ medicine.unit }}
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最小库存：</label>
              <span>{{ medicine.minQuantity }}{{ medicine.unit }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>有效期：</label>
              <span :class="getExpiryClass(medicine.expiryDate)">
                {{ medicine.expiryDate }}
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>存放位置：</label>
              <span>{{ medicine.location || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 购买信息 -->
      <div class="detail-section" v-if="medicine.price || medicine.purchaseDate">
        <h3 class="section-title">购买信息</h3>
        <el-row :gutter="20">
          <el-col :span="12" v-if="medicine.price">
            <div class="detail-item">
              <label>价格：</label>
              <span>¥{{ medicine.price }}</span>
            </div>
          </el-col>
          <el-col :span="12" v-if="medicine.purchaseDate">
            <div class="detail-item">
              <label>购买日期：</label>
              <span>{{ medicine.purchaseDate }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 用药信息 -->
      <div class="detail-section">
        <h3 class="section-title">用药信息</h3>
        <div class="detail-item">
          <label>用法用量：</label>
          <p class="detail-text">{{ medicine.instructions }}</p>
        </div>
        <div class="detail-item" v-if="medicine.sideEffects">
          <label>副作用：</label>
          <p class="detail-text">{{ medicine.sideEffects }}</p>
        </div>
        <div class="detail-item" v-if="medicine.contraindications">
          <label>禁忌症：</label>
          <p class="detail-text">{{ medicine.contraindications }}</p>
        </div>
        <div class="detail-item">
          <label>储存条件：</label>
          <p class="detail-text">{{ medicine.storage }}</p>
        </div>
        <div class="detail-item" v-if="medicine.notes">
          <label>备注：</label>
          <p class="detail-text">{{ medicine.notes }}</p>
        </div>
      </div>

      <!-- 家庭药品特有信息 -->
      <div class="detail-section" v-if="type === 'family' && isFamilyMedicine(medicine)">
        <h3 class="section-title">家庭信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>添加者：</label>
              <span>{{ getMemberName(medicine.addedBy) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>共享成员：</label>
              <span>{{ getSharedMembersText(medicine.sharedWith) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h3 class="section-title">时间信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(medicine.createdAt) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(medicine.updatedAt) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Medicine } from '@/types/medicine';
import type { FamilyMedicine } from '@/services/familyMedicineService';

interface Props {
  modelValue: boolean;
  medicine?: Medicine | FamilyMedicine | null;
  type?: 'personal' | 'family';
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'edit', medicine: Medicine | FamilyMedicine): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'personal',
  medicine: null
});

const emit = defineEmits<Emits>();

const dialogVisible = ref(false);

// 家庭成员映射（模拟数据）
const familyMembers = {
  '1': '张爸爸',
  '2': '李妈妈',
  '3': '小明'
};

const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    prescription: "处方药",
    otc: "非处方药",
    supplement: "保健品",
    other: "其他",
  };
  return labels[category] || category;
};

const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    prescription: "danger",
    otc: "success",
    supplement: "info",
    other: "warning",
  };
  return types[category] || "";
};

const getExpiryClass = (expiryDate: string) => {
  const now = new Date();
  const expiry = new Date(expiryDate);
  const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  if (expiry <= now) {
    return 'expired';
  } else if (expiry <= thirtyDaysLater) {
    return 'expiring-soon';
  }
  return '';
};

const isFamilyMedicine = (medicine: Medicine | FamilyMedicine): medicine is FamilyMedicine => {
  return 'familyId' in medicine;
};

const getMemberName = (userId: string) => {
  return familyMembers[userId as keyof typeof familyMembers] || `用户${userId}`;
};

const getSharedMembersText = (sharedWith: string[]) => {
  return sharedWith.map(id => getMemberName(id)).join('、');
};

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN');
};

const handleClose = () => {
  emit('update:modelValue', false);
};

const handleEdit = () => {
  if (props.medicine) {
    emit('edit', props.medicine);
  }
};

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
});
</script>

<style scoped>
.medicine-details {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #666;
  margin-right: 8px;
}

.detail-item span {
  color: #333;
}

.detail-text {
  margin: 0;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.low-stock {
  color: #f56c6c;
  font-weight: 500;
}

.expired {
  color: #f56c6c;
  font-weight: 500;
}

.expiring-soon {
  color: #e6a23c;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}
</style>
