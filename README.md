# 家庭健康管理系统

一款以家庭为中心、由 AI 驱动的智能健康管理平台。通过整合多源健康数据、提供个性化智能分析与建议，帮助用户全面、系统地管理家庭成员的健康。

## ✨ 特性

- 🏠 **家庭中心化管理** - 统一管理家庭成员的健康信息
- 🤖 **AI 智能分析** - 基于健康数据提供个性化建议和预测
- 📊 **健康数据可视化** - 直观展示健康趋势和指标
- 📅 **智能提醒系统** - 用药提醒、体检提醒等个性化提醒
- 🔒 **隐私安全保护** - 严格的数据加密和隐私保护机制
- 📱 **响应式设计** - 支持桌面、平板、移动设备

## 🚀 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **样式**: CSS3 + 响应式设计

## 📋 功能模块

### 核心模块

- **首页** - 健康概览、紧急警报、快速操作
- **档案** - 个人信息、健康档案、个人药箱、紧急资料卡
- **家庭** - 成员管理、权限设置、家庭药箱、家庭统计
- **助手** - AI 对话、健康分析、智能报告、健康建议
- **日历** - 健康任务管理、提醒设置
- **活动** - 健康活动记录、数据分析
- **设置** - 个性化配置、隐私设置、数据管理

### 子功能模块

- **档案管理**: 个人信息、健康档案中心、个人药箱、综合健康概要、紧急资料卡
- **家庭管理**: 家庭成员管理、家庭药箱、权限设置、家庭统计
- **智能助手**: 智能对话、健康分析、健康报告、健康建议、健康预测
- **活动管理**: 活动列表、活动时间线、活动数据分析

### 药箱管理功能

#### 个人药箱

1. **获取个人药箱药品列表** - 查看个人所有药品信息
2. **向个人药箱添加药品** - 添加新的药品到个人药箱
3. **获取个人药箱中特定药品的详情** - 查看药品详细信息
4. **更新个人药箱中的药品信息** - 编辑药品信息
5. **从个人药箱移除药品** - 删除不需要的药品
6. **调整个人药箱药品库存** - 管理药品数量

#### 家庭药箱

7. **获取家庭药箱药品列表** - 查看家庭共享药品
8. **向家庭药箱添加药品** - 添加药品供家庭成员共享
9. **获取家庭药箱中特定药品的详情** - 查看家庭药品详情
10. **更新家庭药箱中的药品信息** - 编辑家庭药品信息
11. **从家庭药箱移除药品** - 删除家庭药品
12. **调整家庭药箱药品库存** - 管理家庭药品库存

#### 附加功能

- **药品分类管理** - 处方药、非处方药、保健品等分类
- **库存预警** - 低库存和即将过期提醒
- **药品搜索和筛选** - 快速查找药品
- **数据导出** - 导出药品清单为 CSV 格式
- **统计分析** - 药品数量、分类、状态统计

## 📁 项目结构

```
src/
├── components/            # 组件目录
│   ├── common/           # 通用组件
│   │   ├── PageContainer.vue    # 页面容器组件
│   │   ├── ContentCard.vue      # 内容卡片组件
│   │   ├── StatCard.vue         # 统计卡片组件
│   │   ├── Breadcrumb.vue       # 面包屑导航组件
│   │   └── Icon.vue             # 图标组件
│   ├── layout/           # 布局组件
│   │   └── MainLayout.vue       # 主布局组件
│   ├── medicine/         # 药品管理组件
│   │   ├── AddMedicineDialog.vue      # 添加药品对话框
│   │   ├── EditMedicineDialog.vue     # 编辑药品对话框
│   │   ├── MedicineDetailsDialog.vue  # 药品详情对话框
│   │   ├── StockAdjustmentDialog.vue  # 库存调整对话框
│   │   └── MedicineBoxManager.vue     # 药箱管理器
│   ├── family/           # 家庭管理组件
│   ├── health-archive/   # 健康档案组件
│   ├── settings/         # 设置组件
│   └── charts/           # 图表组件
├── router/               # 路由配置
│   ├── index.ts         # 主路由文件
│   ├── guards.ts        # 路由守卫
│   └── modules/         # 路由模块
│       ├── auth.ts      # 认证路由
│       ├── dashboard.ts # 首页路由
│       ├── archive.ts   # 档案路由
│       ├── family.ts    # 家庭路由
│       ├── assistant.ts # 助手路由
│       ├── calendar.ts  # 日历路由
│       ├── activities.ts # 活动路由
│       └── settings.ts  # 设置路由
├── services/            # 业务服务层
│   ├── medicineService.ts       # 个人药品服务
│   ├── familyMedicineService.ts # 家庭药品服务
│   ├── familyService.ts         # 家庭管理服务
│   └── healthArchiveService.ts  # 健康档案服务
├── stores/              # 状态管理
│   └── modules/         # 状态模块
├── types/               # TypeScript类型定义
│   ├── common.ts        # 通用类型
│   ├── medicine.ts      # 药品相关类型
│   └── index.ts         # 类型导出
├── styles/              # 样式文件
│   ├── variables.css    # CSS变量定义
│   ├── reset.css        # 样式重置
│   ├── common.css       # 通用样式
│   ├── components.css   # 组件样式
│   ├── index.css        # 样式入口
│   └── themes/          # 主题样式
│       └── light.css    # 浅色主题
├── types/               # TypeScript类型定义
│   ├── index.ts         # 通用类型
│   ├── common.ts        # 基础类型
│   ├── auth.ts          # 认证类型
│   ├── user.ts          # 用户类型
│   ├── family.ts        # 家庭类型
│   ├── health.ts        # 健康类型
│   ├── calendar.ts      # 日历类型
│   └── activities.ts    # 活动类型
├── utils/               # 工具函数
│   ├── index.ts         # 工具函数入口
│   ├── format.ts        # 格式化工具
│   ├── validation.ts    # 验证工具
│   ├── storage.ts       # 存储工具
│   ├── request.ts       # 请求工具
│   ├── date.ts          # 日期工具
│   └── constants.ts     # 常量定义
├── views/               # 页面组件
│   ├── Home.vue         # 临时首页
│   ├── dashboard/       # 首页模块
│   │   └── Dashboard.vue
│   ├── archive/         # 档案模块
│   │   ├── Archive.vue
│   │   ├── PersonalInfo.vue
│   │   ├── HealthCenter.vue
│   │   ├── MedicineBox.vue          # 个人药箱
│   │   ├── HealthOverview.vue
│   │   └── EmergencyCard.vue
│   ├── family/          # 家庭模块
│   │   ├── Family.vue
│   │   └── FamilyMedicineBox.vue    # 家庭药箱
│   ├── assistant/       # 助手模块
│   │   ├── Assistant.vue
│   │   ├── SmartChat.vue
│   │   ├── HealthAnalysis.vue
│   │   ├── HealthReport.vue
│   │   ├── HealthSuggestions.vue
│   │   └── HealthPrediction.vue
│   ├── calendar/        # 日历模块
│   │   └── Calendar.vue
│   ├── activities/      # 活动模块
│   │   ├── Activities.vue
│   │   ├── ActivityList.vue
│   │   ├── ActivityTimeline.vue
│   │   └── ActivityAnalysis.vue
│   ├── settings/        # 设置模块
│   │   └── Settings.vue
│   ├── auth/            # 认证模块
│   │   ├── Login.vue
│   │   ├── Register.vue
│   │   ├── Onboarding.vue
│   │   └── FamilySetup.vue
│   └── error/           # 错误页面
│       ├── 404.vue
│       └── 403.vue
├── App.vue              # 根组件
└── main.ts              # 应用入口
```

## 🎯 已实现功能

### 核心架构

- ✅ 项目基础架构搭建
- ✅ 路由系统配置（模块化路由）
- ✅ 统一的设计系统和样式规范
- ✅ 响应式布局框架
- ✅ TypeScript 类型系统
- ✅ 组件化开发架构

### 布局和导航

- ✅ 主布局组件（侧边栏 + 主内容区）
- ✅ 响应式侧边栏导航
- ✅ 页面容器组件
- ✅ 面包屑导航支持
- ✅ 路由守卫和权限控制

### 通用组件

- ✅ PageContainer（页面容器）
- ✅ ContentCard（内容卡片）
- ✅ StatCard（统计卡片）
- ✅ 统一的组件样式系统

### 页面实现

- ✅ 首页（Dashboard）- 健康概览和快速操作
- ✅ 档案页面（Archive）- 功能导航和健康概览
- ✅ 家庭页面（Family）- 成员管理和家庭统计
- ✅ 智能助手页面（Assistant）- AI 功能导航
- ✅ 日历页面（Calendar）- 任务管理和日历视图
- ✅ 活动页面（Activities）- 活动记录和统计
- ✅ 设置页面（Settings）- 系统配置和个性化设置
- ✅ 错误页面（404/403）

### 样式系统

- ✅ CSS 变量系统（颜色、字体、间距等）
- ✅ 响应式设计（桌面/平板/移动端）
- ✅ 浅色主题实现
- ✅ 组件样式规范
- ✅ 工具类样式系统

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🔧 开发规范

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API 最佳实践
- 组件采用 `<script setup>` 语法
- 统一的代码格式化和 ESLint 规则
- 组件和函数命名采用驼峰命名法
- 文件命名采用 PascalCase（组件）或 kebab-case（工具文件）

### 目录规范

- `views/` - 页面级组件，按功能模块分组
- `components/` - 可复用组件，按类型分组
- `router/` - 路由配置，采用模块化管理
- `stores/` - 状态管理，按功能模块分组
- `utils/` - 工具函数，按功能分类
- `types/` - TypeScript 类型定义，按模块分组
- `styles/` - 样式文件，采用 CSS 变量和模块化

### 组件开发规范

- 组件名称使用 PascalCase
- Props 定义使用 TypeScript 接口
- 事件命名使用 kebab-case
- 样式使用 scoped 作用域
- 组件文档使用 JSDoc 注释

## 🚧 后续开发计划

### 第一阶段：核心功能完善

- [ ] 用户认证系统（登录/注册/权限管理）
- [ ] 状态管理系统（Pinia stores）
- [ ] API 接口层设计和实现
- [ ] 数据持久化方案
- [ ] 图表组件库集成

### 第二阶段：业务功能实现

- [ ] 健康数据录入和管理
- [ ] 智能设备数据同步
- [ ] AI 健康分析引擎
- [ ] 健康报告生成系统
- [ ] 提醒和通知系统

### 第三阶段：高级功能

- [ ] 数据可视化和趋势分析
- [ ] 家庭成员权限管理
- [ ] 数据导入导出功能
- [ ] 多语言国际化支持
- [ ] 深色主题支持

### 第四阶段：优化和扩展

- [ ] 性能优化和代码分割
- [ ] PWA 支持（离线使用）
- [ ] 移动端 App 开发
- [ ] 数据安全和隐私保护
- [ ] 第三方服务集成

## 📊 项目质量

### 代码质量

- ✅ TypeScript 严格模式
- ✅ ESLint 代码规范检查
- ✅ 组件化架构设计
- ✅ 响应式设计实现
- ✅ 无障碍功能支持

### 测试覆盖

- [ ] 单元测试（Vitest）
- [ ] 组件测试（Vue Test Utils）
- [ ] E2E 测试（Playwright）
- [ ] 性能测试
- [ ] 兼容性测试

### 文档完整性

- ✅ 项目说明文档
- ✅ 开发规范文档
- ✅ 目录结构文档
- ✅ 设计系统文档
- [ ] API 接口文档
- [ ] 部署指南文档

## 📄 许可证

MIT License
