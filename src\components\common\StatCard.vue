<template>
  <div class="stat-card" :class="cardClasses">
    <div class="stat-card-content">
      <!-- 图标区域 -->
      <div v-if="icon" class="stat-icon" :style="iconStyle">
        <component :is="icon" />
      </div>
      
      <!-- 数据区域 -->
      <div class="stat-data">
        <div class="stat-value-wrapper">
          <span class="stat-value">{{ formattedValue }}</span>
          <span v-if="unit" class="stat-unit">{{ unit }}</span>
        </div>
        
        <h4 class="stat-title">{{ title }}</h4>
        
        <!-- 趋势指示器 -->
        <div v-if="trend" class="stat-trend" :class="`trend-${trend.direction}`">
          <span class="trend-icon">
            <component :is="trendIcon" />
          </span>
          <span class="trend-value">{{ trend.value }}%</span>
          <span v-if="trend.period" class="trend-period">{{ trend.period }}</span>
        </div>
        
        <!-- 描述文本 -->
        <p v-if="description" class="stat-description">{{ description }}</p>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div v-if="status" class="stat-status" :class="`status-${status}`"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface TrendData {
  direction: 'up' | 'down' | 'stable';
  value: number;
  period?: string;
}

interface Props {
  title: string;
  value: string | number;
  unit?: string;
  icon?: any;
  iconColor?: string;
  iconBgColor?: string;
  trend?: TrendData;
  status?: 'normal' | 'warning' | 'danger' | 'success';
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'bordered';
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default'
});

// 格式化数值
const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    // 如果是大数字，进行格式化
    if (props.value >= 1000000) {
      return (props.value / 1000000).toFixed(1) + 'M';
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'K';
    }
    return props.value.toLocaleString();
  }
  return props.value;
});

// 计算卡片样式类
const cardClasses = computed(() => {
  return [
    `stat-card-${props.variant}`,
    `stat-card-${props.size}`,
    {
      [`stat-card-${props.status}`]: props.status
    }
  ];
});

// 计算图标样式
const iconStyle = computed(() => {
  const style: Record<string, string> = {};
  
  if (props.iconColor) {
    style.color = props.iconColor;
  }
  
  if (props.iconBgColor) {
    style.backgroundColor = props.iconBgColor;
  }
  
  return style;
});

// 趋势图标
const trendIcon = computed(() => {
  switch (props.trend?.direction) {
    case 'up':
      return 'ArrowUp';
    case 'down':
      return 'ArrowDown';
    case 'stable':
      return 'Minus';
    default:
      return 'Minus';
  }
});
</script>

<style scoped>
.stat-card {
  position: relative;
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* 卡片变体 */
.stat-card-default {
  border: 1px solid var(--theme-border);
  box-shadow: var(--shadow-sm);
}

.stat-card-default:hover {
  box-shadow: var(--shadow-md);
}

.stat-card-minimal {
  border: none;
  box-shadow: none;
  background-color: transparent;
}

.stat-card-bordered {
  border: 2px solid var(--theme-border);
}

/* 卡片尺寸 */
.stat-card-sm .stat-card-content {
  padding: var(--spacing-4);
}

.stat-card-md .stat-card-content {
  padding: var(--spacing-6);
}

.stat-card-lg .stat-card-content {
  padding: var(--spacing-8);
}

/* 状态样式 */
.stat-card-success {
  border-left: 4px solid var(--color-success);
}

.stat-card-warning {
  border-left: 4px solid var(--color-warning);
}

.stat-card-danger {
  border-left: 4px solid var(--color-danger);
}

.stat-card-normal {
  border-left: 4px solid var(--color-info);
}

.stat-card-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
  flex-shrink: 0;
}

.stat-card-sm .stat-icon {
  width: 40px;
  height: 40px;
}

.stat-card-lg .stat-icon {
  width: 56px;
  height: 56px;
}

.stat-data {
  flex: 1;
  min-width: 0;
}

.stat-value-wrapper {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-2);
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
  line-height: var(--line-height-tight);
}

.stat-card-sm .stat-value {
  font-size: var(--font-size-xl);
}

.stat-card-lg .stat-value {
  font-size: var(--font-size-3xl);
}

.stat-unit {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-normal);
}

.stat-card-lg .stat-title {
  font-size: var(--font-size-base);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-2);
}

.trend-up {
  color: var(--color-success);
}

.trend-down {
  color: var(--color-danger);
}

.trend-stable {
  color: var(--color-gray-500);
}

.trend-icon {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.trend-period {
  color: var(--theme-text-secondary);
  margin-left: var(--spacing-1);
}

.stat-description {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.stat-status {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: var(--spacing-3);
}

.status-success {
  background-color: var(--color-success);
}

.status-warning {
  background-color: var(--color-warning);
}

.status-danger {
  background-color: var(--color-danger);
}

.status-normal {
  background-color: var(--color-info);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--spacing-3);
  }
  
  .stat-icon {
    margin-bottom: var(--spacing-2);
  }
  
  .stat-value {
    font-size: var(--font-size-xl);
  }
  
  .stat-card-lg .stat-value {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .stat-card-sm .stat-card-content,
  .stat-card-md .stat-card-content,
  .stat-card-lg .stat-card-content {
    padding: var(--spacing-4);
  }
  
  .stat-value {
    font-size: var(--font-size-lg);
  }
  
  .stat-card-lg .stat-value {
    font-size: var(--font-size-xl);
  }
}
</style>
