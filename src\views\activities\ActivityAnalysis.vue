<template>
  <div class="page-container">
    <!-- 分析筛选 -->
    <div class="analysis-filters">
      <ContentCard size="sm">
        <div class="filters-content">
          <div class="time-filter">
            <label class="filter-label">时间范围:</label>
            <select v-model="selectedTimeRange" class="filter-select">
              <option value="7">最近7天</option>
              <option value="30">最近30天</option>
              <option value="90">最近3个月</option>
              <option value="365">最近1年</option>
            </select>
          </div>

          <div class="member-filter">
            <label class="filter-label">家庭成员:</label>
            <select v-model="selectedMember" class="filter-select">
              <option value="">全部成员</option>
              <option
                v-for="member in familyMembers"
                :key="member.id"
                :value="member.id"
              >
                {{ member.name }}
              </option>
            </select>
          </div>

          <div class="type-filter">
            <label class="filter-label">活动类型:</label>
            <select v-model="selectedType" class="filter-select">
              <option value="">全部类型</option>
              <option value="medication">用药管理</option>
              <option value="exercise">运动锻炼</option>
              <option value="checkup">健康检查</option>
              <option value="appointment">医疗预约</option>
            </select>
          </div>
        </div>
      </ContentCard>
    </div>

    <!-- 分析概览 -->
    <div class="analysis-overview">
      <div class="overview-stats">
        <StatCard
          title="总活动数"
          :value="analysisStats.totalActivities"
          unit="个"
          status="normal"
          icon="Calendar"
          icon-color="#3B82F6"
          icon-bg-color="#DBEAFE"
          size="sm"
        />
        <StatCard
          title="完成率"
          :value="analysisStats.completionRate"
          unit="%"
          :status="getCompletionStatus(analysisStats.completionRate)"
          icon="CheckCircle"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          size="sm"
        />
        <StatCard
          title="平均每天"
          :value="analysisStats.averagePerDay"
          unit="个"
          status="info"
          icon="BarChart"
          icon-color="#8B5CF6"
          icon-bg-color="#EDE9FE"
          size="sm"
        />
        <StatCard
          title="连续天数"
          :value="analysisStats.consecutiveDays"
          unit="天"
          status="success"
          icon="Flame"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          size="sm"
        />
      </div>
    </div>

    <!-- 分析图表 -->
    <div class="analysis-charts">
      <!-- 活动趋势图 -->
      <div class="chart-section">
        <ContentCard title="活动趋势分析" subtitle="活动完成情况的时间趋势">
          <div class="trend-chart">
            <div class="chart-header">
              <div class="chart-controls">
                <button
                  v-for="period in chartPeriods"
                  :key="period.value"
                  class="chart-control-btn"
                  :class="{ active: selectedChartPeriod === period.value }"
                  @click="selectedChartPeriod = period.value"
                >
                  {{ period.label }}
                </button>
              </div>
            </div>

            <div class="chart-container">
              <BarChart
                :data="chartData"
                title=""
                x-axis-label="日期"
                y-axis-label="活动数量"
                height="300px"
              />
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 活动类型分布 -->
      <div class="chart-section">
        <ContentCard title="活动类型分布" subtitle="不同类型活动的占比分析">
          <PieChart
            :data="typeDistributionData"
            title=""
            height="400px"
            :show-legend="true"
          />
        </ContentCard>
      </div>
    </div>

    <!-- 详细分析 -->
    <div class="detailed-analysis">
      <!-- 完成率分析 -->
      <div class="analysis-section">
        <ContentCard title="完成率分析" subtitle="各类活动的完成情况对比">
          <div class="completion-analysis">
            <div
              v-for="analysis in completionAnalysis"
              :key="analysis.type"
              class="completion-item"
            >
              <div class="completion-header">
                <div class="completion-info">
                  <h4 class="completion-type">{{ analysis.typeName }}</h4>
                  <span class="completion-count"
                    >{{ analysis.total }}个活动</span
                  >
                </div>
                <div class="completion-rate">
                  <span class="rate-value" :class="`rate-${analysis.status}`">
                    {{ analysis.completionRate }}%
                  </span>
                </div>
              </div>

              <div class="completion-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="`progress-${analysis.status}`"
                    :style="{ width: `${analysis.completionRate}%` }"
                  ></div>
                </div>
                <div class="progress-details">
                  <span class="completed"
                    >已完成: {{ analysis.completed }}</span
                  >
                  <span class="pending">待完成: {{ analysis.pending }}</span>
                  <span class="overdue">已逾期: {{ analysis.overdue }}</span>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 时间分布分析 -->
      <div class="analysis-section">
        <ContentCard title="时间分布分析" subtitle="活动在一天中的时间分布">
          <div class="time-distribution">
            <div class="time-chart">
              <div class="time-bars">
                <div
                  v-for="hour in timeDistribution"
                  :key="hour.hour"
                  class="time-bar"
                  :style="{ height: `${hour.percentage}%` }"
                  :title="`${hour.hour}:00 - ${hour.count}个活动`"
                >
                  <div class="time-bar-value">{{ hour.count }}</div>
                </div>
              </div>
              <div class="time-labels">
                <span v-for="i in 24" :key="i">{{ i - 1 }}</span>
              </div>
            </div>

            <div class="time-insights">
              <h4>时间分布洞察</h4>
              <div class="insights-list">
                <div class="insight-item">
                  <span class="insight-icon">🌅</span>
                  <span class="insight-text"
                    >早晨7-9点是活动高峰期，主要为用药和检查</span
                  >
                </div>
                <div class="insight-item">
                  <span class="insight-icon">🌆</span>
                  <span class="insight-text">晚上19-21点运动活动较多</span>
                </div>
                <div class="insight-item">
                  <span class="insight-icon">🌙</span>
                  <span class="insight-text">夜间活动较少，作息规律良好</span>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>

      <!-- 改进建议 -->
      <div class="analysis-section">
        <ContentCard title="改进建议" subtitle="基于数据分析的优化建议">
          <div class="improvement-suggestions">
            <div
              v-for="suggestion in improvementSuggestions"
              :key="suggestion.id"
              class="suggestion-card"
              :class="`priority-${suggestion.priority}`"
            >
              <div class="suggestion-header">
                <div class="suggestion-icon">{{ suggestion.icon }}</div>
                <div class="suggestion-info">
                  <h4 class="suggestion-title">{{ suggestion.title }}</h4>
                  <p class="suggestion-description">
                    {{ suggestion.description }}
                  </p>
                </div>
                <div class="suggestion-priority">
                  <span
                    class="priority-badge"
                    :class="`priority-${suggestion.priority}`"
                  >
                    {{ getPriorityLabel(suggestion.priority) }}
                  </span>
                </div>
              </div>

              <div class="suggestion-details">
                <div class="suggestion-data">
                  <span class="data-label">当前数据:</span>
                  <span class="data-value">{{ suggestion.currentData }}</span>
                </div>
                <div class="suggestion-target">
                  <span class="target-label">目标:</span>
                  <span class="target-value">{{ suggestion.target }}</span>
                </div>
                <div class="suggestion-impact">
                  <span class="impact-label">预期改善:</span>
                  <span class="impact-value">{{
                    suggestion.expectedImprovement
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
// 组件已通过 unplugin-vue-components 自动导入，无需手动导入

// 筛选条件
const selectedTimeRange = ref("30");
const selectedMember = ref("");
const selectedType = ref("");
const selectedChartPeriod = ref("daily");

// 图表周期选项
const chartPeriods = ref([
  { value: "daily", label: "按天" },
  { value: "weekly", label: "按周" },
  { value: "monthly", label: "按月" },
]);

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 分析统计数据
const analysisStats = ref({
  totalActivities: 156,
  completionRate: 87,
  averagePerDay: 5.2,
  consecutiveDays: 15,
});

// 图表数据
const chartData = ref([
  { name: "周一", value: 8 },
  { name: "周二", value: 6 },
  { name: "周三", value: 9 },
  { name: "周四", value: 7 },
  { name: "周五", value: 10 },
  { name: "周六", value: 5 },
  { name: "周日", value: 4 },
]);

// 活动类型分布数据（用于饼图）
const typeDistributionData = ref([
  { name: "用药管理", value: 45, color: "#3B82F6" },
  { name: "运动锻炼", value: 38, color: "#10B981" },
  { name: "健康检查", value: 42, color: "#F59E0B" },
  { name: "医疗预约", value: 31, color: "#EF4444" },
]);

// 活动类型分布（保留原有数据结构用于其他地方）
const typeDistribution = ref([
  {
    name: "用药管理",
    count: 45,
    percentage: 29,
    color: "#3B82F6",
    startAngle: 0,
    endAngle: 104,
  },
  {
    name: "运动锻炼",
    count: 38,
    percentage: 24,
    color: "#10B981",
    startAngle: 104,
    endAngle: 190,
  },
  {
    name: "健康检查",
    count: 42,
    percentage: 27,
    color: "#F59E0B",
    startAngle: 190,
    endAngle: 287,
  },
  {
    name: "医疗预约",
    count: 31,
    percentage: 20,
    color: "#EF4444",
    startAngle: 287,
    endAngle: 360,
  },
]);

// 完成率分析
const completionAnalysis = ref([
  {
    type: "medication",
    typeName: "用药管理",
    total: 45,
    completed: 42,
    pending: 2,
    overdue: 1,
    completionRate: 93,
    status: "excellent",
  },
  {
    type: "exercise",
    typeName: "运动锻炼",
    total: 38,
    completed: 30,
    pending: 6,
    overdue: 2,
    completionRate: 79,
    status: "good",
  },
  {
    type: "checkup",
    typeName: "健康检查",
    total: 42,
    completed: 38,
    pending: 3,
    overdue: 1,
    completionRate: 90,
    status: "excellent",
  },
  {
    type: "appointment",
    typeName: "医疗预约",
    total: 31,
    completed: 26,
    pending: 4,
    overdue: 1,
    completionRate: 84,
    status: "good",
  },
]);

// 时间分布数据
const timeDistribution = ref([
  { hour: 7, count: 12, percentage: 80 },
  { hour: 8, count: 15, percentage: 100 },
  { hour: 9, count: 8, percentage: 53 },
  { hour: 12, count: 6, percentage: 40 },
  { hour: 14, count: 4, percentage: 27 },
  { hour: 19, count: 10, percentage: 67 },
  { hour: 20, count: 8, percentage: 53 },
  { hour: 21, count: 5, percentage: 33 },
]);

// 改进建议
const improvementSuggestions = ref([
  {
    id: 1,
    title: "提高运动活动完成率",
    description: "运动锻炼的完成率相对较低，建议优化运动计划",
    icon: "🏃",
    priority: "high",
    currentData: "79%完成率",
    target: "90%以上",
    expectedImprovement: "提升11%",
  },
  {
    id: 2,
    title: "平衡活动时间分布",
    description: "活动主要集中在早晚，可以适当分散到其他时间",
    icon: "⏰",
    priority: "medium",
    currentData: "70%集中在早晚",
    target: "更均匀分布",
    expectedImprovement: "减少时间冲突",
  },
  {
    id: 3,
    title: "增加周末活动",
    description: "周末活动数量偏少，可以增加一些轻松的健康活动",
    icon: "📅",
    priority: "low",
    currentData: "周末平均4.5个",
    target: "6个以上",
    expectedImprovement: "提升33%",
  },
]);

// 获取完成率状态
const getCompletionStatus = (rate: number) => {
  if (rate >= 90) return "success";
  if (rate >= 80) return "warning";
  return "danger";
};

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels = {
    high: "高",
    medium: "中",
    low: "低",
  };
  return labels[priority] || priority;
};
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  width: 100%;
}

.analysis-filters {
  margin-bottom: var(--spacing-6);
}

.filters-content {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
  flex-wrap: wrap;
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-right: var(--spacing-2);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

.analysis-overview {
  margin-bottom: var(--spacing-6);
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.analysis-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.chart-header {
  margin-bottom: var(--spacing-4);
}

.chart-controls {
  display: flex;
  gap: var(--spacing-2);
}

.chart-control-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
}

.chart-control-btn:hover {
  background-color: var(--theme-bg-secondary);
}

.chart-control-btn.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.chart-placeholder {
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 200px;
  margin-bottom: var(--spacing-3);
  gap: var(--spacing-2);
}

.chart-bar {
  flex: 1;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  position: relative;
  min-height: 20px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: all var(--transition-normal);
}

.chart-bar:hover {
  background-color: var(--color-primary-dark);
}

.bar-value {
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.type-distribution {
  display: flex;
  gap: var(--spacing-6);
  align-items: center;
}

.distribution-chart {
  position: relative;
  width: 150px;
  height: 150px;
  flex-shrink: 0;
}

.pie-chart {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  background: conic-gradient(
    #3b82f6 0deg 104deg,
    #10b981 104deg 190deg,
    #f59e0b 190deg 287deg,
    #ef4444 287deg 360deg
  );
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: var(--theme-bg-primary);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pie-total {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
}

.pie-label {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.distribution-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--border-radius-sm);
  flex-shrink: 0;
}

.legend-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.legend-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.legend-value {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.detailed-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.completion-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.completion-item {
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.completion-type {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0;
}

.completion-count {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.rate-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.rate-excellent {
  color: var(--color-success);
}

.rate-good {
  color: var(--color-info);
}

.rate-fair {
  color: var(--color-warning);
}

.rate-poor {
  color: var(--color-danger);
}

.completion-progress {
  margin-bottom: var(--spacing-3);
}

.progress-bar {
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.progress-fill {
  height: 100%;
  transition: width var(--transition-normal);
}

.progress-excellent {
  background-color: var(--color-success);
}

.progress-good {
  background-color: var(--color-info);
}

.progress-fair {
  background-color: var(--color-warning);
}

.progress-poor {
  background-color: var(--color-danger);
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.time-distribution {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.time-chart {
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
}

.time-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 120px;
  margin-bottom: var(--spacing-2);
  gap: var(--spacing-1);
}

.time-bar {
  flex: 1;
  background-color: var(--color-info);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  position: relative;
  min-height: 10px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.time-bar-value {
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacing-1);
}

.time-labels {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.time-insights h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.insight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
}

.insight-icon {
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.insight-text {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.improvement-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.suggestion-card {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid;
  background-color: var(--theme-bg-secondary);
}

.priority-high {
  border-left-color: var(--color-danger);
}

.priority-medium {
  border-left-color: var(--color-warning);
}

.priority-low {
  border-left-color: var(--color-info);
}

.suggestion-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.suggestion-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.suggestion-info {
  flex: 1;
}

.suggestion-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.suggestion-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.priority-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: white;
}

.priority-badge.priority-high {
  background-color: var(--color-danger);
}

.priority-badge.priority-medium {
  background-color: var(--color-warning);
}

.priority-badge.priority-low {
  background-color: var(--color-info);
}

.suggestion-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-3);
}

.suggestion-data,
.suggestion-target,
.suggestion-impact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  padding: var(--spacing-2);
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-sm);
}

.data-label,
.target-label,
.impact-label {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.data-value,
.target-value,
.impact-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .time-filter,
  .member-filter,
  .type-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .filter-select {
    min-width: 150px;
  }

  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .analysis-charts {
    grid-template-columns: 1fr;
  }

  .type-distribution {
    flex-direction: column;
    text-align: center;
  }

  .suggestion-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }

  .suggestion-details {
    grid-template-columns: 1fr;
  }
}
</style>
