<template>
  <div class="register-page">
    <div class="register-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
            </div>
            <h1 class="logo-text">家庭健康管理</h1>
          </div>
          <p class="brand-slogan">开始您的健康管理之旅</p>
          
          <!-- 注册步骤指引 -->
          <div class="steps-guide">
            <div class="step-item" :class="{ active: currentStep >= 1 }">
              <div class="step-number">1</div>
              <div class="step-text">创建账户</div>
            </div>
            <div class="step-item" :class="{ active: currentStep >= 2 }">
              <div class="step-number">2</div>
              <div class="step-text">验证手机</div>
            </div>
            <div class="step-item" :class="{ active: currentStep >= 3 }">
              <div class="step-number">3</div>
              <div class="step-text">完善信息</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">创建新账户</h2>
            <p class="form-subtitle">请填写以下信息完成注册</p>
          </div>

          <form @submit.prevent="handleRegister" class="register-form">
            <!-- 用户名输入 -->
            <div class="form-group">
              <label for="username" class="form-label">用户名</label>
              <input
                id="username"
                v-model="registerForm.username"
                type="text"
                class="form-input"
                placeholder="请输入用户名"
                required
              />
            </div>

            <!-- 手机号输入 -->
            <div class="form-group">
              <label for="phone" class="form-label">手机号</label>
              <input
                id="phone"
                v-model="registerForm.phone"
                type="tel"
                class="form-input"
                placeholder="请输入手机号"
                required
              />
            </div>

            <!-- 验证码输入 -->
            <div class="form-group">
              <label for="verifyCode" class="form-label">验证码</label>
              <div class="verify-code-wrapper">
                <input
                  id="verifyCode"
                  v-model="registerForm.verifyCode"
                  type="text"
                  class="form-input"
                  placeholder="请输入验证码"
                  required
                />
                <button
                  type="button"
                  class="send-code-btn"
                  :disabled="countdown > 0"
                  @click="sendVerifyCode"
                >
                  <span v-if="countdown > 0">{{ countdown }}s</span>
                  <span v-else>发送验证码</span>
                </button>
              </div>
            </div>

            <!-- 密码输入 -->
            <div class="form-group">
              <label for="password" class="form-label">密码</label>
              <div class="password-input-wrapper">
                <input
                  id="password"
                  v-model="registerForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  placeholder="请输入密码"
                  required
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <span v-if="showPassword">👁️</span>
                  <span v-else>👁️‍🗨️</span>
                </button>
              </div>
              <div class="password-strength">
                <div class="strength-bar">
                  <div 
                    class="strength-fill" 
                    :class="`strength-${passwordStrength}`"
                    :style="{ width: `${passwordStrengthPercent}%` }"
                  ></div>
                </div>
                <span class="strength-text">{{ passwordStrengthText }}</span>
              </div>
            </div>

            <!-- 确认密码输入 -->
            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认密码</label>
              <input
                id="confirmPassword"
                v-model="registerForm.confirmPassword"
                type="password"
                class="form-input"
                placeholder="请再次输入密码"
                required
              />
            </div>

            <!-- 用户协议 -->
            <div class="form-group">
              <label class="checkbox-wrapper">
                <input
                  v-model="registerForm.agreeTerms"
                  type="checkbox"
                  class="checkbox"
                  required
                />
                <span class="checkbox-label">
                  我已阅读并同意
                  <a href="#" class="terms-link">《用户协议》</a>
                  和
                  <a href="#" class="terms-link">《隐私政策》</a>
                </span>
              </label>
            </div>

            <!-- 注册按钮 -->
            <button
              type="submit"
              class="register-btn"
              :disabled="loading || !isFormValid"
            >
              <span v-if="loading">注册中...</span>
              <span v-else>立即注册</span>
            </button>

            <!-- 微信快速注册 -->
            <div class="divider">
              <span class="divider-text">或</span>
            </div>

            <button
              type="button"
              class="wechat-register-btn"
              @click="handleWechatRegister"
            >
              <span class="wechat-icon">💬</span>
              微信快速注册
            </button>
          </form>

          <!-- 登录链接 -->
          <div class="form-footer">
            <p class="login-prompt">
              已有账户？
              <router-link to="/login" class="login-link">立即登录</router-link>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 表单数据
const registerForm = ref({
  username: '',
  phone: '',
  verifyCode: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
});

// 状态
const showPassword = ref(false);
const loading = ref(false);
const countdown = ref(0);
const currentStep = ref(1);

// 密码强度计算
const passwordStrength = computed(() => {
  const password = registerForm.value.password;
  if (!password) return 'weak';
  
  let score = 0;
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;
  
  if (score <= 2) return 'weak';
  if (score <= 3) return 'medium';
  return 'strong';
});

const passwordStrengthPercent = computed(() => {
  const strength = passwordStrength.value;
  if (strength === 'weak') return 33;
  if (strength === 'medium') return 66;
  return 100;
});

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value;
  if (strength === 'weak') return '弱';
  if (strength === 'medium') return '中';
  return '强';
});

// 表单验证
const isFormValid = computed(() => {
  return (
    registerForm.value.username &&
    registerForm.value.phone &&
    registerForm.value.verifyCode &&
    registerForm.value.password &&
    registerForm.value.confirmPassword &&
    registerForm.value.password === registerForm.value.confirmPassword &&
    registerForm.value.agreeTerms
  );
});

// 发送验证码
const sendVerifyCode = () => {
  if (!registerForm.value.phone) {
    alert('请先输入手机号');
    return;
  }
  
  // 模拟发送验证码
  console.log('发送验证码到:', registerForm.value.phone);
  
  // 开始倒计时
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 注册处理
const handleRegister = async () => {
  if (!isFormValid.value) {
    alert('请完善所有必填信息');
    return;
  }
  
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    alert('两次输入的密码不一致');
    return;
  }
  
  loading.value = true;
  
  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟注册成功
    console.log('注册成功:', registerForm.value);
    
    // 跳转到引导页面
    router.push('/onboarding');
  } catch (error) {
    console.error('注册失败:', error);
  } finally {
    loading.value = false;
  }
};

// 微信注册处理
const handleWechatRegister = () => {
  console.log('微信注册');
  // 这里应该调用微信注册API
  // 模拟注册成功后跳转
  router.push('/onboarding');
};
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  margin: var(--spacing-8);
}

.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: var(--spacing-12);
  display: flex;
  align-items: center;
}

.brand-content {
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.logo-icon {
  width: 48px;
  height: 48px;
  color: white;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.brand-slogan {
  font-size: var(--font-size-lg);
  margin: 0 0 var(--spacing-8) 0;
  opacity: 0.9;
}

.steps-guide {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.step-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  opacity: 0.5;
  transition: opacity var(--transition-normal);
}

.step-item.active {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
}

.step-item.active .step-number {
  background-color: white;
  color: var(--color-primary);
}

.step-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.form-section {
  flex: 1;
  padding: var(--spacing-12);
  display: flex;
  align-items: center;
  overflow-y: auto;
}

.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.form-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.form-subtitle {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.form-input {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: border-color var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.verify-code-wrapper {
  display: flex;
  gap: var(--spacing-2);
}

.verify-code-wrapper .form-input {
  flex: 1;
}

.send-code-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  white-space: nowrap;
  transition: background-color var(--transition-normal);
}

.send-code-btn:hover:not(:disabled) {
  background-color: var(--color-secondary-dark);
}

.send-code-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  color: var(--theme-text-secondary);
}

.password-strength {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-1);
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: width var(--transition-normal);
}

.strength-weak {
  background-color: var(--color-danger);
}

.strength-medium {
  background-color: var(--color-warning);
}

.strength-strong {
  background-color: var(--color-success);
}

.strength-text {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  min-width: 20px;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.checkbox-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  line-height: var(--line-height-normal);
}

.terms-link {
  color: var(--color-primary);
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.register-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.register-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.divider {
  position: relative;
  text-align: center;
  margin: var(--spacing-4) 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--theme-border);
}

.divider-text {
  background-color: var(--theme-bg-primary);
  padding: 0 var(--spacing-4);
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
}

.wechat-register-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  transition: background-color var(--transition-normal);
}

.wechat-register-btn:hover {
  background-color: #06ad56;
}

.wechat-icon {
  font-size: var(--font-size-lg);
}

.form-footer {
  text-align: center;
  margin-top: var(--spacing-6);
}

.login-prompt {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.login-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.login-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-container {
    flex-direction: column;
    margin: var(--spacing-4);
  }
  
  .brand-section {
    padding: var(--spacing-8);
  }
  
  .form-section {
    padding: var(--spacing-8);
  }
  
  .steps-guide {
    display: none;
  }
}
</style>
