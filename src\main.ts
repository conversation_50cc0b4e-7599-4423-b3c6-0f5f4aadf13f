import { createApp } from "vue";
import "./styles/index.css";
import App from "./App.vue";
import router from "./router";
import * as ElementPlusIcons from "@element-plus/icons-vue";
import "element-plus/dist/index.css";

const app = createApp(App);

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIcons)) {
  app.component(key, component);
}

app.use(router);

app.mount("#app");
