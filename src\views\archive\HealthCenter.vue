<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 顶部统计卡片 -->
      <div class="stats-overview">
        <StatCard
          title="数据总量"
          :value="healthStats.totalRecords"
          unit=""
          status="normal"
          icon="DataBoard"
          icon-color="#3B82F6"
          icon-bg-color="#DBEAFE"
          size="sm"
        />
        <StatCard
          title="存储使用"
          :value="healthStats.storageUsed"
          unit=""
          status="normal"
          icon="FolderOpened"
          icon-color="#10B981"
          icon-bg-color="#D1FAE5"
          size="sm"
        />
        <StatCard
          title="活跃设备"
          :value="healthStats.activeDevices"
          unit=""
          status="success"
          icon="Smartphone"
          icon-color="#F59E0B"
          icon-bg-color="#FEF3C7"
          size="sm"
        />
        <StatCard
          title="本月新增"
          :value="healthStats.monthlyNew"
          unit=""
          status="info"
          icon="Plus"
          icon-color="#8B5CF6"
          icon-bg-color="#EDE9FE"
          size="sm"
        />
      </div>

      <!-- 主要功能区域 -->
      <div class="main-content">
        <!-- 快速操作区域 -->
        <div class="quick-actions">
          <div class="actions-header">
            <h3 class="section-title">快速操作</h3>
            <div class="actions-buttons">
              <el-button type="primary" @click="showUploadDialog = true">
                <el-icon><Upload /></el-icon>
                上传报告
              </el-button>
              <el-button @click="showSyncDialog = true">
                <el-icon><Refresh /></el-icon>
                同步数据
              </el-button>
              <el-button @click="showFilterDialog = true">
                <el-icon><Filter /></el-icon>
                数据筛选
              </el-button>
              <el-button @click="showReportListDialog = true">
                <el-icon><Document /></el-icon>
                报告管理
              </el-button>
              <el-button @click="showTrendAnalysisDialog = true">
                <el-icon><TrendCharts /></el-icon>
                趋势分析
              </el-button>
              <el-button @click="showReminderDialog = true">
                <el-icon><Bell /></el-icon>
                健康提醒
              </el-button>
            </div>
          </div>
        </div>

        <!-- 健康档案管理 -->
        <div class="archives-section">
          <ContentCard
            title="健康档案管理"
            subtitle="管理所有家庭成员的健康记录文件"
          >
            <template #actions>
              <button
                class="btn btn-primary"
                @click="showAddArchiveDialog = true"
              >
                添加档案
              </button>
            </template>

            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <div class="search-bar">
                <input
                  v-model="searchQuery"
                  type="text"
                  class="search-input"
                  placeholder="搜索档案名称..."
                />
                <button class="search-btn">
                  <span>🔍</span>
                </button>
              </div>

              <div class="filters">
                <select v-model="filters.member" class="filter-select">
                  <option value="">全部成员</option>
                  <option
                    v-for="member in familyMembers"
                    :key="member.id"
                    :value="member.id"
                  >
                    {{ member.name }}
                  </option>
                </select>

                <select v-model="filters.type" class="filter-select">
                  <option value="">全部类型</option>
                  <option value="checkup">体检报告</option>
                  <option value="lab">化验单</option>
                  <option value="imaging">影像资料</option>
                  <option value="prescription">处方单</option>
                </select>

                <select v-model="filters.status" class="filter-select">
                  <option value="">全部状态</option>
                  <option value="parsed">已解析</option>
                  <option value="abnormal">有异常</option>
                  <option value="normal">正常</option>
                </select>

                <button
                  class="btn btn-outline"
                  @click="showAdvancedFilters = !showAdvancedFilters"
                >
                  筛选
                </button>
              </div>
            </div>

            <!-- 档案列表 -->
            <div class="archives-table">
              <div class="table-header">
                <div class="header-cell">档案名称</div>
                <div class="header-cell">日期</div>
                <div class="header-cell">类型</div>
                <div class="header-cell">成员</div>
                <div class="header-cell">状态</div>
                <div class="header-cell">大小</div>
                <div class="header-cell">操作</div>
              </div>

              <div class="table-body">
                <div
                  v-for="archive in filteredArchives"
                  :key="archive.id"
                  class="table-row"
                >
                  <div class="cell archive-name">
                    <div class="file-icon">📄</div>
                    <div class="file-info">
                      <div class="file-title">{{ archive.name }}</div>
                      <div class="file-pages">{{ archive.pages }}页</div>
                    </div>
                  </div>
                  <div class="cell">{{ formatDate(archive.date) }}</div>
                  <div class="cell">
                    <span class="type-badge" :class="`type-${archive.type}`">
                      {{ getTypeLabel(archive.type) }}
                    </span>
                  </div>
                  <div class="cell">{{ getMemberName(archive.memberId) }}</div>
                  <div class="cell">
                    <span
                      class="status-badge"
                      :class="`status-${archive.status}`"
                    >
                      {{ getStatusLabel(archive.status) }}
                    </span>
                  </div>
                  <div class="cell">{{ archive.size }}</div>
                  <div class="cell actions">
                    <button class="action-btn" @click="viewArchive(archive)">
                      查看
                    </button>
                    <button class="action-btn" @click="editArchive(archive)">
                      编辑
                    </button>
                    <button
                      class="action-btn"
                      @click="downloadArchive(archive)"
                    >
                      下载
                    </button>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination">
                <button
                  class="page-btn"
                  :disabled="currentPage === 1"
                  @click="currentPage--"
                >
                  上一页
                </button>
                <span class="page-info"
                  >第 {{ currentPage }} 页，共 {{ totalPages }} 页</span
                >
                <button
                  class="page-btn"
                  :disabled="currentPage === totalPages"
                  @click="currentPage++"
                >
                  下一页
                </button>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 设备连接管理 -->
        <div class="devices-section">
          <ContentCard title="设备连接管理" subtitle="管理已连接的智能健康设备">
            <template #actions>
              <button
                class="btn btn-primary"
                @click="showAddDeviceDialog = true"
              >
                添加设备
              </button>
            </template>

            <div class="devices-grid">
              <div
                v-for="device in connectedDevices"
                :key="device.id"
                class="device-card"
                :class="{ offline: device.status === 'offline' }"
              >
                <div class="device-header">
                  <div class="device-icon">{{ device.icon }}</div>
                  <div class="device-info">
                    <h4 class="device-name">{{ device.name }}</h4>
                    <p class="device-model">{{ device.model }}</p>
                  </div>
                  <div class="device-status">
                    <span
                      class="status-dot"
                      :class="`status-${device.status}`"
                    ></span>
                    <span class="status-text">{{
                      getDeviceStatusLabel(device.status)
                    }}</span>
                  </div>
                </div>

                <div class="device-details">
                  <div class="detail-item">
                    <span class="detail-label">最后同步</span>
                    <span class="detail-value">{{
                      formatDateTime(device.lastSync)
                    }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">数据条数</span>
                    <span class="detail-value">{{ device.dataCount }}条</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">电量</span>
                    <div class="battery-indicator">
                      <div
                        class="battery-level"
                        :style="{ width: `${device.battery}%` }"
                      ></div>
                      <span class="battery-text">{{ device.battery }}%</span>
                    </div>
                  </div>
                </div>

                <div class="device-actions">
                  <button
                    class="btn btn-sm btn-outline"
                    @click="syncDevice(device)"
                  >
                    同步数据
                  </button>
                  <button
                    class="btn btn-sm btn-outline"
                    @click="configDevice(device)"
                  >
                    设置
                  </button>
                </div>
              </div>

              <div v-if="connectedDevices.length === 0" class="empty-devices">
                <div class="empty-icon">📱</div>
                <h3>暂无连接设备</h3>
                <p>添加智能健康设备开始自动同步数据</p>
                <button
                  class="btn btn-primary"
                  @click="showAddDeviceDialog = true"
                >
                  添加第一个设备
                </button>
              </div>
            </div>
          </ContentCard>
        </div>

        <!-- 健康时间线 -->
        <div class="timeline-section">
          <ContentCard title="综合活动时间线" subtitle="最近的健康活动记录">
            <template #actions>
              <el-button
                size="small"
                @click="refreshTimeline"
                :loading="timelineLoading"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button size="small" @click="showFilterDialog = true">
                <el-icon><Filter /></el-icon>
                筛选
              </el-button>
            </template>

            <div v-loading="timelineLoading" class="timeline-container">
              <div
                v-for="item in timelineData"
                :key="item.id"
                class="timeline-item"
                :class="`status-${item.status}`"
                @click="viewTimelineDetail(item)"
              >
                <div class="timeline-time">
                  {{ formatTimelineDate(item.timestamp) }}
                </div>
                <div class="timeline-icon">
                  <el-icon>
                    <component :is="getTimelineIcon(item.type)" />
                  </el-icon>
                </div>
                <div class="timeline-content">
                  <div class="timeline-title">{{ item.title }}</div>
                  <div class="timeline-description">{{ item.description }}</div>
                  <div class="timeline-meta">
                    <el-tag :type="getTypeColor(item.type)" size="small">
                      {{ getTypeName(item.type) }}
                    </el-tag>
                    <span class="timeline-source">{{ item.source }}</span>
                  </div>
                </div>
                <div class="timeline-status">
                  <el-tag :type="getStatusColor(item.status)" size="small">
                    {{ getStatusName(item.status) }}
                  </el-tag>
                </div>
              </div>

              <!-- 加载更多 -->
              <div v-if="hasMoreTimeline" class="timeline-load-more">
                <el-button
                  @click="loadMoreTimeline"
                  :loading="loadingMoreTimeline"
                >
                  加载更多
                </el-button>
              </div>

              <!-- 空状态 -->
              <div
                v-if="!timelineLoading && timelineData.length === 0"
                class="timeline-empty"
              >
                <el-empty description="暂无活动记录" />
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <HealthReportUploadDialog
      v-model="showUploadDialog"
      @success="handleUploadSuccess"
      @view-report="handleViewReport"
    />

    <HealthReportListDialog v-model="showReportListDialog" />

    <HealthReportDetailDialog
      v-model="showReportDetailDialog"
      :report-id="selectedReportId"
      @updated="refreshData"
    />

    <HealthDataSyncDialog
      v-model="showSyncDialog"
      @success="handleSyncSuccess"
    />

    <HealthDataFilterDialog v-model="showFilterDialog" />

    <HealthTrendAnalysisDialog v-model="showTrendAnalysisDialog" />

    <HealthReminderDialog v-model="showReminderDialog" />

    <!-- 档案管理对话框 -->
    <AddArchiveDialog
      v-model="showAddArchiveDialog"
      @success="handleArchiveAdded"
    />

    <EditArchiveDialog
      v-model="showEditArchiveDialog"
      :archive="selectedArchive"
      @updated="handleArchiveUpdated"
      @deleted="handleArchiveDeleted"
    />

    <!-- 设备管理对话框 -->
    <AddDeviceDialog
      v-model="showAddDeviceDialog"
      @success="handleDeviceAdded"
    />

    <DeviceConfigDialog
      v-model="showDeviceConfigDialog"
      :device="selectedDevice"
      @updated="handleDeviceUpdated"
    />

    <!-- 设置对话框 -->
    <HealthArchiveSettingsDialog
      v-model="showSettingsDialog"
      @settings-changed="handleSettingsChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Upload,
  Refresh,
  Filter,
  Document,
  User,
  Monitor,
  Coffee,
  Clock,
  CircleCheck,
  Warning,
  TrendCharts,
  Bell,
} from "@element-plus/icons-vue";
import {
  healthArchiveService,
  type HealthTimelineItem,
} from "@/services/healthArchiveService";
import HealthReportUploadDialog from "@/components/health-archive/HealthReportUploadDialog.vue";
import HealthReportListDialog from "@/components/health-archive/HealthReportListDialog.vue";
import HealthReportDetailDialog from "@/components/health-archive/HealthReportDetailDialog.vue";
import HealthDataSyncDialog from "@/components/health-archive/HealthDataSyncDialog.vue";
import HealthDataFilterDialog from "@/components/health-archive/HealthDataFilterDialog.vue";
import HealthTrendAnalysisDialog from "@/components/health-archive/HealthTrendAnalysisDialog.vue";
import HealthReminderDialog from "@/components/health-archive/HealthReminderDialog.vue";
import AddArchiveDialog from "@/components/health-archive/AddArchiveDialog.vue";
import EditArchiveDialog from "@/components/health-archive/EditArchiveDialog.vue";
import AddDeviceDialog from "@/components/health-archive/AddDeviceDialog.vue";
import DeviceConfigDialog from "@/components/health-archive/DeviceConfigDialog.vue";
import HealthArchiveSettingsDialog from "@/components/health-archive/HealthArchiveSettingsDialog.vue";
// 组件通过 unplugin-vue-components 自动导入

// 对话框状态
const showUploadDialog = ref(false);
const showReportListDialog = ref(false);
const showReportDetailDialog = ref(false);
const showSyncDialog = ref(false);
const showFilterDialog = ref(false);
const showTrendAnalysisDialog = ref(false);
const showReminderDialog = ref(false);

// 选中的报告ID
const selectedReportId = ref<string>("");

// 档案管理状态
const showEditArchiveDialog = ref(false);
const selectedArchive = ref<any>(null);

// 设备管理状态
const showDeviceConfigDialog = ref(false);
const selectedDevice = ref<any>(null);

// 设置对话框状态
const showSettingsDialog = ref(false);

// 全局状态
const globalLoading = ref(false);
const operationInProgress = ref(false);
const errorMessage = ref("");

// 时间线数据
const timelineData = ref<HealthTimelineItem[]>([]);
const timelineLoading = ref(false);
const loadingMoreTimeline = ref(false);
const hasMoreTimeline = ref(true);
const timelineLimit = ref(20);

// 健康统计数据
const healthStats = ref({
  totalRecords: "1.2K+",
  storageUsed: "2.3GB",
  activeDevices: "5台",
  monthlyNew: "23份",
});

// 搜索和筛选
const searchQuery = ref("");
const showAdvancedFilters = ref(false);
const filters = ref({
  member: "",
  type: "",
  status: "",
});

// 分页
const currentPage = ref(1);
const pageSize = 10;

// 对话框状态
const showAddArchiveDialog = ref(false);
const showAddDeviceDialog = ref(false);

// 家庭成员
const familyMembers = ref([
  { id: 1, name: "张先生" },
  { id: 2, name: "李女士" },
  { id: 3, name: "张小明" },
]);

// 健康档案数据
const archives = ref([
  {
    id: 1,
    name: "年度体检报告",
    date: new Date("2024-01-15"),
    type: "checkup",
    memberId: 1,
    status: "parsed",
    size: "2.3MB",
    pages: 8,
  },
  {
    id: 2,
    name: "血液检查结果",
    date: new Date("2024-01-10"),
    type: "lab",
    memberId: 1,
    status: "abnormal",
    size: "1.2MB",
    pages: 3,
  },
  {
    id: 3,
    name: "心电图检查",
    date: new Date("2024-01-08"),
    type: "imaging",
    memberId: 2,
    status: "normal",
    size: "0.8MB",
    pages: 2,
  },
]);

// 连接的设备
const connectedDevices = ref([
  {
    id: 1,
    name: "智能血压计",
    model: "BP-2000",
    icon: "🩺",
    status: "connected",
    lastSync: new Date(),
    dataCount: 156,
    battery: 85,
  },
  {
    id: 2,
    name: "智能体重秤",
    model: "WS-3000",
    icon: "⚖️",
    status: "connected",
    lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
    dataCount: 89,
    battery: 92,
  },
  {
    id: 3,
    name: "智能手环",
    model: "FB-5000",
    icon: "⌚",
    status: "offline",
    lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000),
    dataCount: 2340,
    battery: 15,
  },
]);

// 筛选后的档案
const filteredArchives = computed(() => {
  return archives.value.filter((archive) => {
    if (
      searchQuery.value &&
      !archive.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    ) {
      return false;
    }
    if (
      filters.value.member &&
      archive.memberId !== parseInt(filters.value.member)
    ) {
      return false;
    }
    if (filters.value.type && archive.type !== filters.value.type) {
      return false;
    }
    if (filters.value.status && archive.status !== filters.value.status) {
      return false;
    }
    return true;
  });
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredArchives.value.length / pageSize);
});

// 获取成员名称
const getMemberName = (memberId: number) => {
  const member = familyMembers.value.find((m) => m.id === memberId);
  return member ? member.name : "未知";
};

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labels = {
    checkup: "体检报告",
    lab: "化验单",
    imaging: "影像资料",
    prescription: "处方单",
  };
  return labels[type] || type;
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    parsed: "已解析",
    abnormal: "有异常",
    normal: "正常",
  };
  return labels[status] || status;
};

// 获取设备状态标签
const getDeviceStatusLabel = (status: string) => {
  const labels = {
    connected: "已连接",
    offline: "离线",
  };
  return labels[status] || status;
};

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString("zh-CN");
};

// 图表数据
const dataCollectionTrend = ref([
  { name: "1月", value: 156 },
  { name: "2月", value: 189 },
  { name: "3月", value: 134 },
  { name: "4月", value: 223 },
  { name: "5月", value: 198 },
  { name: "6月", value: 267 },
]);

const storageUsageTrend = ref([
  { name: "1月", value: 2.3, date: "2024-01" },
  { name: "2月", value: 2.8, date: "2024-02" },
  { name: "3月", value: 3.1, date: "2024-03" },
  { name: "4月", value: 3.7, date: "2024-04" },
  { name: "5月", value: 4.2, date: "2024-05" },
  { name: "6月", value: 4.8, date: "2024-06" },
]);

// 新增方法：时间线相关
const loadTimeline = async () => {
  timelineLoading.value = true;
  try {
    const data = await healthArchiveService.getComprehensiveTimeline(
      undefined, // 所有成员
      undefined, // 所有时间
      timelineLimit.value
    );
    timelineData.value = data;
    hasMoreTimeline.value = data.length >= timelineLimit.value;
  } catch (error) {
    console.error("加载时间线失败:", error);
    ElMessage.error("加载时间线失败");
  } finally {
    timelineLoading.value = false;
  }
};

const refreshTimeline = () => {
  timelineData.value = [];
  loadTimeline();
};

const loadMoreTimeline = async () => {
  loadingMoreTimeline.value = true;
  try {
    const data = await healthArchiveService.getComprehensiveTimeline(
      undefined,
      undefined,
      timelineLimit.value
    );
    timelineData.value.push(...data);
    hasMoreTimeline.value = data.length >= timelineLimit.value;
  } catch (error) {
    console.error("加载更多失败:", error);
    ElMessage.error("加载更多失败");
  } finally {
    loadingMoreTimeline.value = false;
  }
};

const viewTimelineDetail = (item: HealthTimelineItem) => {
  console.log("查看时间线详情:", item);
  ElMessage.info(`查看 ${item.title} 的详细信息`);
};

// 时间线辅助方法
const getTimelineIcon = (type: string) => {
  const icons: Record<string, any> = {
    checkup: User,
    medication: Coffee, // 用Coffee代替Medicine
    exercise: Monitor,
    diet: Coffee,
    sleep: Clock,
    vital_signs: CircleCheck,
    lab_result: Document,
  };
  return icons[type] || Document;
};

const getTypeName = (type: string) => {
  const names: Record<string, string> = {
    checkup: "体检",
    medication: "用药",
    exercise: "运动",
    diet: "饮食",
    sleep: "睡眠",
    vital_signs: "生命体征",
    lab_result: "检验",
  };
  return names[type] || type;
};

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    checkup: "primary",
    medication: "success",
    exercise: "warning",
    diet: "info",
    sleep: "",
    vital_signs: "primary",
    lab_result: "success",
  };
  return colors[type] || "";
};

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    normal: "正常",
    attention: "关注",
    abnormal: "异常",
  };
  return names[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    normal: "success",
    attention: "warning",
    abnormal: "danger",
  };
  return colors[status] || "";
};

const formatTimelineDate = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) {
    return new Intl.DateTimeFormat("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  } else if (days === 1) {
    return "昨天";
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return new Intl.DateTimeFormat("zh-CN", {
      month: "2-digit",
      day: "2-digit",
    }).format(date);
  }
};

// 对话框处理函数
const handleUploadSuccess = (reportId: string) => {
  console.log("报告上传成功:", reportId);
  ElMessage.success("报告上传成功");
  refreshTimeline(); // 刷新时间线
};

const handleViewReport = (reportId: string) => {
  selectedReportId.value = reportId;
  showReportDetailDialog.value = true;
};

const refreshData = () => {
  refreshTimeline();
  // 可以添加其他数据刷新逻辑
};

// 档案管理处理函数
const handleArchiveAdded = (archive: any) => {
  try {
    archives.value.unshift(archive);
    ElMessage.success("档案添加成功");
    // 刷新相关数据
    refreshTimeline();
  } catch (error) {
    console.error("添加档案失败:", error);
    ElMessage.error("添加档案失败");
  }
};

const handleArchiveUpdated = (updatedArchive: any) => {
  const index = archives.value.findIndex((a) => a.id === updatedArchive.id);
  if (index > -1) {
    archives.value[index] = updatedArchive;
    ElMessage.success("档案更新成功");
  }
};

const handleArchiveDeleted = (archiveId: string) => {
  const index = archives.value.findIndex((a) => a.id === archiveId);
  if (index > -1) {
    archives.value.splice(index, 1);
    ElMessage.success("档案删除成功");
  }
};

const viewArchive = (archive: any) => {
  // 查看档案详情
  ElMessage.info(`查看档案: ${archive.name}`);
  // 这里可以打开档案详情对话框或跳转到详情页面
};

const editArchive = (archive: any) => {
  selectedArchive.value = archive;
  showEditArchiveDialog.value = true;
};

const downloadArchive = async (archive: any) => {
  try {
    ElMessage.info(`开始下载档案: ${archive.name}`);

    // 模拟下载过程
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 创建下载链接
    const blob = new Blob(["模拟档案内容"], { type: "application/pdf" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${archive.name}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success("档案下载成功");
  } catch (error) {
    console.error("下载档案失败:", error);
    ElMessage.error("下载档案失败");
  }
};

// 设备管理处理函数
const handleDeviceAdded = (device: any) => {
  connectedDevices.value.push(device);
  ElMessage.success("设备添加成功");
};

const handleDeviceUpdated = (updatedDevice: any) => {
  const index = connectedDevices.value.findIndex(
    (d) => d.id === updatedDevice.id
  );
  if (index > -1) {
    connectedDevices.value[index] = updatedDevice;
    ElMessage.success("设备设置更新成功");
  }
};

const syncDevice = async (device: any) => {
  ElMessage.info(`开始同步设备: ${device.name}`);
  // 这里实现设备同步逻辑
  try {
    // 模拟同步过程
    await new Promise((resolve) => setTimeout(resolve, 2000));
    device.lastSync = new Date();
    device.dataCount += Math.floor(Math.random() * 10) + 1;
    ElMessage.success("设备同步成功");
  } catch (error) {
    ElMessage.error("设备同步失败");
  }
};

const configDevice = (device: any) => {
  selectedDevice.value = device;
  showDeviceConfigDialog.value = true;
};

// 数据操作功能
const exportAllData = async () => {
  try {
    ElMessage.info("开始导出所有健康数据...");

    // 模拟导出过程
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // 创建导出文件
    const exportData = {
      archives: archives.value,
      devices: connectedDevices.value,
      timeline: timelineData.value,
      exportDate: new Date().toISOString(),
      version: "1.0",
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `健康数据导出_${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success("数据导出成功");
  } catch (error) {
    console.error("导出数据失败:", error);
    ElMessage.error("导出数据失败");
  }
};

const backupData = async () => {
  try {
    ElMessage.info("开始备份健康数据...");

    // 模拟备份过程
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 模拟备份到云端
    const backupId = `backup_${Date.now()}`;

    ElMessage.success(`数据备份成功，备份ID: ${backupId}`);
  } catch (error) {
    console.error("备份数据失败:", error);
    ElMessage.error("备份数据失败");
  }
};

const importData = async (file: File) => {
  try {
    ElMessage.info("开始导入健康数据...");

    const text = await file.text();
    const importData = JSON.parse(text);

    // 验证数据格式
    if (!importData.archives || !importData.version) {
      throw new Error("数据格式不正确");
    }

    // 模拟导入过程
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 合并数据（这里简化处理）
    archives.value.push(...importData.archives);

    ElMessage.success("数据导入成功");
  } catch (error) {
    console.error("导入数据失败:", error);
    ElMessage.error("导入数据失败");
  }
};

// 设置处理函数
const handleSettingsChanged = (newSettings: any) => {
  console.log("设置已更新:", newSettings);
  // 应用新设置
  applySettings(newSettings);
};

const applySettings = (settings: any) => {
  // 应用主题设置
  if (settings.general?.theme) {
    document.documentElement.setAttribute("data-theme", settings.general.theme);
  }

  // 应用语言设置
  if (settings.general?.language) {
    // 这里可以集成国际化库
    console.log("切换语言到:", settings.general.language);
  }

  // 应用其他设置...
  ElMessage.success("设置已应用");
};

// 通用工具函数
const showLoading = (message = "加载中...") => {
  globalLoading.value = true;
  operationInProgress.value = true;
  ElMessage.info(message);
};

const hideLoading = () => {
  globalLoading.value = false;
  operationInProgress.value = false;
};

const handleError = (error: any, defaultMessage = "操作失败") => {
  console.error(error);
  hideLoading();

  let message = defaultMessage;
  if (error?.message) {
    message = error.message;
  } else if (typeof error === "string") {
    message = error;
  }

  errorMessage.value = message;
  ElMessage.error(message);
};

const handleSuccess = (message: string, callback?: () => void) => {
  hideLoading();
  ElMessage.success(message);
  if (callback) {
    callback();
  }
};

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: number;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 节流函数
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function executedFunction(...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

const handleSyncSuccess = (result: any) => {
  console.log("数据同步成功:", result);
  ElMessage.success("数据同步成功");
  refreshTimeline(); // 刷新时间线
};

// 生命周期
onMounted(() => {
  loadTimeline();
});
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 72px);
  background: var(--theme-bg-secondary);
}

.page-content {
  max-width: var(--max-width-7xl);
  margin: 0 auto;
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

/* 时间线网格样式 */
.timeline-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.timeline-card {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.timeline-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.timeline-card.completed {
  opacity: 0.7;
  background: var(--theme-bg-secondary);
}

.timeline-badge {
  flex-shrink: 0;
  width: 48px;
  height: 32px;
  background: #ff6b6b;
  color: white;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.timeline-card.completed .timeline-badge {
  background: var(--color-success);
}

.timeline-day {
  font-size: var(--font-size-xs);
}

.timeline-content {
  flex: 1;
}

.timeline-action {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-1);
}

.timeline-description {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.stat-card {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.stat-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

/* 档案管理和设备管理 */
.archives-management,
.devices-management {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-6);
}

.section-info {
  flex: 1;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.section-subtitle {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 搜索和筛选 */
.search-filters {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  align-items: center;
}

.search-bar {
  display: flex;
  flex: 1;
  max-width: 400px;
}

.search-input {
  flex: 1;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
  font-size: var(--font-size-sm);
}

.search-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-left: none;
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  background-color: var(--theme-bg-secondary);
  cursor: pointer;
}

.filters {
  display: flex;
  gap: var(--spacing-2);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* 档案表格 */
.archives-table {
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1.5fr;
  background-color: var(--theme-bg-secondary);
  border-bottom: 1px solid var(--theme-border);
}

.header-cell {
  padding: var(--spacing-3) var(--spacing-4);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1.5fr;
  border-bottom: 1px solid var(--theme-border);
  transition: background-color var(--transition-normal);
}

.table-row:hover {
  background-color: var(--theme-bg-secondary);
}

.cell {
  padding: var(--spacing-3) var(--spacing-4);
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
}

.archive-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.file-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.file-info {
  flex: 1;
}

.file-title {
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-1);
}

.file-pages {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.type-badge,
.status-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.type-checkup {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.type-lab {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.type-imaging {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.type-prescription {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.status-parsed {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.status-abnormal {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
}

.status-normal {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
}

.actions {
  display: flex;
  gap: var(--spacing-1);
}

.action-btn {
  padding: var(--spacing-1) var(--spacing-2);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.action-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--theme-bg-secondary);
}

.page-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

/* 设备管理 */
.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.device-card {
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  transition: all var(--transition-normal);
}

.device-card:hover {
  box-shadow: var(--shadow-md);
}

.device-card.offline {
  opacity: 0.7;
  border-color: var(--color-gray-300);
}

.device-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.device-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.device-model {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.device-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-connected {
  background-color: var(--color-success);
}

.status-offline {
  background-color: var(--color-gray-400);
}

.status-text {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
}

.battery-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  position: relative;
  width: 60px;
  height: 12px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-sm);
}

.battery-level {
  height: 100%;
  background-color: var(--color-success);
  border-radius: var(--border-radius-sm);
  transition: width var(--transition-normal);
}

.battery-text {
  position: absolute;
  right: -30px;
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.device-actions {
  display: flex;
  gap: var(--spacing-2);
}

.empty-devices {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-12);
  color: var(--theme-text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-4);
}

.empty-devices h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-devices p {
  font-size: var(--font-size-base);
  margin: 0 0 var(--spacing-4) 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .health-center-container {
    flex-direction: column;
  }

  .timeline-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--theme-border-light);
  }

  .timeline-content {
    flex-direction: row;
    overflow-x: auto;
    gap: var(--spacing-2);
  }

  .timeline-item {
    flex-shrink: 0;
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filters {
    flex-wrap: wrap;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .header-cell,
  .cell {
    padding: var(--spacing-2);
  }

  .devices-grid {
    grid-template-columns: 1fr;
  }
}

/* 新增样式：快速操作区域 */
.quick-actions {
  margin-bottom: var(--spacing-6);
}

.actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.actions-buttons {
  display: flex;
  gap: var(--spacing-3);
}

/* 新增样式：时间线 */
.timeline-container {
  max-height: 600px;
  overflow-y: auto;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--theme-border-light);
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-item:hover {
  background: var(--theme-bg-secondary);
}

.timeline-item:last-child {
  border-bottom: none;
}

.timeline-item.status-normal {
  border-left: 3px solid var(--color-success);
}

.timeline-item.status-attention {
  border-left: 3px solid var(--color-warning);
}

.timeline-item.status-abnormal {
  border-left: 3px solid var(--color-danger);
}

.timeline-time {
  font-size: 12px;
  color: var(--theme-text-secondary);
  min-width: 60px;
  text-align: right;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 50%;
  flex-shrink: 0;
}

.timeline-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.timeline-source {
  font-size: 11px;
  color: var(--theme-text-secondary);
}

.timeline-status {
  flex-shrink: 0;
}

.timeline-load-more {
  text-align: center;
  padding: var(--spacing-4);
}

.timeline-empty {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .actions-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }

  .actions-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .timeline-item {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .timeline-time {
    text-align: left;
    min-width: auto;
  }

  .timeline-meta {
    flex-wrap: wrap;
  }
}
</style>
