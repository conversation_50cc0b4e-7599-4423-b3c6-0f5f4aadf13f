/**
 * 健康档案服务
 * 提供健康档案中心的7个核心功能API
 */

export interface HealthTimelineItem {
  id: string
  timestamp: Date
  type: 'checkup' | 'medication' | 'exercise' | 'diet' | 'sleep' | 'vital_signs' | 'lab_result'
  title: string
  description: string
  source: string
  memberId: number | string
  data?: Record<string, any>
  status: 'normal' | 'abnormal' | 'attention'
}

export interface HealthSyncResult {
  success: boolean
  syncedCount: number
  failedCount: number
  lastSyncTime: Date
  sources: Array<{
    name: string
    type: 'device' | 'app' | 'manual'
    status: 'success' | 'failed' | 'partial'
    recordCount: number
    lastSync: Date
  }>
}

export interface HealthReport {
  id: string
  name: string
  type: 'checkup' | 'lab' | 'imaging' | 'prescription' | 'other'
  memberId: number | string
  memberName: string
  uploadDate: Date
  reportDate: Date
  status: 'processing' | 'completed' | 'failed' | 'manual_review'
  ocrProgress: number
  fileUrl: string
  thumbnailUrl?: string
  pageCount: number
  fileSize: string
  extractedData?: HealthReportData
  confidence: number
  needsReview: boolean
}

export interface HealthReportData {
  basicInfo: {
    patientName?: string
    patientId?: string
    age?: number
    gender?: string
    reportDate?: Date
    hospitalName?: string
    doctorName?: string
  }
  vitalSigns?: {
    height?: number
    weight?: number
    bmi?: number
    bloodPressure?: string
    heartRate?: number
    temperature?: number
  }
  labResults?: Array<{
    itemName: string
    value: string | number
    unit: string
    referenceRange: string
    status: 'normal' | 'high' | 'low' | 'abnormal'
  }>
  diagnosis?: string[]
  recommendations?: string[]
  medications?: Array<{
    name: string
    dosage: string
    frequency: string
    duration: string
  }>
  nextAppointment?: Date
  notes?: string
}

export interface HealthDataFilter {
  type?: string[]
  memberId?: number | string
  dateRange?: [Date, Date]
  status?: string[]
  source?: string[]
  limit?: number
  offset?: number
}

export interface HealthDataResult {
  data: HealthTimelineItem[]
  total: number
  hasMore: boolean
  aggregations?: {
    byType: Record<string, number>
    byStatus: Record<string, number>
    bySource: Record<string, number>
  }
}

/**
 * 健康档案服务类
 */
class HealthArchiveService {
  private baseUrl = '/api/health-archive'

  /**
   * 1. 获取综合活动时间线
   */
  async getComprehensiveTimeline(
    memberId?: number | string,
    dateRange?: [Date, Date],
    limit: number = 50
  ): Promise<HealthTimelineItem[]> {
    await this.delay(1000)

    // 模拟综合时间线数据
    const mockTimeline: HealthTimelineItem[] = [
      {
        id: 'timeline_001',
        timestamp: new Date('2024-01-20 09:30:00'),
        type: 'checkup',
        title: '年度体检',
        description: '完成年度全面体检，各项指标正常',
        source: '北京协和医院',
        memberId: memberId || 1,
        status: 'normal',
        data: {
          reportId: 'RPT_001',
          doctorName: '张医生',
          department: '内科'
        }
      },
      {
        id: 'timeline_002',
        timestamp: new Date('2024-01-19 14:15:00'),
        type: 'vital_signs',
        title: '血压测量',
        description: '血压 120/80 mmHg，心率 72 bpm',
        source: '智能血压计',
        memberId: memberId || 1,
        status: 'normal',
        data: {
          systolic: 120,
          diastolic: 80,
          heartRate: 72,
          deviceId: 'BP_001'
        }
      },
      {
        id: 'timeline_003',
        timestamp: new Date('2024-01-19 08:00:00'),
        type: 'exercise',
        title: '晨跑锻炼',
        description: '跑步 5.2 公里，消耗 320 卡路里',
        source: '运动手环',
        memberId: memberId || 1,
        status: 'normal',
        data: {
          distance: 5.2,
          duration: 32,
          calories: 320,
          avgHeartRate: 145
        }
      },
      {
        id: 'timeline_004',
        timestamp: new Date('2024-01-18 22:30:00'),
        type: 'sleep',
        title: '睡眠记录',
        description: '睡眠时长 7.5 小时，深度睡眠 2.1 小时',
        source: '智能手环',
        memberId: memberId || 1,
        status: 'normal',
        data: {
          totalSleep: 7.5,
          deepSleep: 2.1,
          lightSleep: 4.2,
          remSleep: 1.2,
          sleepQuality: 85
        }
      },
      {
        id: 'timeline_005',
        timestamp: new Date('2024-01-18 16:45:00'),
        type: 'lab_result',
        title: '血液检查结果',
        description: '血糖略高，需要注意饮食控制',
        source: '检验科',
        memberId: memberId || 1,
        status: 'attention',
        data: {
          glucose: 6.8,
          cholesterol: 4.2,
          triglycerides: 1.8,
          reportId: 'LAB_001'
        }
      }
    ]

    // 根据日期范围筛选
    let filteredData = mockTimeline
    if (dateRange) {
      filteredData = mockTimeline.filter(item => 
        item.timestamp >= dateRange[0] && item.timestamp <= dateRange[1]
      )
    }

    // 根据成员ID筛选
    if (memberId) {
      filteredData = filteredData.filter(item => item.memberId === memberId)
    }

    // 按时间倒序排列并限制数量
    return filteredData
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit)
  }

  /**
   * 2. 同步健康数据
   */
  async syncHealthData(
    sources?: string[],
    memberId?: number | string
  ): Promise<HealthSyncResult> {
    await this.delay(2000) // 模拟同步时间

    const mockSyncResult: HealthSyncResult = {
      success: true,
      syncedCount: 156,
      failedCount: 3,
      lastSyncTime: new Date(),
      sources: [
        {
          name: '智能手环',
          type: 'device',
          status: 'success',
          recordCount: 89,
          lastSync: new Date()
        },
        {
          name: '血压计',
          type: 'device',
          status: 'success',
          recordCount: 23,
          lastSync: new Date()
        },
        {
          name: '体重秤',
          type: 'device',
          status: 'success',
          recordCount: 15,
          lastSync: new Date()
        },
        {
          name: '健康应用',
          type: 'app',
          status: 'partial',
          recordCount: 29,
          lastSync: new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
        },
        {
          name: '医院系统',
          type: 'app',
          status: 'failed',
          recordCount: 0,
          lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1天前
        }
      ]
    }

    return mockSyncResult
  }

  /**
   * 3. 创建健康报告（异步OCR）
   */
  async createHealthReport(
    file: File,
    memberId: number | string,
    reportType: string = 'checkup'
  ): Promise<{ reportId: string; status: string }> {
    await this.delay(500) // 模拟文件上传时间

    const reportId = `RPT_${Date.now()}`
    
    // 模拟开始OCR处理
    this.processOCR(reportId)

    return {
      reportId,
      status: 'processing'
    }
  }

  /**
   * 4. 获取健康报告列表
   */
  async getHealthReportList(
    memberId?: number | string,
    type?: string,
    status?: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<{ reports: HealthReport[]; total: number }> {
    await this.delay(800)

    const mockReports: HealthReport[] = [
      {
        id: 'RPT_001',
        name: '年度体检报告',
        type: 'checkup',
        memberId: 1,
        memberName: '张先生',
        uploadDate: new Date('2024-01-20'),
        reportDate: new Date('2024-01-20'),
        status: 'completed',
        ocrProgress: 100,
        fileUrl: '/files/reports/checkup_001.pdf',
        thumbnailUrl: '/files/thumbnails/checkup_001.jpg',
        pageCount: 8,
        fileSize: '2.3MB',
        confidence: 95,
        needsReview: false
      },
      {
        id: 'RPT_002',
        name: '血液检查报告',
        type: 'lab',
        memberId: 1,
        memberName: '张先生',
        uploadDate: new Date('2024-01-18'),
        reportDate: new Date('2024-01-18'),
        status: 'completed',
        ocrProgress: 100,
        fileUrl: '/files/reports/lab_001.pdf',
        pageCount: 3,
        fileSize: '1.2MB',
        confidence: 88,
        needsReview: true
      },
      {
        id: 'RPT_003',
        name: 'X光检查报告',
        type: 'imaging',
        memberId: 2,
        memberName: '李女士',
        uploadDate: new Date('2024-01-15'),
        reportDate: new Date('2024-01-15'),
        status: 'processing',
        ocrProgress: 65,
        fileUrl: '/files/reports/xray_001.pdf',
        pageCount: 2,
        fileSize: '0.8MB',
        confidence: 0,
        needsReview: false
      }
    ]

    // 筛选逻辑
    let filteredReports = mockReports
    if (memberId) {
      filteredReports = filteredReports.filter(r => r.memberId === memberId)
    }
    if (type) {
      filteredReports = filteredReports.filter(r => r.type === type)
    }
    if (status) {
      filteredReports = filteredReports.filter(r => r.status === status)
    }

    const total = filteredReports.length
    const startIndex = (page - 1) * pageSize
    const reports = filteredReports.slice(startIndex, startIndex + pageSize)

    return { reports, total }
  }

  /**
   * 5. 获取健康报告详情
   */
  async getHealthReportDetail(reportId: string): Promise<HealthReport> {
    await this.delay(600)

    const mockReport: HealthReport = {
      id: reportId,
      name: '年度体检报告',
      type: 'checkup',
      memberId: 1,
      memberName: '张先生',
      uploadDate: new Date('2024-01-20'),
      reportDate: new Date('2024-01-20'),
      status: 'completed',
      ocrProgress: 100,
      fileUrl: '/files/reports/checkup_001.pdf',
      thumbnailUrl: '/files/thumbnails/checkup_001.jpg',
      pageCount: 8,
      fileSize: '2.3MB',
      confidence: 95,
      needsReview: false,
      extractedData: {
        basicInfo: {
          patientName: '张先生',
          patientId: 'P001',
          age: 45,
          gender: '男',
          reportDate: new Date('2024-01-20'),
          hospitalName: '北京协和医院',
          doctorName: '张医生'
        },
        vitalSigns: {
          height: 175,
          weight: 70,
          bmi: 22.9,
          bloodPressure: '120/80',
          heartRate: 72,
          temperature: 36.5
        },
        labResults: [
          {
            itemName: '血糖',
            value: 5.6,
            unit: 'mmol/L',
            referenceRange: '3.9-6.1',
            status: 'normal'
          },
          {
            itemName: '总胆固醇',
            value: 4.2,
            unit: 'mmol/L',
            referenceRange: '<5.2',
            status: 'normal'
          },
          {
            itemName: '甘油三酯',
            value: 1.8,
            unit: 'mmol/L',
            referenceRange: '<1.7',
            status: 'high'
          }
        ],
        diagnosis: ['健康状况良好', '轻度血脂异常'],
        recommendations: ['保持健康饮食', '适量运动', '定期复查'],
        nextAppointment: new Date('2025-01-20')
      }
    }

    return mockReport
  }

  /**
   * 6. 手动修改健康报告数据
   */
  async updateHealthReportData(
    reportId: string,
    updatedData: Partial<HealthReportData>
  ): Promise<HealthReport> {
    await this.delay(800)

    console.log('更新报告数据:', { reportId, updatedData })

    // 模拟更新后的报告
    const updatedReport = await this.getHealthReportDetail(reportId)
    if (updatedReport.extractedData) {
      updatedReport.extractedData = {
        ...updatedReport.extractedData,
        ...updatedData
      }
    }

    return updatedReport
  }

  /**
   * 7. 获取特定类型的健康数据
   */
  async getSpecificHealthData(filter: HealthDataFilter): Promise<HealthDataResult> {
    await this.delay(1000)

    // 获取基础时间线数据
    const timelineData = await this.getComprehensiveTimeline(
      filter.memberId,
      filter.dateRange,
      filter.limit || 100
    )

    // 按类型筛选
    let filteredData = timelineData
    if (filter.type && filter.type.length > 0) {
      filteredData = timelineData.filter(item => filter.type!.includes(item.type))
    }

    // 按状态筛选
    if (filter.status && filter.status.length > 0) {
      filteredData = filteredData.filter(item => filter.status!.includes(item.status))
    }

    // 按来源筛选
    if (filter.source && filter.source.length > 0) {
      filteredData = filteredData.filter(item => 
        filter.source!.some(source => item.source.includes(source))
      )
    }

    // 分页处理
    const offset = filter.offset || 0
    const limit = filter.limit || 50
    const paginatedData = filteredData.slice(offset, offset + limit)

    // 计算聚合数据
    const aggregations = {
      byType: this.aggregateByField(filteredData, 'type'),
      byStatus: this.aggregateByField(filteredData, 'status'),
      bySource: this.aggregateByField(filteredData, 'source')
    }

    return {
      data: paginatedData,
      total: filteredData.length,
      hasMore: offset + limit < filteredData.length,
      aggregations
    }
  }

  /**
   * 私有方法：模拟OCR处理
   */
  private async processOCR(reportId: string): Promise<void> {
    // 模拟OCR处理进度
    const progressSteps = [20, 40, 60, 80, 100]
    
    for (const progress of progressSteps) {
      await this.delay(1000)
      console.log(`OCR处理进度 ${reportId}: ${progress}%`)
      
      // 这里可以通过WebSocket或轮询通知前端进度更新
      if (progress === 100) {
        console.log(`OCR处理完成 ${reportId}`)
      }
    }
  }

  /**
   * 私有方法：聚合统计
   */
  private aggregateByField(data: HealthTimelineItem[], field: keyof HealthTimelineItem): Record<string, number> {
    return data.reduce((acc, item) => {
      const value = String(item[field])
      acc[value] = (acc[value] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  /**
   * 私有方法：模拟延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例实例
export const healthArchiveService = new HealthArchiveService()
export default healthArchiveService
