<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
              />
            </svg>
          </div>
          <span v-show="!sidebarCollapsed" class="logo-text">家庭健康管理</span>
        </div>

        <button
          class="sidebar-toggle"
          @click="toggleSidebar"
          :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
          </svg>
        </button>
      </div>

      <nav class="sidebar-nav">
        <ul class="nav-menu">
          <li v-for="item in menuItems" :key="item.path" class="nav-item">
            <router-link
              :to="item.path"
              class="nav-link"
              :class="{ active: isActiveRoute(item.path) }"
            >
              <span class="nav-icon">
                <component :is="item.icon" />
              </span>
              <span v-show="!sidebarCollapsed" class="nav-text">{{
                item.title
              }}</span>
            </router-link>
          </li>
        </ul>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main
      class="main-content"
      :class="{ 'sidebar-collapsed': sidebarCollapsed }"
    >
      <!-- 顶部导航栏 -->
      <header class="main-header">
        <div class="header-content">
          <!-- 左侧区域 -->
          <div class="header-left">
            <!-- 页面定位 -->
            <div class="page-location">
              <div class="page-info">
                <h2 class="page-title">{{ currentPageTitle }}</h2>
                <span class="page-path">{{ currentPagePath }}</span>
              </div>
            </div>
          </div>

          <!-- 中间区域 -->
          <div class="header-center">
            <!-- 搜索框 -->
            <div class="search-container">
              <div class="search-input-wrapper">
                <Search class="search-icon" />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索功能、数据或设置..."
                  class="search-input"
                  @keyup.enter="handleSearch"
                />
                <button
                  v-if="searchQuery"
                  class="search-clear"
                  @click="clearSearch"
                >
                  <Close class="clear-icon" />
                </button>
              </div>
            </div>
          </div>

          <!-- 右侧区域 -->
          <div class="header-right">
            <!-- 快捷操作 -->
            <div class="quick-actions">
              <button
                class="header-btn"
                title="添加数据"
                @click="showAddDataModal"
              >
                <Plus class="btn-icon" />
              </button>
              <button
                class="header-btn"
                title="通知"
                @click="showNotifications"
              >
                <Bell class="btn-icon" />
                <span v-if="unreadCount > 0" class="notification-badge">{{
                  unreadCount
                }}</span>
              </button>
            </div>

            <!-- 用户菜单 -->
            <div class="user-menu" @click="toggleUserMenu">
              <div class="user-info">
                <div class="user-avatar">
                  <img
                    v-if="userInfo.avatar"
                    :src="userInfo.avatar"
                    :alt="userInfo.name"
                  />
                  <span v-else class="avatar-text">{{
                    userInfo.name.charAt(0)
                  }}</span>
                </div>
                <div class="user-details">
                  <span class="user-name">{{ userInfo.name }}</span>
                  <span class="user-role">{{ userInfo.role }}</span>
                </div>
                <ArrowDown
                  class="dropdown-icon"
                  :class="{ 'is-open': userMenuOpen }"
                />
              </div>

              <!-- 用户下拉菜单 -->
              <div v-if="userMenuOpen" class="user-dropdown">
                <div class="dropdown-item" @click.stop="goToProfile">
                  <User class="item-icon" />
                  <span>个人资料</span>
                </div>
                <div class="dropdown-item" @click.stop="goToSettings">
                  <Setting class="item-icon" />
                  <span>系统设置</span>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-item logout" @click.stop="handleLogout">
                  <SwitchButton class="item-icon" />
                  <span>退出登录</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  Search,
  Close,
  Plus,
  Bell,
  ArrowDown,
  User,
  Setting,
  SwitchButton,
} from "@element-plus/icons-vue";

// 侧边栏状态
const sidebarCollapsed = ref(false);

// 当前路由
const route = useRoute();
const router = useRouter();

// 搜索相关
const searchQuery = ref("");

// 用户菜单状态
const userMenuOpen = ref(false);

// 通知数量
const unreadCount = ref(3);

// 菜单项配置
const menuItems = [
  { path: "/dashboard", title: "首页", icon: "House" },
  { path: "/archive", title: "档案", icon: "User" },
  { path: "/family", title: "家庭", icon: "UserFilled" },
  { path: "/assistant", title: "助手", icon: "ChatDotRound" },
  { path: "/calendar", title: "日历", icon: "Calendar" },
  { path: "/activities", title: "活动", icon: "Trophy" },
  { path: "/settings", title: "设置", icon: "Setting" },
];

// 用户信息
const userInfo = ref({
  name: "张先生",
  role: "家庭管理员",
  avatar: "",
});

// 当前页面标题
const currentPageTitle = computed(() => {
  const path = route.path;

  // 根据路径返回准确的页面标题
  if (path === "/" || path === "/dashboard") return "首页";
  if (path === "/archive") return "档案";
  if (path === "/archive/personal-info") return "个人信息";
  if (path === "/archive/health-center") return "健康档案中心";
  if (path === "/archive/medicine-box") return "个人药箱";
  if (path === "/archive/health-overview") return "综合健康概要";
  if (path === "/archive/emergency-card") return "紧急资料卡";
  if (path === "/family") return "家庭";
  if (path === "/assistant") return "助手";
  if (path === "/assistant/smart-chat") return "智能对话";
  if (path === "/assistant/health-analysis") return "健康分析";
  if (path === "/assistant/health-report") return "健康报告";
  if (path === "/assistant/health-suggestions") return "健康建议";
  if (path === "/assistant/health-prediction") return "健康预测";
  if (path === "/calendar") return "日历";
  if (path === "/activities") return "活动";
  if (path === "/activities/activity-list") return "活动列表";
  if (path === "/activities/activity-timeline") return "活动时间线";
  if (path === "/activities/activity-analysis") return "活动数据分析";
  if (path === "/settings") return "设置";

  // 从路由元信息获取标题
  const title = route.meta?.title as string;
  if (title) {
    return title.split(" - ")[0];
  }
  return "家庭健康管理系统";
});

// 当前页面路径
const currentPagePath = computed(() => {
  const path = route.path;

  // 根据路径返回准确的面包屑路径
  if (path === "/" || path === "/dashboard") return "首页";
  if (path === "/archive") return "档案";
  if (path === "/archive/personal-info") return "档案 / 个人信息";
  if (path === "/archive/health-center") return "档案 / 健康档案中心";
  if (path === "/archive/medicine-box") return "档案 / 个人药箱";
  if (path === "/archive/health-overview") return "档案 / 综合健康概要";
  if (path === "/archive/emergency-card") return "档案 / 紧急资料卡";
  if (path === "/family") return "家庭";
  if (path === "/assistant") return "助手";
  if (path === "/assistant/smart-chat") return "助手 / 智能对话";
  if (path === "/assistant/health-analysis") return "助手 / 健康分析";
  if (path === "/assistant/health-report") return "助手 / 健康报告";
  if (path === "/assistant/health-suggestions") return "助手 / 健康建议";
  if (path === "/assistant/health-prediction") return "助手 / 健康预测";
  if (path === "/calendar") return "日历";
  if (path === "/activities") return "活动";
  if (path === "/activities/activity-list") return "活动 / 活动列表";
  if (path === "/activities/activity-timeline") return "活动 / 活动时间线";
  if (path === "/activities/activity-analysis") return "活动 / 活动数据分析";
  if (path === "/settings") return "设置";

  // 默认处理
  const pathSegments = path.split("/").filter(Boolean);
  if (pathSegments.length === 0) return "首页";

  const pathMap: Record<string, string> = {
    dashboard: "首页",
    archive: "档案",
    family: "家庭",
    assistant: "助手",
    calendar: "日历",
    activities: "活动",
    settings: "设置",
  };

  return pathSegments.map((segment) => pathMap[segment] || segment).join(" / ");
});

// 当前页面图标
const currentPageIcon = computed(() => {
  const path = route.path;

  // 根据具体路径返回准确的图标
  if (path === "/" || path === "/dashboard") return "House";
  if (path === "/archive") return "User";
  if (path === "/archive/personal-info") return "User";
  if (path === "/archive/health-center") return "DataAnalysis";
  if (path === "/archive/medicine-box") return "Box";
  if (path === "/archive/health-overview") return "TrendCharts";
  if (path === "/archive/emergency-card") return "Warning";
  if (path === "/family") return "UserFilled";
  if (path === "/assistant") return "ChatDotRound";
  if (path === "/assistant/smart-chat") return "ChatDotRound";
  if (path === "/assistant/health-analysis") return "DataAnalysis";
  if (path === "/assistant/health-report") return "Document";
  if (path === "/assistant/health-suggestions") return "Sunny";
  if (path === "/assistant/health-prediction") return "TrendCharts";
  if (path === "/calendar") return "Calendar";
  if (path === "/activities") return "Trophy";
  if (path === "/activities/activity-list") return "List";
  if (path === "/activities/activity-timeline") return "Clock";
  if (path === "/activities/activity-analysis") return "DataAnalysis";
  if (path === "/settings") return "Setting";

  // 默认图标处理
  if (path.includes("/archive")) return "User";
  if (path.includes("/family")) return "UserFilled";
  if (path.includes("/assistant")) return "ChatDotRound";
  if (path.includes("/calendar")) return "Calendar";
  if (path.includes("/activities")) return "Trophy";
  if (path.includes("/settings")) return "Setting";
  return "House";
});

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 检查路由是否激活
const isActiveRoute = (path: string) => {
  return route.path === path || route.path.startsWith(path + "/");
};

// 搜索功能
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log("搜索:", searchQuery.value);
    // 这里可以实现搜索逻辑
  }
};

const clearSearch = () => {
  searchQuery.value = "";
};

// 用户菜单功能
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value;
};

const goToProfile = () => {
  router.push("/archive/personal-info");
  userMenuOpen.value = false;
};

const goToSettings = () => {
  router.push("/settings");
  userMenuOpen.value = false;
};

const handleLogout = () => {
  console.log("handleLogout 函数被调用");
  // 清除认证token
  localStorage.removeItem("auth_token");
  console.log("auth_token 已清除");
  console.log("准备跳转到登录页面");
  // 跳转到登录页面
  router
    .push("/login")
    .then(() => {
      console.log("路由跳转成功");
    })
    .catch((error) => {
      console.error("路由跳转失败:", error);
    });
  userMenuOpen.value = false;
};

// 快捷操作
const showAddDataModal = () => {
  console.log("显示添加数据模态框");
};

const showNotifications = () => {
  console.log("显示通知");
};
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background-color: var(--theme-bg-secondary);
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background-color: var(--theme-bg-primary);
  border-right: 1px solid var(--theme-border);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal);
  z-index: 100;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--theme-border);
  min-height: 72px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
  min-width: 0;
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
  flex-shrink: 0;
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--theme-text-secondary);
  cursor: pointer;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.sidebar-toggle:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-4) 0;
  overflow-y: auto;
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--spacing-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-6);
  color: var(--theme-text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.nav-link.active {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.nav-link.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--color-primary);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: var(--spacing-3);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: margin-left var(--transition-normal);
}

.main-header {
  background: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border);
  padding: 0 var(--spacing-6);
  height: 72px;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: var(--spacing-6);
}

.header-left {
  flex: 0 0 auto;
}

.header-center {
  flex: 1;
  max-width: 600px;
}

.header-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

/* 页面定位 */
.page-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.page-icon {
  width: 24px;
  height: 24px;
  color: var(--color-primary);
}

.page-info {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
  line-height: 1.2;
}

.page-path {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  margin-top: 2px;
}

/* 搜索框 */
.search-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 var(--spacing-3) 0 var(--spacing-10);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  background: var(--theme-bg-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.search-input::placeholder {
  color: var(--theme-text-tertiary);
}

.search-icon {
  position: absolute;
  left: var(--spacing-3);
  width: 16px;
  height: 16px;
  color: var(--theme-text-tertiary);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: var(--spacing-2);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--theme-text-tertiary);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.search-clear:hover {
  background: var(--theme-bg-tertiary);
  color: var(--theme-text-secondary);
}

.clear-icon {
  width: 14px;
  height: 14px;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.header-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--theme-text-secondary);
  cursor: pointer;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.header-btn:hover {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

.btn-icon {
  width: 18px;
  height: 18px;
}

.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--color-danger);
  color: white;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  line-height: 1;
}

/* 用户菜单 */
.user-menu {
  position: relative;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal);
}

.user-info:hover {
  background-color: var(--theme-bg-secondary);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.user-details {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
  line-height: 1.2;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  color: var(--theme-text-tertiary);
  transition: transform var(--transition-normal);
}

.dropdown-icon.is-open {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-2);
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.dropdown-item:hover {
  background-color: var(--theme-bg-secondary);
}

.dropdown-item.logout {
  color: var(--color-danger);
}

.dropdown-item.logout:hover {
  background-color: var(--color-danger-light);
}

.item-icon {
  width: 16px;
  height: 16px;
}

.dropdown-divider {
  height: 1px;
  background: var(--theme-border);
  margin: var(--spacing-1) 0;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--theme-bg-secondary);
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar-header {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .header-content {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .page-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: var(--font-size-base);
  }

  .page-title {
    font-size: var(--font-size-base);
  }

  .header-right {
    gap: var(--spacing-2);
  }

  .header-btn,
  .user-avatar {
    width: 36px;
    height: 36px;
  }
}
</style>
