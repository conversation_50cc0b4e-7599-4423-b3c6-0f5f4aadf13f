<template>
  <div class="family-setup-page">
    <div class="setup-container">
      <!-- 步骤指示器 -->
      <div class="steps-header">
        <div class="steps-indicator">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="step-item"
            :class="{ 
              active: index === currentStep, 
              completed: index < currentStep 
            }"
          >
            <div class="step-number">
              <span v-if="index < currentStep">✓</span>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-title">{{ step.title }}</span>
          </div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1: 创建家庭 -->
        <div v-if="currentStep === 0" class="step-panel">
          <div class="panel-header">
            <h2 class="panel-title">创建您的家庭</h2>
            <p class="panel-subtitle">为您的家庭起一个温馨的名字</p>
          </div>
          
          <div class="panel-body">
            <div class="form-group">
              <label for="familyName" class="form-label">家庭名称</label>
              <input
                id="familyName"
                v-model="familyData.name"
                type="text"
                class="form-input"
                placeholder="例如：张家大院、幸福之家"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="familyAddress" class="form-label">家庭地址（可选）</label>
              <input
                id="familyAddress"
                v-model="familyData.address"
                type="text"
                class="form-input"
                placeholder="请输入家庭地址"
              />
            </div>
          </div>
        </div>

        <!-- 步骤2: 添加家庭成员 -->
        <div v-if="currentStep === 1" class="step-panel">
          <div class="panel-header">
            <h2 class="panel-title">添加家庭成员</h2>
            <p class="panel-subtitle">添加您的家人，开始健康管理之旅</p>
          </div>
          
          <div class="panel-body">
            <!-- 成员列表 -->
            <div class="members-list">
              <div 
                v-for="(member, index) in familyMembers" 
                :key="index"
                class="member-item"
              >
                <div class="member-avatar">
                  <span class="avatar-text">{{ member.name.charAt(0) }}</span>
                </div>
                <div class="member-info">
                  <h4 class="member-name">{{ member.name }}</h4>
                  <p class="member-details">{{ member.relationship }} · {{ member.age }}岁</p>
                </div>
                <button 
                  class="remove-member-btn"
                  @click="removeMember(index)"
                >
                  ×
                </button>
              </div>
            </div>

            <!-- 添加成员表单 -->
            <div class="add-member-form">
              <h3 class="form-title">添加新成员</h3>
              <div class="form-row">
                <div class="form-group">
                  <label class="form-label">姓名</label>
                  <input
                    v-model="newMember.name"
                    type="text"
                    class="form-input"
                    placeholder="请输入姓名"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">关系</label>
                  <select v-model="newMember.relationship" class="form-select">
                    <option value="">请选择关系</option>
                    <option value="本人">本人</option>
                    <option value="配偶">配偶</option>
                    <option value="父亲">父亲</option>
                    <option value="母亲">母亲</option>
                    <option value="儿子">儿子</option>
                    <option value="女儿">女儿</option>
                    <option value="其他">其他</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="form-label">出生日期</label>
                  <input
                    v-model="newMember.birthday"
                    type="date"
                    class="form-input"
                  />
                </div>
              </div>
              <button 
                class="add-member-btn"
                @click="addMember"
                :disabled="!canAddMember"
              >
                添加成员
              </button>
            </div>
          </div>
        </div>

        <!-- 步骤3: 建立首份档案 -->
        <div v-if="currentStep === 2" class="step-panel">
          <div class="panel-header">
            <h2 class="panel-title">建立首份健康档案</h2>
            <p class="panel-subtitle">选择一种方式开始记录健康数据</p>
          </div>
          
          <div class="panel-body">
            <div class="archive-options">
              <div 
                class="option-card"
                :class="{ selected: selectedOption === 'upload' }"
                @click="selectedOption = 'upload'"
              >
                <div class="option-icon">📄</div>
                <h3 class="option-title">上传体检报告</h3>
                <p class="option-description">OCR智能识别关键指标和文本建议</p>
              </div>
              
              <div 
                class="option-card"
                :class="{ selected: selectedOption === 'device' }"
                @click="selectedOption = 'device'"
              >
                <div class="option-icon">⌚</div>
                <h3 class="option-title">连接智能设备</h3>
                <p class="option-description">无感自动同步健康数据</p>
              </div>
              
              <div 
                class="option-card"
                :class="{ selected: selectedOption === 'manual' }"
                @click="selectedOption = 'manual'"
              >
                <div class="option-icon">✏️</div>
                <h3 class="option-title">手动录入</h3>
                <p class="option-description">手动输入各项健康数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation-buttons">
        <button
          v-if="currentStep > 0"
          class="nav-btn prev-btn"
          @click="previousStep"
        >
          上一步
        </button>
        
        <button
          v-if="currentStep < steps.length - 1"
          class="nav-btn next-btn"
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </button>
        
        <button
          v-if="currentStep === steps.length - 1"
          class="nav-btn complete-btn"
          @click="completeSetup"
          :disabled="!selectedOption"
        >
          完成设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 当前步骤
const currentStep = ref(0);

// 步骤配置
const steps = ref([
  { title: '创建家庭' },
  { title: '添加成员' },
  { title: '建立档案' }
]);

// 家庭数据
const familyData = ref({
  name: '',
  address: ''
});

// 家庭成员
const familyMembers = ref([]);

// 新成员表单
const newMember = ref({
  name: '',
  relationship: '',
  birthday: ''
});

// 档案选项
const selectedOption = ref('');

// 计算年龄
const calculateAge = (birthday: string) => {
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

// 是否可以添加成员
const canAddMember = computed(() => {
  return newMember.value.name && 
         newMember.value.relationship && 
         newMember.value.birthday;
});

// 是否可以进行下一步
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return familyData.value.name.trim() !== '';
  }
  if (currentStep.value === 1) {
    return familyMembers.value.length > 0;
  }
  return true;
});

// 添加成员
const addMember = () => {
  if (!canAddMember.value) return;
  
  const age = calculateAge(newMember.value.birthday);
  
  familyMembers.value.push({
    ...newMember.value,
    age
  });
  
  // 重置表单
  newMember.value = {
    name: '',
    relationship: '',
    birthday: ''
  };
};

// 移除成员
const removeMember = (index: number) => {
  familyMembers.value.splice(index, 1);
};

// 下一步
const nextStep = () => {
  if (canProceed.value && currentStep.value < steps.value.length - 1) {
    currentStep.value++;
  }
};

// 上一步
const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 完成设置
const completeSetup = () => {
  if (!selectedOption.value) return;
  
  console.log('家庭设置完成:', {
    family: familyData.value,
    members: familyMembers.value,
    archiveOption: selectedOption.value
  });
  
  // 根据选择的档案选项跳转到不同页面
  switch (selectedOption.value) {
    case 'upload':
      router.push('/archive/health-center?action=upload');
      break;
    case 'device':
      router.push('/archive/health-center?action=connect');
      break;
    case 'manual':
      router.push('/archive/personal-info?action=manual');
      break;
    default:
      router.push('/dashboard');
  }
};
</script>

<style scoped>
.family-setup-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-4);
}

.setup-container {
  width: 100%;
  max-width: 800px;
  background-color: var(--theme-bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
}

.steps-header {
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-6) var(--spacing-8);
  border-bottom: 1px solid var(--theme-border);
}

.steps-indicator {
  display: flex;
  justify-content: center;
  gap: var(--spacing-8);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  opacity: 0.5;
  transition: opacity var(--transition-normal);
}

.step-item.active,
.step-item.completed {
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-gray-300);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  transition: background-color var(--transition-normal);
}

.step-item.active .step-number {
  background-color: var(--color-primary);
}

.step-item.completed .step-number {
  background-color: var(--color-success);
}

.step-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
  text-align: center;
}

.step-item.active .step-title,
.step-item.completed .step-title {
  color: var(--theme-text-primary);
}

.step-content {
  padding: var(--spacing-8);
  min-height: 400px;
}

.step-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.panel-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.panel-subtitle {
  font-size: var(--font-size-base);
  color: var(--theme-text-secondary);
  margin: 0;
}

.panel-body {
  flex: 1;
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
}

.form-input,
.form-select {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: border-color var(--transition-normal);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.members-list {
  margin-bottom: var(--spacing-6);
}

.member-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--theme-bg-secondary);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-3);
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.avatar-text {
  font-size: var(--font-size-lg);
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.member-details {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
}

.remove-member-btn {
  width: 32px;
  height: 32px;
  border: none;
  background-color: var(--color-danger-light);
  color: var(--color-danger);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  transition: background-color var(--transition-normal);
}

.remove-member-btn:hover {
  background-color: var(--color-danger);
  color: white;
}

.add-member-form {
  background-color: var(--theme-bg-secondary);
  padding: var(--spacing-6);
  border-radius: var(--border-radius-md);
}

.form-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.add-member-btn {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.add-member-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.add-member-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.archive-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-6);
}

.option-card {
  padding: var(--spacing-6);
  border: 2px solid var(--theme-border);
  border-radius: var(--border-radius-lg);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.option-card:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
}

.option-card.selected {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.option-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-4);
}

.option-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.option-description {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-6) var(--spacing-8);
  background-color: var(--theme-bg-secondary);
  border-top: 1px solid var(--theme-border);
}

.nav-btn {
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.prev-btn {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border);
}

.prev-btn:hover {
  background-color: var(--color-gray-100);
}

.next-btn,
.complete-btn {
  background-color: var(--color-primary);
  color: white;
}

.next-btn:hover:not(:disabled),
.complete-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.next-btn:disabled,
.complete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setup-container {
    margin: var(--spacing-4);
  }
  
  .steps-indicator {
    gap: var(--spacing-4);
  }
  
  .step-content {
    padding: var(--spacing-6);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .archive-options {
    grid-template-columns: 1fr;
  }
  
  .navigation-buttons {
    padding: var(--spacing-4);
  }
}
</style>
