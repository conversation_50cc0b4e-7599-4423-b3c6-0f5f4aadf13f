import type {
  Medicine,
  MedicineStats,
  MedicineFilter,
  MedicineFormData,
  StockAdjustment,
} from "@/types/medicine";

// 家庭药品接口
export interface FamilyMedicine extends Medicine {
  familyId: string;
  addedBy: string; // 添加者用户ID
  sharedWith: string[]; // 共享给的家庭成员ID列表
}

// 模拟家庭药品数据
const mockFamilyMedicines: FamilyMedicine[] = [
  {
    id: "f1",
    name: "感冒灵颗粒",
    category: "otc",
    dosage: "10g",
    unit: "袋",
    quantity: 15,
    minQuantity: 5,
    expiryDate: "2025-06-30",
    manufacturer: "同仁堂",
    batchNumber: "TR20240201",
    instructions: "每次1袋，每日3次，温水冲服",
    sideEffects: "偶见恶心、头晕",
    storage: "密封，置阴凉干燥处",
    price: 28.5,
    purchaseDate: "2024-02-01",
    location: "客厅药箱",
    notes: "家庭常备感冒药",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-02-01T10:00:00Z",
    updatedAt: "2024-02-01T10:00:00Z",
  },
  {
    id: "f2",
    name: "布洛芬缓释胶囊",
    category: "otc",
    dosage: "300mg",
    unit: "粒",
    quantity: 8,
    minQuantity: 12,
    expiryDate: "2024-09-15",
    manufacturer: "扬子江药业",
    batchNumber: "YZ20240115",
    instructions: "每次1粒，每日2次，饭后服用",
    sideEffects: "可能出现胃肠道不适",
    contraindications: "胃溃疡患者禁用",
    storage: "密封保存",
    price: 15.8,
    purchaseDate: "2024-01-15",
    location: "主卧药箱",
    notes: "退热止痛药",
    userId: "2",
    familyId: "family1",
    addedBy: "2",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "f3",
    name: "复合维生素片",
    category: "supplement",
    dosage: "1000mg",
    unit: "片",
    quantity: 45,
    minQuantity: 20,
    expiryDate: "2025-12-31",
    manufacturer: "善存",
    instructions: "每日1片，饭后服用",
    storage: "避光，干燥处保存",
    price: 89.0,
    purchaseDate: "2024-01-01",
    location: "餐厅药箱",
    notes: "全家营养补充",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-01-01T10:00:00Z",
    updatedAt: "2024-01-01T10:00:00Z",
  },
  {
    id: "f4",
    name: "创可贴",
    category: "other",
    dosage: "标准型",
    unit: "片",
    quantity: 25,
    minQuantity: 10,
    expiryDate: "2026-03-15",
    manufacturer: "邦迪",
    batchNumber: "BD20240301",
    instructions: "清洁伤口后贴敷",
    sideEffects: "个别人可能对胶布过敏",
    contraindications: "深度伤口不适用",
    storage: "干燥处保存",
    price: 8.5,
    purchaseDate: "2024-03-01",
    location: "客厅药箱",
    notes: "外伤处理",
    userId: "2",
    familyId: "family1",
    addedBy: "2",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-03-01T10:00:00Z",
    updatedAt: "2024-03-01T10:00:00Z",
  },
  {
    id: "f5",
    name: "碘伏消毒液",
    category: "other",
    dosage: "100ml",
    unit: "瓶",
    quantity: 2,
    minQuantity: 1,
    expiryDate: "2025-08-20",
    manufacturer: "利康",
    batchNumber: "LK20240401",
    instructions: "外用，涂抹于伤口周围",
    sideEffects: "偶见皮肤刺激",
    contraindications: "碘过敏者禁用",
    storage: "避光，密封保存",
    price: 12.0,
    purchaseDate: "2024-04-01",
    location: "卫生间药箱",
    notes: "外伤消毒",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-04-01T10:00:00Z",
    updatedAt: "2024-04-01T10:00:00Z",
  },
  {
    id: "f6",
    name: "小儿退热贴",
    category: "other",
    dosage: "5cm×12cm",
    unit: "贴",
    quantity: 12,
    minQuantity: 6,
    expiryDate: "2025-10-15",
    manufacturer: "小林制药",
    batchNumber: "XL20240501",
    instructions: "贴于额头，每次使用8小时",
    sideEffects: "个别人可能皮肤过敏",
    contraindications: "皮肤破损处禁用",
    storage: "阴凉干燥处保存",
    price: 18.8,
    purchaseDate: "2024-05-01",
    location: "儿童房药箱",
    notes: "儿童退热",
    userId: "3",
    familyId: "family1",
    addedBy: "3",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-05-01T10:00:00Z",
    updatedAt: "2024-05-01T10:00:00Z",
  },
  {
    id: "f7",
    name: "健胃消食片",
    category: "otc",
    dosage: "0.8g",
    unit: "片",
    quantity: 36,
    minQuantity: 20,
    expiryDate: "2024-12-30",
    manufacturer: "江中制药",
    batchNumber: "JZ20240201",
    instructions: "嚼服，一次4-6片，一日3次",
    sideEffects: "偶见轻微腹泻",
    contraindications: "糖尿病患者慎用",
    storage: "密封，在干燥处保存",
    price: 15.5,
    purchaseDate: "2024-02-01",
    location: "餐厅药箱",
    notes: "消化不良",
    userId: "2",
    familyId: "family1",
    addedBy: "2",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-02-01T10:00:00Z",
    updatedAt: "2024-02-01T10:00:00Z",
  },
  {
    id: "f8",
    name: "体温计",
    category: "other",
    dosage: "电子式",
    unit: "支",
    quantity: 2,
    minQuantity: 1,
    expiryDate: "2030-01-01",
    manufacturer: "欧姆龙",
    batchNumber: "OM20240301",
    instructions: "口腔或腋下测量3-5分钟",
    sideEffects: "无",
    contraindications: "无",
    storage: "干燥处保存",
    price: 35.0,
    purchaseDate: "2024-03-01",
    location: "主卧药箱",
    notes: "体温监测",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-03-01T10:00:00Z",
    updatedAt: "2024-03-01T10:00:00Z",
  },
  {
    id: "f9",
    name: "藿香正气水",
    category: "otc",
    dosage: "10ml",
    unit: "支",
    quantity: 10,
    minQuantity: 6,
    expiryDate: "2025-07-20",
    manufacturer: "太极集团",
    batchNumber: "TJ20240315",
    instructions: "口服，一次半支至1支，一日2-3次",
    sideEffects: "偶见恶心、呕吐",
    contraindications: "酒精过敏者禁用",
    storage: "密封，置阴凉处",
    price: 9.8,
    purchaseDate: "2024-03-20",
    location: "客厅药箱",
    notes: "解暑祛湿",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-03-20T10:00:00Z",
    updatedAt: "2024-03-20T10:00:00Z",
  },
  {
    id: "f10",
    name: "风油精",
    category: "otc",
    dosage: "3ml",
    unit: "瓶",
    quantity: 3,
    minQuantity: 2,
    expiryDate: "2026-12-31",
    manufacturer: "中华老字号",
    batchNumber: "ZH20240401",
    instructions: "外用，涂擦患处",
    sideEffects: "偶见皮肤刺激",
    contraindications: "皮肤破损处禁用",
    storage: "密封，避免高温",
    price: 6.5,
    purchaseDate: "2024-04-05",
    location: "客厅药箱",
    notes: "驱蚊止痒",
    userId: "2",
    familyId: "family1",
    addedBy: "2",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-04-05T10:00:00Z",
    updatedAt: "2024-04-05T10:00:00Z",
  },
  {
    id: "f11",
    name: "板蓝根颗粒",
    category: "otc",
    dosage: "10g",
    unit: "袋",
    quantity: 20,
    minQuantity: 10,
    expiryDate: "2024-11-30",
    manufacturer: "白云山",
    batchNumber: "BY20240210",
    instructions: "开水冲服，一次1袋，一日3-4次",
    sideEffects: "偶见腹泻",
    contraindications: "脾胃虚寒者慎用",
    storage: "密封，置干燥处",
    price: 13.2,
    purchaseDate: "2024-02-15",
    location: "客厅药箱",
    notes: "清热解毒",
    userId: "3",
    familyId: "family1",
    addedBy: "3",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-02-15T10:00:00Z",
    updatedAt: "2024-02-15T10:00:00Z",
  },
  {
    id: "f12",
    name: "创可贴(防水型)",
    category: "other",
    dosage: "标准型",
    unit: "片",
    quantity: 25,
    minQuantity: 15,
    expiryDate: "2026-06-30",
    manufacturer: "3M",
    batchNumber: "3M20240401",
    instructions: "清洁伤口后贴敷",
    sideEffects: "个别人可能对胶布过敏",
    contraindications: "深度伤口不适用",
    storage: "干燥处保存",
    price: 15.0,
    purchaseDate: "2024-04-10",
    location: "卫生间药箱",
    notes: "防水创可贴",
    userId: "1",
    familyId: "family1",
    addedBy: "1",
    sharedWith: ["1", "2", "3"],
    createdAt: "2024-04-10T10:00:00Z",
    updatedAt: "2024-04-10T10:00:00Z",
  },
];

class FamilyMedicineService {
  // 获取家庭药品列表
  async getFamilyMedicines(
    familyId: string,
    filter?: MedicineFilter
  ): Promise<FamilyMedicine[]> {
    let medicines = mockFamilyMedicines.filter((m) => m.familyId === familyId);

    if (filter?.category && filter.category !== "all") {
      medicines = medicines.filter((m) => m.category === filter.category);
    }

    if (filter?.search) {
      const search = filter.search.toLowerCase();
      medicines = medicines.filter(
        (m) =>
          m.name.toLowerCase().includes(search) ||
          m.manufacturer.toLowerCase().includes(search)
      );
    }

    if (filter?.status && filter.status !== "all") {
      const now = new Date();
      const thirtyDaysLater = new Date(
        now.getTime() + 30 * 24 * 60 * 60 * 1000
      );

      medicines = medicines.filter((m) => {
        const expiryDate = new Date(m.expiryDate);

        switch (filter.status) {
          case "expiring":
            return expiryDate <= thirtyDaysLater && expiryDate > now;
          case "expired":
            return expiryDate <= now;
          case "lowStock":
            return m.quantity <= m.minQuantity;
          default:
            return true;
        }
      });
    }

    return medicines;
  }

  // 获取家庭药品统计
  async getFamilyMedicineStats(familyId: string): Promise<MedicineStats> {
    const medicines = await this.getFamilyMedicines(familyId);
    const now = new Date();
    const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const stats: MedicineStats = {
      total: medicines.length,
      expiringSoon: 0,
      lowStock: 0,
      expired: 0,
      byCategory: {
        prescription: 0,
        otc: 0,
        supplement: 0,
        other: 0,
      },
    };

    medicines.forEach((medicine) => {
      const expiryDate = new Date(medicine.expiryDate);

      if (expiryDate <= now) {
        stats.expired++;
      } else if (expiryDate <= thirtyDaysLater) {
        stats.expiringSoon++;
      }

      if (medicine.quantity <= medicine.minQuantity) {
        stats.lowStock++;
      }

      stats.byCategory[medicine.category]++;
    });

    return stats;
  }

  // 向家庭药箱添加药品
  async addFamilyMedicine(
    familyId: string,
    userId: string,
    data: MedicineFormData
  ): Promise<FamilyMedicine> {
    const newMedicine: FamilyMedicine = {
      id: "f" + Date.now().toString(),
      ...data,
      userId,
      familyId,
      addedBy: userId,
      sharedWith: [userId], // 默认只共享给添加者
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockFamilyMedicines.push(newMedicine);
    return newMedicine;
  }

  // 获取特定家庭药品详情
  async getFamilyMedicineDetails(
    familyId: string,
    medicineId: string
  ): Promise<FamilyMedicine | null> {
    const medicine = mockFamilyMedicines.find(
      (m) => m.id === medicineId && m.familyId === familyId
    );
    return medicine || null;
  }

  // 更新家庭药品信息
  async updateFamilyMedicine(
    familyId: string,
    medicineId: string,
    data: Partial<MedicineFormData>
  ): Promise<FamilyMedicine> {
    const index = mockFamilyMedicines.findIndex(
      (m) => m.id === medicineId && m.familyId === familyId
    );
    if (index === -1) {
      throw new Error("药品不存在");
    }

    mockFamilyMedicines[index] = {
      ...mockFamilyMedicines[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    return mockFamilyMedicines[index];
  }

  // 从家庭药箱移除药品
  async removeFamilyMedicine(
    familyId: string,
    medicineId: string
  ): Promise<void> {
    const index = mockFamilyMedicines.findIndex(
      (m) => m.id === medicineId && m.familyId === familyId
    );
    if (index === -1) {
      throw new Error("药品不存在");
    }

    mockFamilyMedicines.splice(index, 1);
  }

  // 调整家庭药品库存
  async adjustFamilyMedicineStock(
    familyId: string,
    adjustment: StockAdjustment
  ): Promise<FamilyMedicine> {
    const medicine = mockFamilyMedicines.find(
      (m) => m.id === adjustment.medicineId && m.familyId === familyId
    );
    if (!medicine) {
      throw new Error("药品不存在");
    }

    switch (adjustment.type) {
      case "add":
        medicine.quantity += adjustment.quantity;
        break;
      case "subtract":
        medicine.quantity = Math.max(
          0,
          medicine.quantity - adjustment.quantity
        );
        break;
      case "set":
        medicine.quantity = adjustment.quantity;
        break;
    }

    medicine.updatedAt = new Date().toISOString();
    return medicine;
  }

  // 导出家庭药品清单
  async exportFamilyMedicineList(familyId: string): Promise<string> {
    const medicines = await this.getFamilyMedicines(familyId);
    const csvContent = [
      "药品名称,类别,规格,数量,单位,最小库存,有效期,生产厂家,批号,用法用量,储存条件,添加者,位置,备注",
      ...medicines.map((m) =>
        [
          m.name,
          m.category,
          m.dosage,
          m.quantity,
          m.unit,
          m.minQuantity,
          m.expiryDate,
          m.manufacturer,
          m.batchNumber || "",
          m.instructions,
          m.storage,
          m.addedBy,
          m.location || "",
          m.notes || "",
        ].join(",")
      ),
    ].join("\n");

    return csvContent;
  }
}

export const familyMedicineService = new FamilyMedicineService();
export type { FamilyMedicine };
