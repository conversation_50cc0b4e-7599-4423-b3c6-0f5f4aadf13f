<template>
  <BaseChart
    :option="chartOption"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BaseChart from './BaseChart.vue';
import type { EChartsOption } from 'echarts';

interface DataItem {
  name: string;
  value: number;
  date?: string;
}

interface Props {
  data: DataItem[];
  title?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
  width?: string;
  height?: string;
  loading?: boolean;
  smooth?: boolean;
  showArea?: boolean;
  color?: string;
  colors?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  xAxisLabel: '',
  yAxisLabel: '',
  width: '100%',
  height: '400px',
  loading: false,
  smooth: true,
  showArea: false,
  color: '#3B82F6',
  colors: () => ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
});

const emit = defineEmits<{
  chartReady: [chart: any];
  chartClick: [params: any];
}>();

const chartOption = computed<EChartsOption>(() => {
  const xAxisData = props.data.map(item => item.date || item.name);
  const seriesData = props.data.map(item => item.value);

  return {
    title: {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#374151'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const param = Array.isArray(params) ? params[0] : params;
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      name: props.xAxisLabel,
      nameLocation: 'middle',
      nameGap: 30,
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisLabel: {
        color: '#6B7280'
      }
    },
    yAxis: {
      type: 'value',
      name: props.yAxisLabel,
      nameLocation: 'middle',
      nameGap: 40,
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisLabel: {
        color: '#6B7280'
      },
      splitLine: {
        lineStyle: {
          color: '#F3F4F6'
        }
      }
    },
    series: [
      {
        name: props.yAxisLabel || '数值',
        type: 'line',
        data: seriesData,
        smooth: props.smooth,
        lineStyle: {
          color: props.color,
          width: 2
        },
        itemStyle: {
          color: props.color
        },
        areaStyle: props.showArea ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: props.color + '40'
              },
              {
                offset: 1,
                color: props.color + '10'
              }
            ]
          }
        } : undefined,
        symbol: 'circle',
        symbolSize: 6,
        emphasis: {
          itemStyle: {
            borderColor: props.color,
            borderWidth: 2
          }
        }
      }
    ]
  };
});

const handleChartReady = (chart: any) => {
  emit('chartReady', chart);
};

const handleChartClick = (params: any) => {
  emit('chartClick', params);
};
</script>
