<template>
  <BaseChart
    :option="chartOption"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BaseChart from './BaseChart.vue';
import type { EChartsOption } from 'echarts';

interface DataItem {
  name: string;
  value: number;
  color?: string;
}

interface Props {
  data: DataItem[];
  title?: string;
  width?: string;
  height?: string;
  loading?: boolean;
  showLegend?: boolean;
  radius?: string | [string, string];
  colors?: string[];
  center?: [string, string];
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  width: '100%',
  height: '400px',
  loading: false,
  showLegend: true,
  radius: '70%',
  colors: () => ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
  center: () => ['50%', '50%']
});

const emit = defineEmits<{
  chartReady: [chart: any];
  chartClick: [params: any];
}>();

const chartOption = computed<EChartsOption>(() => {
  const seriesData = props.data.map((item, index) => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: item.color || props.colors[index % props.colors.length]
    }
  }));

  return {
    title: {
      text: props.title,
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#374151'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: props.showLegend ? {
      type: 'scroll',
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      textStyle: {
        color: '#6B7280'
      },
      pageButtonItemGap: 5,
      pageButtonGap: 20,
      pageButtonPosition: 'end',
      pageFormatter: '{current}/{total}',
      pageIconColor: '#3B82F6',
      pageIconInactiveColor: '#D1D5DB',
      pageIconSize: 12,
      pageTextStyle: {
        color: '#6B7280'
      }
    } : undefined,
    series: [
      {
        name: props.title || '数据分布',
        type: 'pie',
        radius: props.radius,
        center: props.center,
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {d}%',
          fontSize: 12,
          color: '#6B7280'
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#D1D5DB'
          }
        }
      }
    ]
  };
});

const handleChartReady = (chart: any) => {
  emit('chartReady', chart);
};

const handleChartClick = (params: any) => {
  emit('chartClick', params);
};
</script>
