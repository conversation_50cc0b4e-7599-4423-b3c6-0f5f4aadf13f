import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";
import { setupRouterGuards, handleNavigationError } from "./guards";

// 导入路由模块
import { authRoutes } from "./modules/auth";
import { dashboardRoutes } from "./modules/dashboard";
import { archiveRoutes } from "./modules/archive";
import { familyRoutes } from "./modules/family";
import { assistantRoutes } from "./modules/assistant";
import { calendarRoutes } from "./modules/calendar";
import { activitiesRoutes } from "./modules/activities";
import { settingsRoutes } from "./modules/settings";

// 基础路由
const baseRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Root",
    redirect: (to) => {
      // 检查是否已登录
      const isAuthenticated = localStorage.getItem("auth_token") !== null;
      return isAuthenticated ? "/dashboard" : "/login";
    },
    meta: {
      title: "家庭健康管理系统",
      hidden: true,
    },
  },
];

// 主应用布局路由
const mainRoutes: RouteRecordRaw = {
  path: "/",
  component: () => import("@/components/layout/MainLayout.vue"),
  meta: {
    requiresAuth: true,
  },
  children: [
    ...dashboardRoutes,
    ...archiveRoutes,
    ...familyRoutes,
    ...assistantRoutes,
    ...calendarRoutes,
    ...activitiesRoutes,
    ...settingsRoutes,
  ],
};

// 错误页面路由
const errorRoutes: RouteRecordRaw[] = [
  {
    path: "/403",
    name: "Forbidden",
    component: () => import("@/views/error/403.vue"),
    meta: {
      title: "403 - 访问被拒绝",
      hidden: true,
    },
  },
  {
    path: "/404",
    name: "NotFound",
    component: () => import("@/views/error/404.vue"),
    meta: {
      title: "404 - 页面不存在",
      hidden: true,
    },
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
  },
];

// 合并所有路由
const routes: RouteRecordRaw[] = [
  ...baseRoutes,
  ...authRoutes,
  mainRoutes,
  ...errorRoutes,
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 设置路由守卫
setupRouterGuards(router);

// 处理导航错误
router.onError(handleNavigationError);

export default router;
