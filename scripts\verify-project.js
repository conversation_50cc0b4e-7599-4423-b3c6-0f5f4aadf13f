#!/usr/bin/env node

/**
 * 项目验证脚本
 * 检查项目结构、文件完整性和基本配置
 */

import fs from "fs";

// 颜色输出
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查文件是否存在
function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    log(`✓ ${description}`, "green");
  } else {
    log(`✗ ${description}`, "red");
  }
  return exists;
}

// 检查目录是否存在
function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  if (exists) {
    log(`✓ ${description}`, "green");
  } else {
    log(`✗ ${description}`, "red");
  }
  return exists;
}

// 检查 package.json 依赖
function checkDependencies() {
  log("\n📦 检查项目依赖...", "blue");

  const packageJsonPath = "package.json";
  if (!checkFile(packageJsonPath, "package.json 文件存在")) {
    return false;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
    const requiredDeps = ["vue", "vue-router"];
    const requiredDevDeps = ["vite", "@vitejs/plugin-vue", "typescript"];

    let allDepsOk = true;

    requiredDeps.forEach((dep) => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        log(`✓ 依赖 ${dep} 已安装`, "green");
      } else {
        log(`✗ 缺少依赖 ${dep}`, "red");
        allDepsOk = false;
      }
    });

    requiredDevDeps.forEach((dep) => {
      if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        log(`✓ 开发依赖 ${dep} 已安装`, "green");
      } else {
        log(`✗ 缺少开发依赖 ${dep}`, "red");
        allDepsOk = false;
      }
    });

    return allDepsOk;
  } catch (error) {
    log(`✗ 读取 package.json 失败: ${error.message}`, "red");
    return false;
  }
}

// 检查项目结构
function checkProjectStructure() {
  log("\n📁 检查项目结构...", "blue");

  const requiredDirs = [
    { path: "src", desc: "src 源码目录" },
    { path: "src/components", desc: "components 组件目录" },
    { path: "src/components/common", desc: "common 通用组件目录" },
    { path: "src/components/layout", desc: "layout 布局组件目录" },
    { path: "src/views", desc: "views 页面目录" },
    { path: "src/router", desc: "router 路由目录" },
    { path: "src/styles", desc: "styles 样式目录" },
    { path: "src/types", desc: "types 类型定义目录" },
    { path: "src/utils", desc: "utils 工具函数目录" },
    { path: "docs", desc: "docs 文档目录" },
    { path: "tests", desc: "tests 测试目录" },
  ];

  let allDirsOk = true;
  requiredDirs.forEach((dir) => {
    if (!checkDirectory(dir.path, dir.desc)) {
      allDirsOk = false;
    }
  });

  return allDirsOk;
}

// 检查核心文件
function checkCoreFiles() {
  log("\n📄 检查核心文件...", "blue");

  const requiredFiles = [
    { path: "src/main.ts", desc: "应用入口文件" },
    { path: "src/App.vue", desc: "根组件文件" },
    { path: "src/router/index.ts", desc: "路由配置文件" },
    { path: "src/components/layout/MainLayout.vue", desc: "主布局组件" },
    { path: "src/components/common/PageContainer.vue", desc: "页面容器组件" },
    { path: "src/components/common/ContentCard.vue", desc: "内容卡片组件" },
    { path: "src/components/common/StatCard.vue", desc: "统计卡片组件" },
    { path: "src/styles/variables.css", desc: "样式变量文件" },
    { path: "src/styles/common.css", desc: "通用样式文件" },
    { path: "src/styles/components.css", desc: "组件样式文件" },
    { path: "vite.config.ts", desc: "Vite 配置文件" },
    { path: "tsconfig.json", desc: "TypeScript 配置文件" },
  ];

  let allFilesOk = true;
  requiredFiles.forEach((file) => {
    if (!checkFile(file.path, file.desc)) {
      allFilesOk = false;
    }
  });

  return allFilesOk;
}

// 检查页面文件
function checkPageFiles() {
  log("\n🖼️ 检查页面文件...", "blue");

  const pageFiles = [
    { path: "src/views/dashboard/Dashboard.vue", desc: "首页" },
    { path: "src/views/archive/Archive.vue", desc: "档案页面" },
    { path: "src/views/family/Family.vue", desc: "家庭页面" },
    { path: "src/views/assistant/Assistant.vue", desc: "智能助手页面" },
    { path: "src/views/calendar/Calendar.vue", desc: "日历页面" },
    { path: "src/views/activities/Activities.vue", desc: "活动页面" },
    { path: "src/views/settings/Settings.vue", desc: "设置页面" },
    { path: "src/views/error/404.vue", desc: "404错误页面" },
    { path: "src/views/error/403.vue", desc: "403错误页面" },
  ];

  let allPagesOk = true;
  pageFiles.forEach((page) => {
    if (!checkFile(page.path, page.desc)) {
      allPagesOk = false;
    }
  });

  return allPagesOk;
}

// 检查文档文件
function checkDocumentation() {
  log("\n📚 检查文档文件...", "blue");

  const docFiles = [
    { path: "README.md", desc: "项目说明文档" },
    { path: "docs/PROJECT_SPECIFICATION.md", desc: "项目规范文档" },
    { path: "docs/DIRECTORY_STRUCTURE.md", desc: "目录结构文档" },
    { path: "docs/DESIGN_SYSTEM.md", desc: "设计系统文档" },
  ];

  let allDocsOk = true;
  docFiles.forEach((doc) => {
    if (!checkFile(doc.path, doc.desc)) {
      allDocsOk = false;
    }
  });

  return allDocsOk;
}

// 主验证函数
function main() {
  log("🔍 开始项目验证...", "blue");
  log("=====================================", "blue");

  const results = {
    dependencies: checkDependencies(),
    structure: checkProjectStructure(),
    coreFiles: checkCoreFiles(),
    pageFiles: checkPageFiles(),
    documentation: checkDocumentation(),
  };

  log("\n📊 验证结果汇总:", "blue");
  log("=====================================", "blue");

  Object.entries(results).forEach(([key, result]) => {
    const status = result ? "✓ 通过" : "✗ 失败";
    const color = result ? "green" : "red";
    const description = {
      dependencies: "项目依赖",
      structure: "项目结构",
      coreFiles: "核心文件",
      pageFiles: "页面文件",
      documentation: "文档文件",
    }[key];

    log(`${status} ${description}`, color);
  });

  const allPassed = Object.values(results).every((result) => result);

  log("\n🎯 总体结果:", "blue");
  if (allPassed) {
    log("✅ 项目验证通过！所有检查项目都正常。", "green");
  } else {
    log("❌ 项目验证失败！请检查上述失败项目。", "red");
  }

  log("\n💡 建议:", "yellow");
  log("- 运行 npm run dev 启动开发服务器", "yellow");
  log("- 在浏览器中访问 http://localhost:5174", "yellow");
  log("- 检查控制台是否有错误信息", "yellow");
  log("- 测试各个页面的导航和功能", "yellow");

  process.exit(allPassed ? 0 : 1);
}

// 运行验证
main();
