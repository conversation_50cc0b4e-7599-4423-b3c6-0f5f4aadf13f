# 目录结构规范

## 项目整体结构

```
family-health-management/
├── public/                     # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/                        # 源代码
│   ├── components/             # 公共组件
│   ├── views/                  # 页面组件
│   ├── stores/                 # 状态管理
│   ├── types/                  # 类型定义
│   ├── utils/                  # 工具函数
│   ├── styles/                 # 样式文件
│   ├── router/                 # 路由配置
│   ├── assets/                 # 静态资源
│   ├── App.vue                 # 根组件
│   ├── main.ts                 # 入口文件
│   └── vite-env.d.ts          # Vite 类型声明
├── docs/                       # 项目文档
├── tests/                      # 测试文件
├── package.json                # 项目配置
├── vite.config.ts             # Vite 配置
├── tsconfig.json              # TypeScript 配置
└── README.md                  # 项目说明
```

## 详细目录说明

### 1. src/components/ - 公共组件

```
components/
├── common/                     # 通用组件
│   ├── PageContainer.vue       # 页面容器组件
│   ├── ContentCard.vue         # 内容卡片组件
│   ├── StatCard.vue           # 统计卡片组件
│   ├── LoadingSpinner.vue     # 加载动画组件
│   ├── EmptyState.vue         # 空状态组件
│   └── index.ts               # 组件导出
├── layout/                     # 布局组件
│   ├── MainLayout.vue         # 主布局组件
│   ├── Sidebar.vue            # 侧边栏组件
│   ├── Header.vue             # 头部组件
│   └── index.ts               # 布局组件导出
├── forms/                      # 表单组件
│   ├── BaseForm.vue           # 基础表单组件
│   ├── FormField.vue          # 表单字段组件
│   └── index.ts               # 表单组件导出
└── charts/                     # 图表组件
    ├── LineChart.vue          # 折线图组件
    ├── BarChart.vue           # 柱状图组件
    └── index.ts               # 图表组件导出
```

### 2. src/views/ - 页面组件

```
views/
├── auth/                       # 认证相关页面
│   ├── Login.vue              # 登录页面
│   ├── Register.vue           # 注册页面
│   ├── Onboarding.vue         # 产品引导页面
│   └── FamilySetup.vue        # 家庭设置页面
├── dashboard/                  # 首页模块
│   └── Dashboard.vue          # 首页仪表板
├── archive/                    # 档案模块
│   ├── Archive.vue            # 档案主页
│   ├── PersonalInfo.vue       # 个人信息页面
│   ├── HealthCenter.vue       # 健康档案中心
│   ├── MedicineBox.vue        # 个人药箱
│   ├── HealthOverview.vue     # 综合健康概要
│   └── EmergencyCard.vue      # 紧急资料卡
├── family/                     # 家庭模块
│   └── Family.vue             # 家庭管理页面
├── assistant/                  # 智能助手模块
│   ├── Assistant.vue          # 助手主页
│   ├── SmartChat.vue          # 智能对话
│   ├── HealthAnalysis.vue     # 健康分析
│   ├── HealthReport.vue       # 健康报告
│   ├── HealthSuggestions.vue  # 健康建议
│   └── HealthPrediction.vue   # 健康预测
├── calendar/                   # 日历模块
│   └── Calendar.vue           # 健康日历
├── activities/                 # 活动模块
│   ├── Activities.vue         # 活动主页
│   ├── ActivityList.vue       # 活动列表
│   ├── ActivityTimeline.vue   # 活动时间线
│   └── ActivityAnalysis.vue   # 活动数据分析
└── settings/                   # 设置模块
    └── Settings.vue           # 系统设置
```

### 3. src/stores/ - 状态管理

```
stores/
├── modules/                    # 模块化 store
│   ├── auth.ts                # 认证状态
│   ├── user.ts                # 用户信息状态
│   ├── family.ts              # 家庭信息状态
│   ├── health.ts              # 健康数据状态
│   ├── calendar.ts            # 日历状态
│   ├── activities.ts          # 活动状态
│   └── settings.ts            # 设置状态
└── index.ts                   # store 入口文件
```

### 4. src/types/ - 类型定义

```
types/
├── auth.ts                     # 认证相关类型
├── user.ts                     # 用户相关类型
├── family.ts                   # 家庭相关类型
├── health.ts                   # 健康数据类型
├── calendar.ts                 # 日历相关类型
├── activities.ts               # 活动相关类型
├── api.ts                      # API 响应类型
├── common.ts                   # 通用类型
└── index.ts                    # 类型导出
```

### 5. src/utils/ - 工具函数

```
utils/
├── request.ts                  # HTTP 请求封装
├── storage.ts                  # 本地存储工具
├── validation.ts               # 表单验证工具
├── format.ts                   # 数据格式化工具
├── date.ts                     # 日期处理工具
├── health.ts                   # 健康数据处理工具
├── constants.ts                # 常量定义
└── index.ts                    # 工具函数导出
```

### 6. src/styles/ - 样式文件

```
styles/
├── variables.css               # CSS 变量定义
├── reset.css                   # 样式重置
├── common.css                  # 通用样式
├── components.css              # 组件样式
├── layout.css                  # 布局样式
├── themes/                     # 主题样式
│   ├── light.css              # 浅色主题
│   └── dark.css               # 深色主题
└── index.css                   # 样式入口文件
```

### 7. src/router/ - 路由配置

```
router/
├── modules/                    # 路由模块
│   ├── auth.ts                # 认证路由
│   ├── dashboard.ts           # 首页路由
│   ├── archive.ts             # 档案路由
│   ├── family.ts              # 家庭路由
│   ├── assistant.ts           # 助手路由
│   ├── calendar.ts            # 日历路由
│   ├── activities.ts          # 活动路由
│   └── settings.ts            # 设置路由
├── guards.ts                   # 路由守卫
└── index.ts                    # 路由入口文件
```

### 8. docs/ - 项目文档

```
docs/
├── PROJECT_SPECIFICATION.md   # 项目规范文档
├── DIRECTORY_STRUCTURE.md     # 目录结构说明
├── DEVELOPMENT_GUIDE.md       # 开发指南
├── DESIGN_SYSTEM.md          # 设计系统文档
├── API_DOCUMENTATION.md      # API 文档
├── DEPLOYMENT_GUIDE.md       # 部署指南
└── CHANGELOG.md              # 更新日志
```

### 9. tests/ - 测试文件

```
tests/
├── unit/                       # 单元测试
│   ├── components/            # 组件测试
│   ├── utils/                 # 工具函数测试
│   └── stores/                # 状态管理测试
├── integration/                # 集成测试
│   ├── auth.test.ts           # 认证流程测试
│   ├── health-data.test.ts    # 健康数据流程测试
│   └── family.test.ts         # 家庭管理流程测试
├── e2e/                        # 端到端测试
│   ├── user-journey.test.ts   # 用户旅程测试
│   └── critical-path.test.ts  # 关键路径测试
└── setup.ts                   # 测试配置
```

## 文件命名规范

### 1. 组件文件
- 使用 PascalCase: `PersonalInfo.vue`
- 组件名应该清晰描述其功能
- 避免使用缩写，除非是广泛认知的缩写

### 2. 工具文件
- 使用 camelCase: `formatDate.ts`
- 文件名应该描述其主要功能
- 相关功能可以组织在同一个文件中

### 3. 类型文件
- 使用 camelCase: `userTypes.ts`
- 按功能模块组织类型定义
- 导出时使用 PascalCase

### 4. 样式文件
- 使用 kebab-case: `common-styles.css`
- 按功能或组件组织样式
- 使用有意义的类名

## 导入导出规范

### 1. 组件导入
```typescript
// 推荐：使用绝对路径
import PersonalInfo from '@/views/archive/PersonalInfo.vue'

// 避免：使用相对路径
import PersonalInfo from '../archive/PersonalInfo.vue'
```

### 2. 工具函数导入
```typescript
// 推荐：从 index 文件导入
import { formatDate, validateEmail } from '@/utils'

// 或者直接导入
import { formatDate } from '@/utils/format'
```

### 3. 类型导入
```typescript
// 推荐：使用 type 关键字
import type { User, Family } from '@/types'

// 混合导入
import { type User, validateUser } from '@/types/user'
```

## 目录组织原则

### 1. 按功能模块组织
- 相关的文件应该放在同一个目录下
- 每个模块应该有清晰的边界
- 避免过深的目录嵌套（建议不超过 3 层）

### 2. 按文件类型组织
- 同类型的文件放在对应的目录下
- 保持目录结构的一致性
- 便于查找和维护

### 3. 可扩展性考虑
- 目录结构应该支持项目的扩展
- 新增功能时应该遵循现有的组织方式
- 定期重构和优化目录结构

## 最佳实践

### 1. 保持目录结构简洁
- 避免创建不必要的嵌套目录
- 每个目录都应该有明确的用途
- 定期清理无用的文件和目录

### 2. 使用 index 文件
- 在每个组件目录下创建 index.ts 文件
- 统一导出该目录下的所有组件
- 简化导入路径

### 3. 文档同步更新
- 目录结构变更时及时更新文档
- 保持文档与实际结构的一致性
- 为新加入的开发者提供清晰的指引
