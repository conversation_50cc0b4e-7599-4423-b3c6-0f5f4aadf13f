<template>
  <el-dialog
    v-model="visible"
    title="家庭设置"
    width="600px"
    :before-close="handleClose"
  >
    <div class="settings-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="100px"
          >
            <el-form-item label="家庭名称" prop="name">
              <el-input
                v-model="basicForm.name"
                placeholder="请输入家庭名称"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="家庭描述" prop="description">
              <el-input
                v-model="basicForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入家庭描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="家庭地址" prop="address">
              <el-input
                v-model="basicForm.address"
                placeholder="请输入家庭地址"
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="basicForm.phone"
                placeholder="请输入联系电话"
                maxlength="20"
              />
            </el-form-item>

            <el-form-item label="家庭头像">
              <el-upload
                class="avatar-uploader"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :on-success="handleAvatarSuccess"
                action="#"
                :auto-upload="false"
              >
                <img v-if="basicForm.avatar" :src="basicForm.avatar" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 隐私设置 -->
        <el-tab-pane label="隐私设置" name="privacy">
          <div class="privacy-settings">
            <div class="setting-group">
              <h4 class="group-title">数据共享</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">成员间数据共享</div>
                  <div class="setting-desc">允许家庭成员查看彼此的健康数据</div>
                </div>
                <el-switch v-model="privacyForm.dataSharing" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">健康报告共享</div>
                  <div class="setting-desc">自动生成并共享家庭健康报告</div>
                </div>
                <el-switch v-model="privacyForm.reportSharing" />
              </div>
            </div>

            <div class="setting-group">
              <h4 class="group-title">访问控制</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">访客模式</div>
                  <div class="setting-desc">允许临时访客查看基本信息</div>
                </div>
                <el-switch v-model="privacyForm.guestMode" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">位置共享</div>
                  <div class="setting-desc">在紧急情况下共享位置信息</div>
                </div>
                <el-switch v-model="privacyForm.locationSharing" />
              </div>
            </div>

            <div class="setting-group">
              <h4 class="group-title">数据保留</h4>
              <el-form-item label="数据保留期限">
                <el-select v-model="privacyForm.dataRetention" placeholder="选择保留期限">
                  <el-option label="1年" value="1year" />
                  <el-option label="3年" value="3years" />
                  <el-option label="5年" value="5years" />
                  <el-option label="永久保留" value="forever" />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <div class="notification-settings">
            <div class="setting-group">
              <h4 class="group-title">健康提醒</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">用药提醒</div>
                  <div class="setting-desc">为家庭成员发送用药提醒</div>
                </div>
                <el-switch v-model="notificationForm.medicationReminder" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">体检提醒</div>
                  <div class="setting-desc">定期体检和健康检查提醒</div>
                </div>
                <el-switch v-model="notificationForm.checkupReminder" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">异常警报</div>
                  <div class="setting-desc">健康数据异常时发送警报</div>
                </div>
                <el-switch v-model="notificationForm.abnormalAlert" />
              </div>
            </div>

            <div class="setting-group">
              <h4 class="group-title">家庭动态</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">成员活动通知</div>
                  <div class="setting-desc">成员加入、离开等活动通知</div>
                </div>
                <el-switch v-model="notificationForm.memberActivity" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">权限变更通知</div>
                  <div class="setting-desc">成员权限变更时发送通知</div>
                </div>
                <el-switch v-model="notificationForm.permissionChange" />
              </div>
            </div>

            <div class="setting-group">
              <h4 class="group-title">通知方式</h4>
              <el-checkbox-group v-model="notificationForm.methods">
                <el-checkbox label="app" size="large">应用内通知</el-checkbox>
                <el-checkbox label="email" size="large">邮件通知</el-checkbox>
                <el-checkbox label="sms" size="large">短信通知</el-checkbox>
                <el-checkbox label="push" size="large">推送通知</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <div class="security-settings">
            <div class="setting-group">
              <h4 class="group-title">访问安全</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">双重认证</div>
                  <div class="setting-desc">启用双重认证提高账户安全性</div>
                </div>
                <el-switch v-model="securityForm.twoFactorAuth" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">登录验证</div>
                  <div class="setting-desc">新设备登录时需要验证</div>
                </div>
                <el-switch v-model="securityForm.loginVerification" />
              </div>
            </div>

            <div class="setting-group">
              <h4 class="group-title">会话管理</h4>
              <el-form-item label="会话超时">
                <el-select v-model="securityForm.sessionTimeout" placeholder="选择超时时间">
                  <el-option label="30分钟" value="30min" />
                  <el-option label="1小时" value="1hour" />
                  <el-option label="4小时" value="4hours" />
                  <el-option label="24小时" value="24hours" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="danger" @click="handleLogoutAllDevices">
                  登出所有设备
                </el-button>
              </el-form-item>
            </div>

            <div class="setting-group">
              <h4 class="group-title">数据安全</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">数据加密</div>
                  <div class="setting-desc">启用端到端数据加密</div>
                </div>
                <el-switch v-model="securityForm.dataEncryption" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-name">操作日志</div>
                  <div class="setting-desc">记录所有重要操作日志</div>
                </div>
                <el-switch v-model="securityForm.operationLog" />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">
          保存设置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'

interface Props {
  modelValue: boolean
  familyId?: number | string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const activeTab = ref('basic')
const basicFormRef = ref<FormInstance>()

// 基本信息表单
const basicForm = reactive({
  name: '',
  description: '',
  address: '',
  phone: '',
  avatar: ''
})

// 隐私设置表单
const privacyForm = reactive({
  dataSharing: true,
  reportSharing: true,
  guestMode: false,
  locationSharing: true,
  dataRetention: '3years'
})

// 通知设置表单
const notificationForm = reactive({
  medicationReminder: true,
  checkupReminder: true,
  abnormalAlert: true,
  memberActivity: true,
  permissionChange: true,
  methods: ['app', 'push']
})

// 安全设置表单
const securityForm = reactive({
  twoFactorAuth: false,
  loginVerification: true,
  sessionTimeout: '4hours',
  dataEncryption: true,
  operationLog: true
})

const basicRules: FormRules = {
  name: [
    { required: true, message: '请输入家庭名称', trigger: 'blur' },
    { min: 2, max: 20, message: '家庭名称长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadFamilySettings()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadFamilySettings = async () => {
  // 模拟加载家庭设置数据
  Object.assign(basicForm, {
    name: '张家大院',
    description: '我们是一个温馨的大家庭，关爱彼此的健康',
    address: '北京市朝阳区xxx街道xxx号',
    phone: '138****8888',
    avatar: ''
  })
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  basicForm.avatar = response.url
}

const handleLogoutAllDevices = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要登出所有设备吗？这将强制所有设备重新登录。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('已登出所有设备')
  } catch (error) {
    // 用户取消
  }
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  if (!basicFormRef.value) return

  try {
    await basicFormRef.value.validate()
    loading.value = true

    // 模拟保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('家庭设置保存成功！')
    emit('success')
    visible.value = false
  } catch (error) {
    console.error('保存设置失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.settings-content {
  max-height: 600px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 24px;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid var(--theme-border-light);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-name {
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.avatar-uploader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed var(--theme-border-light);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: var(--color-primary);
}

.avatar {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: var(--theme-text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
